# 商家标签管理前端集成示例

## 概述

本文档提供商家标签管理功能的前端集成示例，包括标签列表、添加、编辑、删除等操作的实现。

## JavaScript 服务类

```javascript
/**
 * 商家标签管理服务
 */
class StoreTagService {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }

    /**
     * 获取标签分页列表
     */
    async getTagList(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseURL}/user/store/tag?${queryString}`);
        return response.json();
    }

    /**
     * 获取标签详情
     */
    async getTagDetail(id) {
        const response = await fetch(`${this.baseURL}/user/store/tag/${id}`);
        return response.json();
    }

    /**
     * 添加标签
     */
    async addTag(tagData) {
        const response = await fetch(`${this.baseURL}/user/store/tag`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(tagData)
        });
        return response.json();
    }

    /**
     * 编辑标签
     */
    async editTag(tagData) {
        const response = await fetch(`${this.baseURL}/user/store/tag`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(tagData)
        });
        return response.json();
    }

    /**
     * 删除标签
     */
    async deleteTag(id) {
        const response = await fetch(`${this.baseURL}/user/store/tag/${id}`, {
            method: 'DELETE'
        });
        return response.json();
    }

    /**
     * 批量删除标签
     */
    async batchDeleteTags(ids) {
        const queryString = ids.map(id => `ids=${id}`).join('&');
        const response = await fetch(`${this.baseURL}/user/store/tag/batch?${queryString}`, {
            method: 'DELETE'
        });
        return response.json();
    }

    /**
     * 更新标签状态
     */
    async updateTagStatus(id, enabled) {
        const response = await fetch(`${this.baseURL}/user/store/tag/${id}/status?enabled=${enabled}`, {
            method: 'PUT'
        });
        return response.json();
    }

    /**
     * 获取启用的标签列表
     */
    async getEnabledTags() {
        const response = await fetch(`${this.baseURL}/user/store/tag/enabled`);
        return response.json();
    }

    /**
     * 根据类型获取标签
     */
    async getTagsByType(tagType) {
        const response = await fetch(`${this.baseURL}/user/store/tag/type/${tagType}`);
        return response.json();
    }

    /**
     * 检查标签名称是否存在
     */
    async checkTagNameExists(tagName, excludeId = null) {
        const params = new URLSearchParams({ tagName });
        if (excludeId) params.append('excludeId', excludeId);
        
        const response = await fetch(`${this.baseURL}/user/store/tag/check?${params}`);
        return response.json();
    }

    /**
     * 获取标签类型枚举
     */
    async getTagTypes() {
        const response = await fetch(`${this.baseURL}/user/store/tag/types`);
        return response.json();
    }
}
```

## React 组件示例

### 标签管理主页面

```jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Select, Switch, ColorPicker, message } from 'antd';

const StoreTagManagement = () => {
    const [tags, setTags] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingTag, setEditingTag] = useState(null);
    const [form] = Form.useForm();
    const [tagTypes, setTagTypes] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    const storeTagService = new StoreTagService('/api');

    useEffect(() => {
        loadTags();
        loadTagTypes();
    }, []);

    const loadTags = async (page = 1, size = 10) => {
        setLoading(true);
        try {
            const response = await storeTagService.getTagList({
                page,
                size
            });
            
            if (response.success) {
                setTags(response.result.records);
                setPagination({
                    current: response.result.current,
                    pageSize: response.result.size,
                    total: response.result.total
                });
            }
        } catch (error) {
            message.error('加载标签列表失败');
        } finally {
            setLoading(false);
        }
    };

    const loadTagTypes = async () => {
        try {
            const response = await storeTagService.getTagTypes();
            if (response.success) {
                setTagTypes(response.result.map(type => ({
                    value: type.code,
                    label: type.name
                })));
            }
        } catch (error) {
            console.error('加载标签类型失败', error);
        }
    };

    const handleAdd = () => {
        setEditingTag(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = (record) => {
        setEditingTag(record);
        form.setFieldsValue(record);
        setModalVisible(true);
    };

    const handleDelete = async (id) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这个标签吗？',
            onOk: async () => {
                try {
                    const response = await storeTagService.deleteTag(id);
                    if (response.success) {
                        message.success('删除成功');
                        loadTags(pagination.current, pagination.pageSize);
                    } else {
                        message.error(response.message || '删除失败');
                    }
                } catch (error) {
                    message.error('删除失败');
                }
            }
        });
    };

    const handleStatusChange = async (id, enabled) => {
        try {
            const response = await storeTagService.updateTagStatus(id, enabled);
            if (response.success) {
                message.success('状态更新成功');
                loadTags(pagination.current, pagination.pageSize);
            } else {
                message.error(response.message || '状态更新失败');
            }
        } catch (error) {
            message.error('状态更新失败');
        }
    };

    const handleSubmit = async (values) => {
        try {
            let response;
            if (editingTag) {
                response = await storeTagService.editTag({
                    ...values,
                    id: editingTag.id
                });
            } else {
                response = await storeTagService.addTag(values);
            }

            if (response.success) {
                message.success(editingTag ? '编辑成功' : '添加成功');
                setModalVisible(false);
                loadTags(pagination.current, pagination.pageSize);
            } else {
                message.error(response.message || '操作失败');
            }
        } catch (error) {
            message.error('操作失败');
        }
    };

    const columns = [
        {
            title: '标签名称',
            dataIndex: 'tagName',
            key: 'tagName',
            render: (text, record) => (
                <span style={{ 
                    color: record.tagColor,
                    fontWeight: 'bold'
                }}>
                    {text}
                </span>
            )
        },
        {
            title: '标签描述',
            dataIndex: 'tagDescription',
            key: 'tagDescription'
        },
        {
            title: '标签类型',
            dataIndex: 'tagTypeName',
            key: 'tagTypeName'
        },
        {
            title: '排序',
            dataIndex: 'sortOrder',
            key: 'sortOrder'
        },
        {
            title: '使用店铺数',
            dataIndex: 'storeCount',
            key: 'storeCount'
        },
        {
            title: '状态',
            dataIndex: 'enabled',
            key: 'enabled',
            render: (enabled, record) => (
                <Switch
                    checked={enabled}
                    onChange={(checked) => handleStatusChange(record.id, checked)}
                />
            )
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <div>
                    <Button 
                        type="link" 
                        onClick={() => handleEdit(record)}
                    >
                        编辑
                    </Button>
                    <Button 
                        type="link" 
                        danger 
                        onClick={() => handleDelete(record.id)}
                    >
                        删除
                    </Button>
                </div>
            )
        }
    ];

    return (
        <div className="store-tag-management">
            <div className="header">
                <h2>商家标签管理</h2>
                <Button type="primary" onClick={handleAdd}>
                    添加标签
                </Button>
            </div>

            <Table
                columns={columns}
                dataSource={tags}
                loading={loading}
                rowKey="id"
                pagination={{
                    ...pagination,
                    onChange: (page, size) => loadTags(page, size)
                }}
            />

            <Modal
                title={editingTag ? '编辑标签' : '添加标签'}
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                onOk={() => form.submit()}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                >
                    <Form.Item
                        name="tagName"
                        label="标签名称"
                        rules={[{ required: true, message: '请输入标签名称' }]}
                    >
                        <Input placeholder="请输入标签名称" />
                    </Form.Item>

                    <Form.Item
                        name="tagDescription"
                        label="标签描述"
                    >
                        <Input.TextArea placeholder="请输入标签描述" />
                    </Form.Item>

                    <Form.Item
                        name="tagType"
                        label="标签类型"
                    >
                        <Select placeholder="请选择标签类型">
                            {tagTypes.map(type => (
                                <Select.Option key={type.value} value={type.value}>
                                    {type.label}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="tagColor"
                        label="标签颜色"
                    >
                        <ColorPicker />
                    </Form.Item>

                    <Form.Item
                        name="sortOrder"
                        label="排序"
                        rules={[{ required: true, message: '请输入排序值' }]}
                    >
                        <Input type="number" placeholder="数值越小排序越靠前" />
                    </Form.Item>

                    <Form.Item
                        name="enabled"
                        label="是否启用"
                        valuePropName="checked"
                        initialValue={true}
                    >
                        <Switch />
                    </Form.Item>

                    <Form.Item
                        name="remark"
                        label="备注"
                    >
                        <Input.TextArea placeholder="请输入备注" />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default StoreTagManagement;
```

### 标签选择器组件

```jsx
import React, { useState, useEffect } from 'react';
import { Select, Tag } from 'antd';

const TagSelector = ({ value, onChange, multiple = false, tagType = null }) => {
    const [tags, setTags] = useState([]);
    const [loading, setLoading] = useState(false);

    const storeTagService = new StoreTagService('/api');

    useEffect(() => {
        loadTags();
    }, [tagType]);

    const loadTags = async () => {
        setLoading(true);
        try {
            let response;
            if (tagType) {
                response = await storeTagService.getTagsByType(tagType);
            } else {
                response = await storeTagService.getEnabledTags();
            }

            if (response.success) {
                setTags(response.result);
            }
        } catch (error) {
            console.error('加载标签失败', error);
        } finally {
            setLoading(false);
        }
    };

    const tagRender = (props) => {
        const { label, value, closable, onClose } = props;
        const tag = tags.find(t => t.id === value);
        
        return (
            <Tag
                color={tag?.tagColor}
                closable={closable}
                onClose={onClose}
                style={{ marginRight: 3 }}
            >
                {label}
            </Tag>
        );
    };

    return (
        <Select
            mode={multiple ? 'multiple' : undefined}
            value={value}
            onChange={onChange}
            loading={loading}
            placeholder="请选择标签"
            tagRender={multiple ? tagRender : undefined}
            style={{ width: '100%' }}
        >
            {tags.map(tag => (
                <Select.Option key={tag.id} value={tag.id}>
                    <Tag color={tag.tagColor} style={{ margin: 0 }}>
                        {tag.tagName}
                    </Tag>
                </Select.Option>
            ))}
        </Select>
    );
};

export default TagSelector;
```

## Vue 组件示例

```vue
<template>
  <div class="store-tag-management">
    <div class="header">
      <h2>商家标签管理</h2>
      <el-button type="primary" @click="handleAdd">添加标签</el-button>
    </div>

    <el-table
      :data="tags"
      v-loading="loading"
      style="width: 100%"
    >
      <el-table-column prop="tagName" label="标签名称">
        <template #default="{ row }">
          <span :style="{ color: row.tagColor, fontWeight: 'bold' }">
            {{ row.tagName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="tagDescription" label="标签描述" />
      <el-table-column prop="tagTypeName" label="标签类型" />
      <el-table-column prop="sortOrder" label="排序" />
      <el-table-column prop="storeCount" label="使用店铺数" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-switch
            v-model="row.enabled"
            @change="handleStatusChange(row.id, row.enabled)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      @current-change="loadTags"
      @size-change="loadTags"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="editingTag ? '编辑标签' : '添加标签'"
      v-model="dialogVisible"
    >
      <el-form :model="form" label-width="100px">
        <el-form-item label="标签名称" required>
          <el-input v-model="form.tagName" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="标签描述">
          <el-input
            v-model="form.tagDescription"
            type="textarea"
            placeholder="请输入标签描述"
          />
        </el-form-item>
        <el-form-item label="标签类型">
          <el-select v-model="form.tagType" placeholder="请选择标签类型">
            <el-option
              v-for="type in tagTypes"
              :key="type.code"
              :label="type.name"
              :value="type.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签颜色">
          <el-color-picker v-model="form.tagColor" />
        </el-form-item>
        <el-form-item label="排序" required>
          <el-input-number
            v-model="form.sortOrder"
            placeholder="数值越小排序越靠前"
          />
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="form.enabled" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StoreTagManagement',
  data() {
    return {
      tags: [],
      loading: false,
      dialogVisible: false,
      editingTag: null,
      tagTypes: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      form: {
        tagName: '',
        tagDescription: '',
        tagType: '',
        tagColor: '',
        sortOrder: 0,
        enabled: true,
        remark: ''
      }
    };
  },
  mounted() {
    this.loadTags();
    this.loadTagTypes();
  },
  methods: {
    async loadTags(page = 1, size = 10) {
      this.loading = true;
      try {
        const storeTagService = new StoreTagService('/api');
        const response = await storeTagService.getTagList({ page, size });
        
        if (response.success) {
          this.tags = response.result.records;
          this.pagination = {
            current: response.result.current,
            pageSize: response.result.size,
            total: response.result.total
          };
        }
      } catch (error) {
        this.$message.error('加载标签列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    async loadTagTypes() {
      try {
        const storeTagService = new StoreTagService('/api');
        const response = await storeTagService.getTagTypes();
        if (response.success) {
          this.tagTypes = response.result;
        }
      } catch (error) {
        console.error('加载标签类型失败', error);
      }
    },
    
    handleAdd() {
      this.editingTag = null;
      this.form = {
        tagName: '',
        tagDescription: '',
        tagType: '',
        tagColor: '',
        sortOrder: 0,
        enabled: true,
        remark: ''
      };
      this.dialogVisible = true;
    },
    
    handleEdit(tag) {
      this.editingTag = tag;
      this.form = { ...tag };
      this.dialogVisible = true;
    },
    
    async handleSubmit() {
      try {
        const storeTagService = new StoreTagService('/api');
        let response;
        
        if (this.editingTag) {
          response = await storeTagService.editTag({
            ...this.form,
            id: this.editingTag.id
          });
        } else {
          response = await storeTagService.addTag(this.form);
        }
        
        if (response.success) {
          this.$message.success(this.editingTag ? '编辑成功' : '添加成功');
          this.dialogVisible = false;
          this.loadTags(this.pagination.current, this.pagination.pageSize);
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        this.$message.error('操作失败');
      }
    }
  }
};
</script>
```

## 使用说明

1. **引入服务类**：将 `StoreTagService` 类引入到项目中
2. **配置API地址**：根据实际情况配置 `baseURL`
3. **权限控制**：确保用户有相应的标签管理权限
4. **错误处理**：添加适当的错误处理和用户提示
5. **样式定制**：根据项目UI规范调整组件样式
