package cn.lili.modules.store;

import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.dto.StoreTagDTO;
import cn.lili.modules.store.entity.dto.StoreTagSearchParams;
import cn.lili.modules.store.entity.enums.StoreTagTypeEnum;
import cn.lili.modules.store.entity.vos.StoreTagVO;
import cn.lili.modules.store.service.StoreTagService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商家标签服务测试
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StoreTagServiceTest {

    @Autowired
    private StoreTagService storeTagService;

    @Test
    public void testAddStoreTag() {
        // 测试添加标签
        StoreTagDTO storeTagDTO = new StoreTagDTO();
        storeTagDTO.setTagName("测试标签");
        storeTagDTO.setTagDescription("这是一个测试标签");
        storeTagDTO.setTagColor("#ff0000");
        storeTagDTO.setTagType(StoreTagTypeEnum.QUALITY.getCode());
        storeTagDTO.setSortOrder(BigDecimal.valueOf(10));
        storeTagDTO.setEnabled(true);
        storeTagDTO.setRemark("测试用标签");

        StoreTag result = storeTagService.addStoreTag(storeTagDTO);

        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("测试标签", result.getTagName());
        assertEquals("这是一个测试标签", result.getTagDescription());
        assertEquals("#ff0000", result.getTagColor());
        assertEquals(StoreTagTypeEnum.QUALITY.getCode(), result.getTagType());
        assertTrue(result.getEnabled());
    }

    @Test
    public void testEditStoreTag() {
        // 先添加一个标签
        StoreTagDTO addDTO = new StoreTagDTO();
        addDTO.setTagName("编辑前标签");
        addDTO.setTagDescription("编辑前描述");
        addDTO.setSortOrder(BigDecimal.valueOf(5));
        addDTO.setEnabled(true);

        StoreTag addedTag = storeTagService.addStoreTag(addDTO);

        // 编辑标签
        StoreTagDTO editDTO = new StoreTagDTO();
        editDTO.setId(addedTag.getId());
        editDTO.setTagName("编辑后标签");
        editDTO.setTagDescription("编辑后描述");
        editDTO.setTagColor("#00ff00");
        editDTO.setSortOrder(BigDecimal.valueOf(15));
        editDTO.setEnabled(false);

        StoreTag result = storeTagService.editStoreTag(editDTO);

        assertNotNull(result);
        assertEquals("编辑后标签", result.getTagName());
        assertEquals("编辑后描述", result.getTagDescription());
        assertEquals("#00ff00", result.getTagColor());
        assertFalse(result.getEnabled());
    }

    @Test
    public void testDeleteStoreTag() {
        // 先添加一个标签
        StoreTagDTO storeTagDTO = new StoreTagDTO();
        storeTagDTO.setTagName("待删除标签");
        storeTagDTO.setTagDescription("这个标签将被删除");
        storeTagDTO.setSortOrder(BigDecimal.valueOf(20));
        storeTagDTO.setEnabled(true);

        StoreTag addedTag = storeTagService.addStoreTag(storeTagDTO);

        // 删除标签
        Boolean result = storeTagService.deleteStoreTag(addedTag.getId());

        assertTrue(result);

        // 验证标签已被删除
        StoreTag deletedTag = storeTagService.getById(addedTag.getId());
        assertNull(deletedTag);
    }

    @Test
    public void testGetStoreTagPage() {
        // 添加几个测试标签
        for (int i = 1; i <= 3; i++) {
            StoreTagDTO storeTagDTO = new StoreTagDTO();
            storeTagDTO.setTagName("分页测试标签" + i);
            storeTagDTO.setTagDescription("分页测试描述" + i);
            storeTagDTO.setSortOrder(BigDecimal.valueOf(i));
            storeTagDTO.setEnabled(i % 2 == 1); // 奇数启用，偶数禁用
            storeTagService.addStoreTag(storeTagDTO);
        }

        // 测试分页查询
        StoreTagSearchParams searchParams = new StoreTagSearchParams();
        searchParams.setPageNumber(1);
        searchParams.setPageSize(10);

        Page<StoreTagVO> result = storeTagService.getStoreTagPage(searchParams);

        assertNotNull(result);
        assertTrue(result.getTotal() >= 3);
    }

    @Test
    public void testGetEnabledTags() {
        // 添加启用和禁用的标签
        StoreTagDTO enabledTag = new StoreTagDTO();
        enabledTag.setTagName("启用标签");
        enabledTag.setTagDescription("这是启用的标签");
        enabledTag.setSortOrder(BigDecimal.valueOf(1));
        enabledTag.setEnabled(true);
        storeTagService.addStoreTag(enabledTag);

        StoreTagDTO disabledTag = new StoreTagDTO();
        disabledTag.setTagName("禁用标签");
        disabledTag.setTagDescription("这是禁用的标签");
        disabledTag.setSortOrder(BigDecimal.valueOf(2));
        disabledTag.setEnabled(false);
        storeTagService.addStoreTag(disabledTag);

        // 获取启用的标签
        List<StoreTag> enabledTags = storeTagService.getEnabledTags();

        assertNotNull(enabledTags);
        // 验证返回的标签都是启用状态
        for (StoreTag tag : enabledTags) {
            assertTrue(tag.getEnabled());
        }
    }

    @Test
    public void testGetTagsByType() {
        // 添加不同类型的标签
        StoreTagDTO qualityTag = new StoreTagDTO();
        qualityTag.setTagName("优质标签");
        qualityTag.setTagType(StoreTagTypeEnum.QUALITY.getCode());
        qualityTag.setSortOrder(BigDecimal.valueOf(1));
        qualityTag.setEnabled(true);
        storeTagService.addStoreTag(qualityTag);

        StoreTagDTO recommendTag = new StoreTagDTO();
        recommendTag.setTagName("推荐标签");
        recommendTag.setTagType(StoreTagTypeEnum.RECOMMEND.getCode());
        recommendTag.setSortOrder(BigDecimal.valueOf(2));
        recommendTag.setEnabled(true);
        storeTagService.addStoreTag(recommendTag);

        // 获取指定类型的标签
        List<StoreTag> qualityTags = storeTagService.getTagsByType(StoreTagTypeEnum.QUALITY.getCode());

        assertNotNull(qualityTags);
        // 验证返回的标签都是指定类型
        for (StoreTag tag : qualityTags) {
            assertEquals(StoreTagTypeEnum.QUALITY.getCode(), tag.getTagType());
        }
    }

    @Test
    public void testCheckTagNameExists() {
        // 添加一个标签
        StoreTagDTO storeTagDTO = new StoreTagDTO();
        storeTagDTO.setTagName("重复检查标签");
        storeTagDTO.setTagDescription("用于检查重复的标签");
        storeTagDTO.setSortOrder(BigDecimal.valueOf(1));
        storeTagDTO.setEnabled(true);

        StoreTag addedTag = storeTagService.addStoreTag(storeTagDTO);

        // 检查标签名称是否存在
        Boolean exists = storeTagService.checkTagNameExists("重复检查标签", null);
        assertTrue(exists);

        // 检查不存在的标签名称
        Boolean notExists = storeTagService.checkTagNameExists("不存在的标签", null);
        assertFalse(notExists);

        // 检查排除自身的情况
        Boolean excludeSelf = storeTagService.checkTagNameExists("重复检查标签", addedTag.getId());
        assertFalse(excludeSelf);
    }

    @Test
    public void testUpdateTagStatus() {
        // 添加一个启用的标签
        StoreTagDTO storeTagDTO = new StoreTagDTO();
        storeTagDTO.setTagName("状态测试标签");
        storeTagDTO.setTagDescription("用于测试状态更新");
        storeTagDTO.setSortOrder(BigDecimal.valueOf(1));
        storeTagDTO.setEnabled(true);

        StoreTag addedTag = storeTagService.addStoreTag(storeTagDTO);

        // 禁用标签
        Boolean result = storeTagService.updateTagStatus(addedTag.getId(), false);
        assertTrue(result);

        // 验证状态已更新
        StoreTag updatedTag = storeTagService.getById(addedTag.getId());
        assertFalse(updatedTag.getEnabled());
    }

    @Test
    public void testBatchUpdateTagStatus() {
        // 添加多个标签
        StoreTag tag1 = storeTagService.addStoreTag(createTestTag("批量状态测试1", 1));
        StoreTag tag2 = storeTagService.addStoreTag(createTestTag("批量状态测试2", 2));

        List<String> ids = Arrays.asList(tag1.getId(), tag2.getId());

        // 批量禁用
        Boolean result = storeTagService.batchUpdateTagStatus(ids, false);
        assertTrue(result);

        // 验证状态已更新
        StoreTag updatedTag1 = storeTagService.getById(tag1.getId());
        StoreTag updatedTag2 = storeTagService.getById(tag2.getId());
        assertFalse(updatedTag1.getEnabled());
        assertFalse(updatedTag2.getEnabled());
    }

    @Test
    public void testInitDefaultTags() {
        // 初始化默认标签
        storeTagService.initDefaultTags();

        // 验证默认标签已创建
        List<StoreTag> allTags = storeTagService.list();
        assertTrue(allTags.size() >= StoreTagTypeEnum.values().length);

        // 验证每种类型的标签都存在
        for (StoreTagTypeEnum tagType : StoreTagTypeEnum.values()) {
            List<StoreTag> tagsOfType = storeTagService.getTagsByType(tagType.getCode());
            assertFalse(tagsOfType.isEmpty());
        }
    }

    /**
     * 创建测试标签的辅助方法
     */
    private StoreTagDTO createTestTag(String name, int order) {
        StoreTagDTO storeTagDTO = new StoreTagDTO();
        storeTagDTO.setTagName(name);
        storeTagDTO.setTagDescription(name + "描述");
        storeTagDTO.setSortOrder(BigDecimal.valueOf(order));
        storeTagDTO.setEnabled(true);
        return storeTagDTO;
    }
}
