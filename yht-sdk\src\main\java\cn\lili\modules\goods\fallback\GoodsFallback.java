package cn.lili.modules.goods.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.BatchUpdateGoodsDTO;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 15:29
 * @description: 商品服务Fallback
 */
public class GoodsFallback implements GoodsClient {


    @Override
    public GoodsSku getGoodsSkuByIdFromCache(String skuId) {
        throw new ServiceException();
    }

    @Override
    public GoodsSku getGoodsSkuById(String skuId) {
        throw new ServiceException();
    }

    @Override
    public GoodsSku getCanPromotionGoodsSkuByIdFromEs(String skuId) {
        throw new ServiceException();
    }

    @Override
    public void updateStoreDetail(Store store) {
        throw new ServiceException();
    }

    @Override
    public void batchUpdateGoods(BatchUpdateGoodsDTO batchUpdateGoodsDTO) {
        throw new ServiceException();
    }

    @Override
    public Long count(String storeId) {
        throw new ServiceException();
    }

    @Override
    public Integer getStock(String skuId) {
        throw new ServiceException();
    }

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @Override
    public void addGoodsCommentNum(Integer commentNum, String goodsId) {
        throw new ServiceException();
    }

    @Override
    public Goods getById(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public List<Goods> queryListByParams(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }


    @Override
    public List<GoodsSku> getGoodsSkuByList(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public void updateGoodsBuyCount(List<GoodsCompleteMessage> goodsCompleteMessageList) {
        throw new ServiceException();
    }

    @Override
    public void updateGoodsSku(GoodsSku goodsSku) {
        throw new ServiceException();
    }

    @Override
    public Goods getGoodsByParams(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public Long countSkuNum(String storeId) {
        throw new ServiceException();
    }

    @Override
    public void addSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void editSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void deleteGoods(List<String> goodsIds) {
        throw new ServiceException();
    }

    @Override
    public void addSupplierGoods(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public void deleteSupplierGoods(List<String> goodsIds) {
        throw new ServiceException();
    }

    @Override
    public void editProxyGoods(ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void syncStock(List<String> goodsId) {
        throw new ServiceException();
    }

    @Override
    public void syncGoodsSkuCommentCount(String skuId) {
        throw new ServiceException();
    }

    @Override
    public Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public FreightTemplateVO getGoodsTemplate(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public List<GoodsSku> getGoodsSkuByIdFromCache(List<String> ids) {
        throw new ServiceException();
    }

    @Override
    public List<String> getAllSkuIds() {
        throw new ServiceException();
    }
    
    @Override
    public Double getSkuCostPrice(String skuId) {
        throw new ServiceException();
    }

    @Override
    public Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore(StoreRankStatisticsParams params) {
        throw new ServiceException();
    }

    @Override
    public List<Goods> countGoodsNum(String storeId) {
        throw new ServiceException();
    }

    @Override
    public Double getRealShootRate(String storeId) {
        // 返回0.0而不是抛出异常，避免影响应用启动
        return 0.0;
    }
}
