package cn.lili.modules.store.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.dto.StoreTagDTO;
import cn.lili.modules.store.entity.dto.StoreTagSearchParams;
import cn.lili.modules.store.entity.enums.StoreTagTypeEnum;
import cn.lili.modules.store.entity.vos.StoreTagVO;
import cn.lili.modules.store.mapper.StoreTagMapper;
import cn.lili.modules.store.service.StoreTagService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家标签业务层实现
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreTagServiceImpl extends ServiceImpl<StoreTagMapper, StoreTag> implements StoreTagService {

    @Override
    public Page<StoreTagVO> getStoreTagPage(StoreTagSearchParams searchParams) {
        return this.baseMapper.getStoreTagPage(PageUtil.initPage(searchParams), searchParams.queryWrapper());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreTag addStoreTag(StoreTagDTO storeTagDTO) {
        storeTagDTO.validateParams();

        // 检查标签名称是否存在
        if (checkTagNameExists(storeTagDTO.getTagName(), null)) {
            throw new ServiceException(ResultCode.STORE_TAG_NAME_EXIST);
        }

        StoreTag storeTag = new StoreTag();
        BeanUtil.copyProperties(storeTagDTO, storeTag);

        // 设置默认颜色
        if (CharSequenceUtil.isEmpty(storeTag.getTagColor()) && CharSequenceUtil.isNotEmpty(storeTag.getTagType())) {
            StoreTagTypeEnum tagTypeEnum = StoreTagTypeEnum.getByCode(storeTag.getTagType());
            if (tagTypeEnum != null) {
                storeTag.setTagColor(tagTypeEnum.getDefaultColor());
            }
        }

        this.save(storeTag);
        return storeTag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreTag editStoreTag(StoreTagDTO storeTagDTO) {
        if (CharSequenceUtil.isEmpty(storeTagDTO.getId())) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        storeTagDTO.validateParams();

        // 检查标签是否存在
        StoreTag existTag = this.getById(storeTagDTO.getId());
        if (existTag == null) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        // 检查标签名称是否存在（排除当前标签）
        if (checkTagNameExists(storeTagDTO.getTagName(), storeTagDTO.getId())) {
            throw new ServiceException(ResultCode.STORE_TAG_NAME_EXIST);
        }

        BeanUtil.copyProperties(storeTagDTO, existTag);
        this.updateById(existTag);
        return existTag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteStoreTag(String id) {
        if (CharSequenceUtil.isEmpty(id)) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        StoreTag storeTag = this.getById(id);
        if (storeTag == null) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        // 检查是否有店铺在使用该标签
        Long storeCount = getStoreCountByTag(storeTag.getTagName());
        if (storeCount > 0) {
            throw new ServiceException("该标签正在被 " + storeCount + " 个店铺使用，无法删除");
        }

        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteStoreTag(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("请选择要删除的标签");
        }

        // 检查每个标签是否可以删除
        for (String id : ids) {
            StoreTag storeTag = this.getById(id);
            if (storeTag != null) {
                Long storeCount = getStoreCountByTag(storeTag.getTagName());
                if (storeCount > 0) {
                    throw new ServiceException("标签 \"" + storeTag.getTagName() + "\" 正在被 " + storeCount + " 个店铺使用，无法删除");
                }
            }
        }

        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTagStatus(String id, Boolean enabled) {
        if (CharSequenceUtil.isEmpty(id)) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        LambdaUpdateWrapper<StoreTag> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StoreTag::getId, id);
        updateWrapper.set(StoreTag::getEnabled, enabled);

        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateTagStatus(List<String> ids, Boolean enabled) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("请选择要操作的标签");
        }

        return this.baseMapper.batchUpdateStatus(ids, enabled) > 0;
    }

    @Override
    public List<StoreTag> getEnabledTags() {
        return this.baseMapper.getEnabledTags();
    }

    @Override
    public List<StoreTag> getTagsByType(String tagType) {
        if (CharSequenceUtil.isEmpty(tagType)) {
            return getEnabledTags();
        }
        return this.baseMapper.getTagsByType(tagType);
    }

    @Override
    public Boolean checkTagNameExists(String tagName, String excludeId) {
        if (CharSequenceUtil.isEmpty(tagName)) {
            return false;
        }
        return this.baseMapper.checkTagNameExists(tagName, excludeId) > 0;
    }

    @Override
    public StoreTagVO getStoreTagDetail(String id) {
        if (CharSequenceUtil.isEmpty(id)) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        StoreTag storeTag = this.getById(id);
        if (storeTag == null) {
            throw new ServiceException(ResultCode.STORE_TAG_NOT_EXIST);
        }

        StoreTagVO storeTagVO = new StoreTagVO(storeTag);
        storeTagVO.setStoreCount(getStoreCountByTag(storeTag.getTagName()));
        return storeTagVO;
    }

    @Override
    public Long getStoreCountByTag(String tagName) {
        if (CharSequenceUtil.isEmpty(tagName)) {
            return 0L;
        }
        return this.baseMapper.getStoreCountByTag(tagName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDefaultTags() {
        // 检查是否已经初始化过
        LambdaQueryWrapper<StoreTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreTag::getTagType, StoreTagTypeEnum.QUALITY.getCode());
        if (this.count(queryWrapper) > 0) {
            log.info("默认标签已存在，跳过初始化");
            return;
        }

        // 初始化默认标签
        StoreTagTypeEnum[] tagTypes = StoreTagTypeEnum.values();
        for (int i = 0; i < tagTypes.length; i++) {
            StoreTagTypeEnum tagType = tagTypes[i];
            StoreTag storeTag = new StoreTag();
            storeTag.setTagName(tagType.getName());
            storeTag.setTagDescription(tagType.getName() + "标签");
            storeTag.setTagColor(tagType.getDefaultColor());
            storeTag.setTagType(tagType.getCode());
            storeTag.setSortOrder(BigDecimal.valueOf(i + 1));
            storeTag.setEnabled(true);
            storeTag.setRemark("系统默认标签");
            this.save(storeTag);
        }

        log.info("默认标签初始化完成");
    }
}
