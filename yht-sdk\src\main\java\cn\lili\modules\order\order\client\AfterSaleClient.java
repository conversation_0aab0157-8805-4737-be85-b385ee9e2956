package cn.lili.modules.order.order.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.fallback.AfterSaleFallback;
import cn.lili.modules.store.entity.dos.Store;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
@FeignClient(name = ServiceConstant.ORDER_SERVICE, contextId = "after-sale", fallback = AfterSaleFallback.class)
public interface AfterSaleClient {

    @PutMapping("/feign/after-sale/store/edit")
    void editAfterSaleStoreInfo(@RequestBody Store store);

    @PutMapping("/feign/after-sale/user/edit")
    void editAfterSaleUserInfo(@RequestBody User user);


    /**
     * 根据订单编号查询售后服务单
     *
     * @param orderSn 订单编号
     * @return 售后服务单
     */
    @GetMapping("/feign/after-sale/queryByOrderSn")
    List<AfterSale> queryByOrderSn(@RequestParam String orderSn);

    /**
     * 根据订单编号查询是否存在售后服务单
     *
     * @param orderSn 订单编号
     * @return 是否售后
     */
    @GetMapping("/feign/after-sale/isAfterSale")
    Boolean isAfterSale(@RequestParam String orderSn);

    /**
     * 获取店铺退货成功率
     *
     * @param storeId 店铺ID
     * @return 退货成功率（百分比）
     */
    @GetMapping("/feign/after-sale/getReturnSuccessRate/{storeId}")
    Double getReturnSuccessRate(@PathVariable String storeId);
}
