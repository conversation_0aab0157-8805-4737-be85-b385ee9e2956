package cn.lili.modules.goods.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.ProductSearchParams;
import cn.lili.modules.goods.entity.vos.GoodsStatusCountVO;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商品业务层
 *
 * <AUTHOR>
 * @since 2020-02-23 16:18:56
 */
public interface GoodsService extends IService<Goods> {


    /**
     * 根据品牌获取商品
     *
     * @param brandIds 品牌ids
     */
    List<Goods> getByBrandIds(List<String> brandIds);

    /**
     * 更新商品参数
     *
     * @param goodsId 商品id
     * @param params  商品参数
     */
    void updateGoodsParams(String goodsId, String params);

    /**
     * 获取某分类下的商品数量
     *
     * @param categoryId 分类ID
     * @return 商品数量
     */
    long getGoodsCountByCategory(String categoryId);

    /**
     * 修改商品
     *
     * @param goods 商品
     */
    void updateGoods(Goods goods);


    /**
     * 商品查询
     *
     * @param goodsSearchParams 查询参数
     * @return 商品分页
     */
    Page<Goods> queryByParams(GoodsSearchParams goodsSearchParams);

    /**
     * 统计商品状态数量
     * @param goodsSearchParams 查询参数
     * @return 商品状态数量
     */
    GoodsStatusCountVO countGoodsStatus(GoodsSearchParams goodsSearchParams);


    /**
     * 商品查询
     *
     * @param goodsSearchParams 查询参数
     * @return 商品信息
     */
    List<Goods> queryListByParams(GoodsSearchParams goodsSearchParams);


    /**
     * 商品id查询
     *
     * @param goodsSearchParams 查询参数
     * @return 商品信息
     */
    List<String> queryListIdByParams(GoodsSearchParams goodsSearchParams);

    /**
     * 商品查询
     *
     * @param goodsSearchParams 查询参数
     * @return 商品信息
     */
    List<String> queryListStoreIdByParams(GoodsSearchParams goodsSearchParams);

    /**
     * 设置商品运费模板
     *
     * @param goodsIds   商品列表
     * @param templateId 运费模板ID
     * @return 操作结果
     */
    Boolean freight(List<String> goodsIds, String templateId);

    /**
     * 修改商品库存数量
     *
     * @param goodsId  商品ID
     * @param quantity 库存数量
     */
    void updateStock(String goodsId, Integer quantity);

    /**
     * 根据全部sku信息更新商品库存数量
     *
     * @param goodsSkus 全部商品sku信息
     */
    void updateStock(List<GoodsSku> goodsSkus);


    /**
     * 同步最新商品库存
     *
     * @param goodsId 商品id
     */
    void syncStock(String goodsId);

    /**
     * 更新商品的购买数量
     *
     * @param goodsId  商品ID
     * @param buyCount 购买数量
     */
    void updateGoodsBuyCount(String goodsId, Integer buyCount);

    /**
     * 更新商品的购买数量
     * @param goodsCompleteMessageList 购买数量
     */
    void updateGoodsBuyCount(List<GoodsCompleteMessage> goodsCompleteMessageList);
    /**
     * 统计店铺的商品数量
     *
     * @param storeId 店铺id
     * @return 商品数量
     */
    long countStoreGoodsNum(String storeId);

    /**
     * 手动调整商品的评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    void addGoodsCommentNum(Integer commentNum, String goodsId);

    /**
     * 根据参数查询商品信息
     *
     * @param goodsSearchParams 查询参数
     * @return 商品信息
     */
    Goods getGoodsByParams(GoodsSearchParams goodsSearchParams);

    /**
     * 统计商品数量
     * @param goodsSearchParams 查询参数
     * @return 商品数量
     */
    long countGoodsNum(GoodsSearchParams goodsSearchParams);

    /**
     * 资源包下载次数
     * @param storeId
     * @return
     */
    long getImageDownloadCount(String storeId);

    /**
     * 分页查询商品信息
     *
     * @param searchParams 查询参数
     * @return 商品sku信息
     */
    Page<Goods> getGoodsByPage(GoodsSearchParams searchParams);

    /**
     * 搜同款商品
     * @param productSearchParams
     * @return
     */
    Page<EsGoodsIndex> searchProduct(ProductSearchParams productSearchParams);

    /**
     * 商品图片下载次数排行
     * @param params
     * @return
     */
    Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore (StoreRankStatisticsParams params);

    /**
     * 商品总数
     * @param storeId
     * @return
     */
    List<Goods> countGoodsNum(String storeId);

    /**
     * 获取店铺商品实拍率
     *
     * @param storeId 店铺ID
     * @return 商品实拍率（百分比）
     */
    Double getRealShootRate(String storeId);

    /**
     * 获取店铺商品质量合格率
     *
     * @param storeId 店铺ID
     * @return 商品质量合格率（百分比）
     */
    Double getQualityPassRate(String storeId);
}