# ES商品搜索中使用 isBackMoney 字段示例

## 概述

本文档展示如何在商品搜索功能中使用新增的 `isBackMoney`（是否支持退现）字段进行筛选和查询。

## 搜索参数扩展

### 1. 搜索参数类添加字段

在商品搜索参数类中添加 `isBackMoney` 字段：

```java
// GoodsSearchParams.java
@Schema(title = "是否支持退现")
private Boolean isBackMoney;

public Boolean getIsBackMoney() {
    return isBackMoney;
}

public void setIsBackMoney(Boolean isBackMoney) {
    this.isBackMoney = isBackMoney;
}
```

### 2. 搜索服务中添加查询条件

在 ES 商品搜索服务中添加 `isBackMoney` 的查询逻辑：

```java
// EsGoodsSearchServiceImpl.java
private void buildBackMoneyQuery(BoolQueryBuilder boolQueryBuilder, GoodsSearchParams searchParams) {
    // 是否支持退现筛选
    if (searchParams.getIsBackMoney() != null) {
        boolQueryBuilder.must(QueryBuilders.termQuery("isBackMoney", searchParams.getIsBackMoney()));
    }
}
```

## 前端接口示例

### 1. 商品搜索接口

```java
@RestController
@RequestMapping("/buyer/goods")
public class GoodsSearchController {

    @Autowired
    private EsGoodsSearchService esGoodsSearchService;

    @GetMapping("/search")
    @Operation(summary = "商品搜索")
    public ResultMessage<EsGoodsSearchVO> searchGoods(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Boolean isBackMoney,
            @RequestParam(required = false, defaultValue = "1") Integer pageNumber,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        
        GoodsSearchParams searchParams = new GoodsSearchParams();
        searchParams.setKeyword(keyword);
        searchParams.setIsBackMoney(isBackMoney);
        
        PageVO pageVO = new PageVO();
        pageVO.setPageNumber(pageNumber);
        pageVO.setPageSize(pageSize);
        
        EsGoodsSearchVO result = esGoodsSearchService.searchGoods(searchParams, pageVO);
        return ResultUtil.data(result);
    }
}
```

### 2. 商品筛选接口

```java
@GetMapping("/filter")
@Operation(summary = "商品筛选条件")
public ResultMessage<GoodsFilterVO> getGoodsFilter(
        @RequestParam(required = false) String categoryId,
        @RequestParam(required = false) String storeId) {
    
    GoodsFilterVO filterVO = new GoodsFilterVO();
    
    // 添加退现筛选选项
    List<FilterOption> backMoneyOptions = Arrays.asList(
        new FilterOption("支持退现", "true", "isBackMoney"),
        new FilterOption("不支持退现", "false", "isBackMoney")
    );
    filterVO.setBackMoneyOptions(backMoneyOptions);
    
    return ResultUtil.data(filterVO);
}
```

## 前端使用示例

### 1. 搜索页面筛选

```javascript
// 商品搜索页面
const searchGoods = async (params) => {
  const searchParams = {
    keyword: params.keyword,
    isBackMoney: params.isBackMoney, // true/false/null
    pageNumber: params.pageNumber || 1,
    pageSize: params.pageSize || 20
  };
  
  const response = await api.get('/buyer/goods/search', { params: searchParams });
  return response.data;
};

// 筛选组件
const BackMoneyFilter = ({ value, onChange }) => {
  const options = [
    { label: '全部', value: null },
    { label: '支持退现', value: true },
    { label: '不支持退现', value: false }
  ];
  
  return (
    <div className="filter-group">
      <h4>退现政策</h4>
      {options.map(option => (
        <label key={option.value}>
          <input
            type="radio"
            name="isBackMoney"
            value={option.value}
            checked={value === option.value}
            onChange={() => onChange(option.value)}
          />
          {option.label}
        </label>
      ))}
    </div>
  );
};
```

### 2. 商品列表展示

```javascript
// 商品卡片组件
const GoodsCard = ({ goods }) => {
  return (
    <div className="goods-card">
      <img src={goods.thumbnail} alt={goods.goodsName} />
      <h3>{goods.goodsName}</h3>
      <p className="price">¥{goods.price}</p>
      
      {/* 退现标签 */}
      {goods.isBackMoney && (
        <span className="back-money-tag">支持退现</span>
      )}
      
      <div className="goods-tags">
        {goods.tags && goods.tags.map(tag => (
          <span key={tag} className="tag">{tag}</span>
        ))}
      </div>
    </div>
  );
};
```

### 3. 商品详情页展示

```javascript
// 商品详情页
const GoodsDetail = ({ goods }) => {
  return (
    <div className="goods-detail">
      <h1>{goods.goodsName}</h1>
      <div className="price">¥{goods.price}</div>
      
      {/* 服务保障 */}
      <div className="service-guarantee">
        <h3>服务保障</h3>
        <ul>
          <li>
            <span className="service-icon">💰</span>
            <span>
              {goods.isBackMoney ? '支持退现' : '不支持退现'}
            </span>
          </li>
          <li>
            <span className="service-icon">🚚</span>
            <span>快速发货</span>
          </li>
          <li>
            <span className="service-icon">🛡️</span>
            <span>品质保证</span>
          </li>
        </ul>
      </div>
    </div>
  );
};
```

## 移动端适配

### 1. 移动端筛选

```javascript
// 移动端筛选弹窗
const MobileFilter = ({ filters, onApply }) => {
  const [isBackMoney, setIsBackMoney] = useState(filters.isBackMoney);
  
  const handleApply = () => {
    onApply({
      ...filters,
      isBackMoney
    });
  };
  
  return (
    <div className="mobile-filter">
      <div className="filter-section">
        <h4>退现政策</h4>
        <div className="radio-group">
          <label>
            <input
              type="radio"
              name="isBackMoney"
              checked={isBackMoney === null}
              onChange={() => setIsBackMoney(null)}
            />
            全部
          </label>
          <label>
            <input
              type="radio"
              name="isBackMoney"
              checked={isBackMoney === true}
              onChange={() => setIsBackMoney(true)}
            />
            支持退现
          </label>
          <label>
            <input
              type="radio"
              name="isBackMoney"
              checked={isBackMoney === false}
              onChange={() => setIsBackMoney(false)}
            />
            不支持退现
          </label>
        </div>
      </div>
      
      <button onClick={handleApply} className="apply-btn">
        应用筛选
      </button>
    </div>
  );
};
```

### 2. 移动端商品卡片

```javascript
// 移动端商品卡片
const MobileGoodsCard = ({ goods }) => {
  return (
    <div className="mobile-goods-card">
      <img src={goods.thumbnail} alt={goods.goodsName} />
      <div className="goods-info">
        <h3>{goods.goodsName}</h3>
        <div className="price-row">
          <span className="price">¥{goods.price}</span>
          {goods.isBackMoney && (
            <span className="back-money-badge">退现</span>
          )}
        </div>
        <div className="store-info">
          <span>{goods.storeName}</span>
          <span className="sales">销量 {goods.buyCount}</span>
        </div>
      </div>
    </div>
  );
};
```

## 样式示例

### 1. 退现标签样式

```css
/* 支持退现标签 */
.back-money-tag {
  display: inline-block;
  padding: 2px 6px;
  background-color: #52c41a;
  color: white;
  font-size: 12px;
  border-radius: 3px;
  margin-left: 8px;
}

.back-money-badge {
  display: inline-block;
  padding: 1px 4px;
  background-color: #ff4d4f;
  color: white;
  font-size: 10px;
  border-radius: 2px;
  margin-left: 4px;
}
```

### 2. 筛选组件样式

```css
/* 筛选组件 */
.filter-group {
  margin-bottom: 20px;
}

.filter-group h4 {
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  cursor: pointer;
}

.filter-group input[type="radio"] {
  margin-right: 8px;
}
```

## API 响应示例

### 1. 搜索结果响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 150,
    "records": [
      {
        "id": "goods_001",
        "goodsName": "iPhone 14 Pro",
        "price": 7999.00,
        "thumbnail": "https://example.com/iphone14.jpg",
        "storeName": "Apple官方旗舰店",
        "isBackMoney": true,
        "tags": ["热销", "新品"]
      },
      {
        "id": "goods_002",
        "goodsName": "华为 Mate 50",
        "price": 4999.00,
        "thumbnail": "https://example.com/mate50.jpg",
        "storeName": "华为官方店",
        "isBackMoney": false,
        "tags": ["推荐"]
      }
    ]
  }
}
```

### 2. 筛选条件响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "backMoneyOptions": [
      {
        "label": "支持退现",
        "value": "true",
        "field": "isBackMoney"
      },
      {
        "label": "不支持退现",
        "value": "false",
        "field": "isBackMoney"
      }
    ]
  }
}
```

## 注意事项

### 1. 数据处理
- 前端需要正确处理 `null`、`true`、`false` 三种状态
- 在显示时将 `null` 视为"不支持退现"或不显示标签

### 2. 用户体验
- 提供清晰的筛选选项说明
- 在商品卡片上明显标识支持退现的商品
- 在商品详情页详细说明退现政策

### 3. 性能优化
- 合理使用缓存减少 ES 查询压力
- 对于热门搜索条件可以预先缓存结果
- 移动端考虑分页加载和懒加载

通过以上示例，可以完整地在前端实现基于 `isBackMoney` 字段的商品搜索和展示功能，为用户提供更好的购物体验。
