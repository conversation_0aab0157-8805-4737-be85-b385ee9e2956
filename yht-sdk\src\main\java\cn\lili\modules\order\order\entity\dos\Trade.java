package cn.lili.modules.order.order.entity.dos;

import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 交易
 *
 * <AUTHOR>
 * @since 2020/11/17 7:34 下午
 */
@Data
@TableName("li_trade")
@Schema(title = "交易")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Trade extends BaseStandardEntity {

    private static final long serialVersionUID = 5177608752643561827L;

    /**
     * @see OrderStatusEnum
     */
    @Schema(title = "订单状态")
    private String orderStatus;

    @Schema(title = "交易编号")
    private String sn;

    @Schema(title = "买家id")
    private String memberId;

    @Schema(title = "买家用户名")
    private String memberName;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "第三方付款流水号")
    private String receivableNo;

    @Schema(title = "总价格")
    private Double flowPrice;

    @Schema(title = "原价")
    private Double goodsPrice;

    @Schema(title = "运费")
    private Double freightPrice;

    @Schema(title = "优惠的金额")
    private Double discountPrice;


    @Schema(title = "收货人姓名")
    private String consigneeName;

    @Schema(title = "收件人手机")
    private String consigneeMobile;

    @Schema(title = "地址名称， '，'分割")
    private String consigneeAddressPath;

    @Schema(title = "地址id，'，'分割 ")
    private String consigneeAddressIdPath;

    @Schema(title = "服务费")
    private Double serviceFee;

    public Trade(TradeDTO tradeDTO) {
        String originId = this.getId();
        if (tradeDTO.getUserAddress() != null) {
            BeanUtil.copyProperties(tradeDTO.getUserAddress(), this);
            this.setConsigneeMobile(tradeDTO.getUserAddress().getMobile());
            this.setConsigneeName(tradeDTO.getUserAddress().getName());
        }
        BeanUtil.copyProperties(tradeDTO, this);
        BeanUtil.copyProperties(tradeDTO.getPriceDetailDTO(), this);

        if (tradeDTO.getPriceDetailDTO().getFlowPrice() <= 0) {
            this.setOrderStatus(OrderStatusEnum.PAID.name());
        } else {
            this.setOrderStatus(OrderStatusEnum.UNPAID.name());
        }

        this.setServiceFee(tradeDTO.getPriceDetailDTO().getServiceFee());

        this.setId(originId);
    }



    /**
     * 订单是否支持支付
     *
     * @return 是否支持支付
     */
    public boolean supportPayment() {
        return getOrderStatus().equals(OrderStatusEnum.UNPAID.name());
    }

    public String getOrderStatus() {
        //兼容历史数据，如果没有订单状态则返回未支付状态。
        if (StringUtils.isEmpty(orderStatus)) {
            return OrderStatusEnum.UNPAID.name();
        }
        return orderStatus;
    }
}