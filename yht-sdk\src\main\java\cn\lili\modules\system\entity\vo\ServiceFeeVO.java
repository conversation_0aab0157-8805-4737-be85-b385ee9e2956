package cn.lili.modules.system.entity.vo;

import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.system.entity.dos.Region;
import cn.lili.modules.system.entity.dos.ServiceFee;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@Data
@NoArgsConstructor
public class ServiceFeeVO extends ServiceFee {

    /**
     * 子信息
     */
    private List<ServiceFeeVO> children;

    private Integer level = 1;

    public ServiceFeeVO(ServiceFee serviceFee) {
        BeanUtil.copyProperties(serviceFee, this);
        this.children = new ArrayList<>();
    }

    public ServiceFeeVO(ServiceFee serviceFee,int level) {
        BeanUtil.copyProperties(serviceFee, this);
        this.children = new ArrayList<>();
        this.level = level;
    }

}


