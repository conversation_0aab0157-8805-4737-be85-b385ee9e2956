package cn.lili.modules.page.entity.dto;

import cn.lili.common.vo.PageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 页面数据分页查询参数包装类
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@NoArgsConstructor
@Schema(title = "页面数据分页查询参数")
public class PageDataListParams {

    @Schema(title = "分页参数")
    private PageVO pageVO;

    @Schema(title = "查询参数")
    private PageDataSearchParams searchParams;

    public PageDataListParams(PageVO pageVO, PageDataSearchParams searchParams) {
        this.pageVO = pageVO;
        this.searchParams = searchParams;
    }
}
