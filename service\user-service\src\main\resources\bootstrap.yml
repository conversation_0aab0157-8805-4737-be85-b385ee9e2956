server:
  #端口号
  port: 11114
spring:
  #服务名称
  application:
    name: '@artifactId@'
  #运行环境
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.1.182:30848
        namespace: tb_s2b2b2c
        group: DEFAULT_GROUP
      username: nacos
      password: nacos

      config:
        server-addr: 192.168.1.182:30848
        username: nacos
        password: nacos
        namespace: tb_s2b2b2c
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: application-dev.${spring.cloud.nacos.config.file-extension} # 配置文件名-Data Id
            group: DEFAULT_GROUP  # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
logging:
  file:
    path: '@log.path@'
#  config: http://@nacos.server@/nacos/v1/cs/configs?group=DEFAULT_GROUP&tenant=@nacos.namespaceId@&dataId=logback-spring.xml
