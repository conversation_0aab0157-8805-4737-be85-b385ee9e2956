# PageData Feign 客户端使用指南

## 概述

PageDataClient 是用于页面数据管理的 Feign 客户端，提供了页面数据的增删改查、状态管理等功能的远程调用接口。

## 客户端接口

### 基础CRUD操作

#### 1. 获取页面详情
```java
@Autowired
private PageDataClient pageDataClient;

// 根据ID获取页面详情
PageData pageData = pageDataClient.getById("pageId");
```

#### 2. C端获取页面信息
```java
// 构建查询参数
PageDataShowParams params = new PageDataShowParams();
params.setPageType(PageTypeEnum.INDEX);
params.setExtendId("-1");

// 获取页面数据
PageDataVO pageDataVO = pageDataClient.getPageData(params);
```

#### 3. 获取页面分页列表
```java
// 分页参数
PageVO pageVO = new PageVO();
pageVO.setPageNumber(1);
pageVO.setPageSize(10);

// 查询参数
PageDataSearchParams searchParams = new PageDataSearchParams();
searchParams.setPageType(PageTypeEnum.INDEX);

// 获取分页数据
Page<PageData> pageList = pageDataClient.getPageDataList(pageVO, searchParams);
```

#### 4. 添加页面
```java
// 构建保存DTO
PageDataSaveDTO saveDTO = new PageDataSaveDTO();
saveDTO.setName("首页");
saveDTO.setPageData("{\"components\":[]}");
saveDTO.setPageType(PageTypeEnum.INDEX);
saveDTO.setEnable(true);

// 添加页面
PageData newPageData = pageDataClient.addPageData(saveDTO);
```

#### 5. 修改页面
```java
// 构建修改DTO
PageDataSaveDTO updateDTO = new PageDataSaveDTO();
updateDTO.setId("pageId");
updateDTO.setName("修改后的首页");
updateDTO.setPageData("{\"components\":[{\"type\":\"banner\"}]}");
updateDTO.setPageType(PageTypeEnum.INDEX);
updateDTO.setEnable(true);

// 修改页面
PageData updatedPageData = pageDataClient.updatePageData(updateDTO);
```

#### 6. 删除页面
```java
// 删除页面
Boolean result = pageDataClient.deletePageData("pageId");
```

### 状态管理

#### 1. 更新页面状态
```java
// 启用页面
PageData pageData = pageDataClient.updateStatus("pageId", true);

// 禁用页面
PageData pageData = pageDataClient.updateStatus("pageId", false);
```

#### 2. 发布首页
```java
// 发布首页
PageData pageData = pageDataClient.releaseIndex("pageId");
```

### 查询工具方法

#### 1. 根据页面类型获取页面
```java
// 获取平台首页
PageDataVO indexPage = pageDataClient.getPageDataByType("INDEX", "-1");

// 获取店铺首页
PageDataVO storePage = pageDataClient.getPageDataByType("STORE", "storeId");
```

#### 2. 检查页面是否存在
```java
// 检查平台首页是否存在
Boolean exists = pageDataClient.checkPageDataExists("INDEX", "-1");

// 检查店铺首页是否存在
Boolean storeExists = pageDataClient.checkPageDataExists("STORE", "storeId");
```

#### 3. 获取启用的页面列表
```java
// 获取所有启用的页面
List<PageData> allEnabledPages = pageDataClient.getEnabledPageDataList(null);

// 获取指定类型的启用页面
List<PageData> indexPages = pageDataClient.getEnabledPageDataList("INDEX");
```

## 使用场景

### 1. 商城首页展示
```java
@Service
public class IndexPageService {
    
    @Autowired
    private PageDataClient pageDataClient;
    
    /**
     * 获取商城首页数据
     */
    public PageDataVO getIndexPageData() {
        PageDataShowParams params = new PageDataShowParams();
        params.setPageType(PageTypeEnum.INDEX);
        params.setExtendId("-1");
        
        return pageDataClient.getPageData(params);
    }
}
```

### 2. 店铺装修页面
```java
@Service
public class StorePageService {
    
    @Autowired
    private PageDataClient pageDataClient;
    
    /**
     * 获取店铺首页数据
     */
    public PageDataVO getStorePageData(String storeId) {
        PageDataShowParams params = new PageDataShowParams();
        params.setPageType(PageTypeEnum.STORE);
        params.setExtendId(storeId);
        
        return pageDataClient.getPageData(params);
    }
    
    /**
     * 保存店铺装修数据
     */
    public PageData saveStorePageData(String storeId, String pageData) {
        // 检查是否已存在
        Boolean exists = pageDataClient.checkPageDataExists("STORE", storeId);
        
        PageDataSaveDTO saveDTO = new PageDataSaveDTO();
        saveDTO.setName("店铺首页");
        saveDTO.setPageData(pageData);
        saveDTO.setPageType(PageTypeEnum.STORE);
        saveDTO.setEnable(true);
        
        if (exists) {
            // 更新现有页面
            // 需要先获取现有页面ID
            PageDataVO existingPage = pageDataClient.getPageDataByType("STORE", storeId);
            if (existingPage != null) {
                saveDTO.setId(existingPage.getId());
                return pageDataClient.updatePageData(saveDTO);
            }
        }
        
        // 创建新页面
        return pageDataClient.addPageData(saveDTO);
    }
}
```

### 3. 页面管理后台
```java
@Service
public class PageManagementService {
    
    @Autowired
    private PageDataClient pageDataClient;
    
    /**
     * 获取页面管理列表
     */
    public Page<PageData> getPageManagementList(int page, int size, PageTypeEnum pageType) {
        PageVO pageVO = new PageVO();
        pageVO.setPageNumber(page);
        pageVO.setPageSize(size);
        
        PageDataSearchParams searchParams = new PageDataSearchParams();
        searchParams.setPageType(pageType);
        
        return pageDataClient.getPageDataList(pageVO, searchParams);
    }
    
    /**
     * 批量启用/禁用页面
     */
    public void batchUpdateStatus(List<String> pageIds, Boolean enable) {
        for (String pageId : pageIds) {
            pageDataClient.updateStatus(pageId, enable);
        }
    }
}
```

### 4. 移动端页面适配
```java
@Service
public class MobilePageService {
    
    @Autowired
    private PageDataClient pageDataClient;
    
    /**
     * 获取移动端页面数据
     */
    public PageDataVO getMobilePageData(PageTypeEnum pageType, String extendId) {
        // 先尝试获取移动端专用页面
        PageDataShowParams mobileParams = new PageDataShowParams();
        mobileParams.setPageType(pageType);
        mobileParams.setExtendId(extendId);
        
        PageDataVO pageData = pageDataClient.getPageData(mobileParams);
        
        // 如果没有移动端页面，则使用PC端页面
        if (pageData == null || pageData.getPageData() == null) {
            pageData = pageDataClient.getPageDataByType(pageType.name(), extendId);
        }
        
        return pageData;
    }
}
```

## 错误处理

### 1. 服务降级处理
当 system-service 不可用时，Feign 客户端会自动调用 PageDataFallback 中的降级方法：

```java
// 降级方法会返回默认值
PageData pageData = pageDataClient.getById("pageId"); // 返回 null
PageDataVO pageDataVO = pageDataClient.getPageData(params); // 返回空的 PageDataVO
Page<PageData> pageList = pageDataClient.getPageDataList(pageVO, searchParams); // 返回空的分页对象
```

### 2. 异常处理建议
```java
@Service
public class SafePageDataService {
    
    @Autowired
    private PageDataClient pageDataClient;
    
    public PageDataVO getPageDataSafely(PageDataShowParams params) {
        try {
            PageDataVO result = pageDataClient.getPageData(params);
            if (result != null && result.getPageData() != null) {
                return result;
            }
            
            // 返回默认页面数据
            return getDefaultPageData();
        } catch (Exception e) {
            log.error("获取页面数据失败", e);
            return getDefaultPageData();
        }
    }
    
    private PageDataVO getDefaultPageData() {
        PageDataVO defaultPage = new PageDataVO(null);
        defaultPage.setPageData("{\"components\":[]}"); // 空的页面结构
        return defaultPage;
    }
}
```

## 配置说明

### 1. Feign 配置
确保在应用配置中启用了 Feign 客户端：

```yaml
# application.yml
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
  hystrix:
    enabled: true
```

### 2. 服务发现配置
确保能够发现 system-service：

```yaml
# application.yml
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
```

## 注意事项

1. **服务依赖**：PageDataClient 依赖 system-service，确保服务正常运行
2. **数据一致性**：跨服务调用时注意数据一致性问题
3. **性能考虑**：频繁调用时考虑添加缓存机制
4. **错误处理**：合理处理服务降级和异常情况
5. **参数验证**：调用前验证参数的有效性

## 最佳实践

1. **缓存策略**：对于频繁访问的页面数据，建议添加本地缓存
2. **异步处理**：对于非关键路径的页面数据更新，可以考虑异步处理
3. **监控告警**：添加服务调用的监控和告警机制
4. **版本兼容**：注意接口版本的向后兼容性
5. **文档维护**：及时更新接口文档和使用说明
