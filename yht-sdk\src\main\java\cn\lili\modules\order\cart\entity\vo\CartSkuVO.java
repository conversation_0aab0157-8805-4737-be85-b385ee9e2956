package cn.lili.modules.order.cart.entity.vo;

import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.order.cart.entity.enums.CartSceneEnum;
import cn.lili.modules.promotion.entity.enums.FullDiscountTypeEnum;
import cn.lili.modules.promotion.entity.enums.GiftEnum;
import cn.lili.modules.promotion.tools.PromotionTools;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 购物车中的产品
 *
 * <AUTHOR>
 * @since 2020-03-24 10:33 上午
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CartSkuVO extends CartBase implements Serializable {


    private static final long serialVersionUID = -894598033321906974L;


    // 预设 编号字段
    private String sn;
    /**
     * 对应的sku DO
     */
    private GoodsSku goodsSku;

    /**
     * 供应商商品物流 ID
     */
    private String supplierGoodsTemplateId;

    @Schema(title = "购买数量")
    private Integer num;

    @Schema(title = "购买时的成交价")
    private Double purchasePrice;

    @Schema(title = "小记")
    private Double subTotal;

    @Schema(title = "是否锁定")
    private Boolean locked;

    @Schema(title = "锁定标识")
    private String lockedTag;

    @Schema(title = "单价")
    private Double utilPrice;

    @Schema(title = "是否选中，选中则下单会进行结算")
    private Boolean checked;

    @Schema(title = "是否免运费")
    private Boolean isFreeFreight;

    @Schema(title = "是否失效")
    private Boolean invalid;

    @Schema(title = "购物车商品错误消息")
    private String errorMessage;

    @Schema(title = "是否可配送")
    private Boolean isShip;


    @Schema(title = "积分购买 积分数量")
    private Long points;

    @Schema(title = "商品促销活动集合，key 为 促销活动类型，value 为 促销活动实体信息 ")
    private Map<String, Object> promotionMap;

    @Schema(title = "供应商id")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "当前单品促销类型")
    private PromotionTypeEnum promotionTypeEnum;

    @Schema(title = "促销id：" +
            "拼团：则为拼团id为空则为创建拼团" +
            "砍价活动：为砍价活动id" +
            "积分兑换：积分兑换活动id")
    private String promotionId;

    /**
     * @see CartSceneEnum
     */
    @Schema(title = "购物车类型")
    private CartSceneEnum cartType;

    /**
     * @see SalesModeEnum
     */
    @Schema(title = "商品销售模式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String salesModel;

    @Schema(title = "是否收藏")
    private Boolean collectFlag;

    @Schema(title = "是否支持服务费")
    private Boolean isServiceFee;

    /**
     * 在构造器里初始化促销列表，规格列表
     */
    public CartSkuVO(GoodsSku goodsSku) {
        this.goodsSku = goodsSku;
        if (goodsSku.getUpdateTime() == null) {
            this.goodsSku.setUpdateTime(goodsSku.getCreateTime());
        }
        this.checked = true;
        this.invalid = false;
        //默认时间为0，让系统为此商品更新缓存
        this.errorMessage = "";
        this.isShip = true;
        this.purchasePrice = goodsSku.getPromotionFlag() != null && goodsSku.getPromotionFlag() ? goodsSku.getPromotionPrice() : goodsSku.getPrice();
        this.isFreeFreight = false;
        this.utilPrice = goodsSku.getPromotionFlag() != null && goodsSku.getPromotionFlag() ? goodsSku.getPromotionPrice() : goodsSku.getPrice();
        this.setStoreId(goodsSku.getStoreId());
        this.setStoreName(goodsSku.getStoreName());
        //如果供应商id不为空，则设置供应商id
        if (goodsSku.getSupplierId() != null) {
            this.supplierId = goodsSku.getSupplierId();
            this.supplierName = goodsSku.getSupplierName();
        }
        this.salesModel = goodsSku.getSalesModel();
    }

    /**
     * 在构造器里初始化促销列表，规格列表
     */
    public CartSkuVO(GoodsSku goodsSku, PromotionTypeEnum promotionTypeEnum) {
        this(goodsSku);
        // 满额活动添加的 sku，价格设置为 0
        if (promotionTypeEnum.equals(PromotionTypeEnum.FULL_DISCOUNT)) {
            this.purchasePrice = 0D;
            this.locked = true;
            this.lockedTag = GiftEnum.SKU.getDescription();
            this.num = 1;
        }else if(promotionTypeEnum.equals(PromotionTypeEnum.GIFT_GOODS)){
            this.purchasePrice = 0D;
            this.locked = true;
            this.lockedTag = GiftEnum.SKU.getDescription();
        }
    }

    /**
     * 在构造器里初始化促销列表，规格列表
     */
    public CartSkuVO(GoodsSku goodsSku, FullDiscountTypeEnum typeEnum,Integer num) {
        this(goodsSku);
        if(typeEnum.equals(FullDiscountTypeEnum.HALF)){
            this.purchasePrice = CurrencyUtil.div(goodsSku.getPrice(),2);
            this.locked = true;
            this.lockedTag = GiftEnum.HALF.getDescription();
            this.num = num;
            this.utilPrice = CurrencyUtil.div(goodsSku.getPrice(),2);
        }
    }

    /**
     * 在构造器里初始化促销列表，规格列表
     */
    public CartSkuVO(GoodsSku goodsSku, Map<String, Object> promotionMap) {
        this(goodsSku);
        if (promotionMap != null && !promotionMap.isEmpty()) {
            this.promotionMap = promotionMap;
        }
    }

    public Map<String, Object> getPromotionMap() {
        return PromotionTools.filterInvalidPromotionsMap(this.promotionMap);
    }

    public Map<String, Object> notFilterPromotionMap() {
        return this.promotionMap;
    }

    public boolean getCollectFlag(){
        if(this.collectFlag == null){
            return false;
        }
        return this.collectFlag;
    }

    public Boolean getLocked() {
        if (locked == null) {
            return false;
        }
        return locked;
    }
}
