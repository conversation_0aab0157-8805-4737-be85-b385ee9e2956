package cn.lili.controller.client;

import cn.hutool.core.collection.CollUtil;
import cn.lili.modules.file.client.UploadClient;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.file.entity.dto.FileUploadDTO;
import cn.lili.modules.file.plugin.FilePluginFactory;
import cn.lili.modules.file.service.FileService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
public class FileFeignController implements UploadClient {

    @Autowired
    private FilePluginFactory filePluginFactory;

    @Autowired
    private FileService fileService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String upload(FileUploadDTO fileUploadDTO) {
        return filePluginFactory.filePlugin().inputStreamUpload(fileUploadDTO.getBucketName(), fileUploadDTO.getContext(), fileUploadDTO.getKey());
    }


    @Override
    public String uploadByUrl(FileUploadDTO fileUploadDTO) {
        URL url;
        try {
            File byName = fileService.getByName(fileUploadDTO.getKey());

            if (byName == null) {
                URI uri = new URI(fileUploadDTO.getUrl());
                url = uri.toURL();
                try (InputStream inputStream = url.openStream()) {
                    String fileUrl = filePluginFactory.filePlugin().inputStreamUpload(inputStream, fileUploadDTO.getKey());
                    byName = new File();
                    byName.setFileKey(fileUploadDTO.getKey());
                    byName.setName(fileUploadDTO.getKey());
                    byName.setUrl(fileUrl);
                    byName.setOwnerId("0");
                    byName.setUserEnums("SYSTEM");
                    byName.setFileType("image/png");
                    fileService.save(byName);
                }
            }
            return byName.getUrl();
        } catch (IOException e) {
            log.error("文件上传失败", e);
        } catch (URISyntaxException e) {
            log.error("uri解析失败", e);
        }
        return null;
    }

    @Override
    public String uploadFile(FileUploadDTO fileUploadDTO) {
        URL url;
        try {
            log.debug("fileUploadDTO.getKey() = {}", fileUploadDTO.getKey());
            File file = fileService.getByName(fileUploadDTO.getKey());

            if (file == null) {
                String fileUrl = "";
                log.debug("fileUploadDTO.getZipFilePath() = {}", fileUploadDTO.getZipFilePath());
                if(fileUploadDTO.getZipFilePath() != null){
                    fileUrl = filePluginFactory.filePlugin().inputStreamUpload(Files.newInputStream(fileUploadDTO.getZipFilePath()), fileUploadDTO.getContentType(), fileUploadDTO.getKey());
                }else{
                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileUploadDTO.getContextBytes());
                    fileUrl = filePluginFactory.filePlugin().inputStreamUpload((InputStream) byteArrayInputStream, fileUploadDTO.getContentType(), fileUploadDTO.getKey());
                }
                file = new File();
                file.setFileKey(fileUploadDTO.getKey());
                file.setName(fileUploadDTO.getKey());
                file.setUrl(fileUrl);
                file.setOwnerId("0");
                file.setUserEnums("SYSTEM");
                file.setFileType(fileUploadDTO.getContentType());
                fileService.save(file);
            }
            return file.getUrl();
        } catch (Exception e) {
            log.error("文件上传失败", e);
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void remove(List<String> keys) {
        filePluginFactory.filePlugin().deleteFile(keys);
    }

    @Override
    public List<File> getFilesByUrls(List<String> urls) {
        try {
            if (CollUtil.isEmpty(urls)) {
                return new ArrayList<>();
            }

            LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(File::getUrl, urls);
            return fileService.list(queryWrapper);
        } catch (Exception e) {
            log.error("FileFeignController.getFilesByUrls 执行异常，参数：{}", urls, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> checkFileUsageInGoods(List<String> fileUrls, List<String> excludeGoodsIds) {
        try {
            if (CollUtil.isEmpty(fileUrls)) {
                return new ArrayList<>();
            }

            log.info("检查文件在商品中的使用情况：fileUrls={}, excludeGoodsIds={}", fileUrls, excludeGoodsIds);

            List<String> usedFiles = new ArrayList<>();

            // 构建SQL查询，检查这些图片URL是否在商品表中被使用
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT url FROM (");
            sql.append("  SELECT thumbnail as url FROM lilishop_goods.li_goods WHERE thumbnail IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT small as url FROM lilishop_goods.li_goods WHERE small IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT original as url FROM lilishop_goods.li_goods WHERE original IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT image_zip_file as url FROM lilishop_goods.li_goods WHERE image_zip_file IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT thumbnail as url FROM lilishop_goods.li_goods_gallery WHERE thumbnail IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND goods_id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT small as url FROM lilishop_goods.li_goods_gallery WHERE small IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND goods_id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(" UNION ");
            sql.append("  SELECT original as url FROM lilishop_goods.li_goods_gallery WHERE original IN (");

            // 添加占位符
            for (int i = 0; i < fileUrls.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(")");

            // 排除要删除的商品
            if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                sql.append(" AND goods_id NOT IN (");
                for (int i = 0; i < excludeGoodsIds.size(); i++) {
                    if (i > 0) sql.append(",");
                    sql.append("?");
                }
                sql.append(")");
            }

            sql.append(") t WHERE url IS NOT NULL AND url != ''");

            // 准备参数
            List<Object> params = new ArrayList<>();

            // 为每个UNION子查询添加fileUrls参数
            for (int i = 0; i < 7; i++) { // 7个子查询
                params.addAll(fileUrls);
                if (CollUtil.isNotEmpty(excludeGoodsIds)) {
                    params.addAll(excludeGoodsIds);
                }
            }

            // 执行查询
            usedFiles = jdbcTemplate.queryForList(sql.toString(), params.toArray(), String.class);

            log.info("检查结果：被使用的文件URL：{}", usedFiles);

            return usedFiles;

        } catch (Exception e) {
            log.error("FileFeignController.checkFileUsageInGoods 执行异常，参数：fileUrls={}, excludeGoodsIds={}", fileUrls, excludeGoodsIds, e);
            // 异常时返回所有文件都在使用，避免误删
            return fileUrls;
        }
    }

    @Override
    public void deleteFiles(List<String> fileKeys) {
        try {
            if (CollUtil.isEmpty(fileKeys)) {
                return;
            }

            log.info("开始删除文件：{}", fileKeys);

            // 1. 从数据库中查询文件信息
            LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(File::getFileKey, fileKeys);
            List<File> files = fileService.list(queryWrapper);

            if (CollUtil.isEmpty(files)) {
                log.warn("未找到要删除的文件记录：{}", fileKeys);
                return;
            }

            // 2. 删除OSS文件
            try {
                filePluginFactory.filePlugin().deleteFile(fileKeys);
                log.info("OSS文件删除成功：{}", fileKeys);
            } catch (Exception e) {
                log.error("OSS文件删除失败：{}", fileKeys, e);
                // OSS删除失败不影响数据库记录删除
            }

            // 3. 删除数据库记录
            List<String> fileIds = files.stream().map(File::getId).collect(Collectors.toList());
            fileService.removeByIds(fileIds);

            log.info("文件删除完成：数据库记录={}, OSS文件={}", fileIds, fileKeys);

        } catch (Exception e) {
            log.error("FileFeignController.deleteFiles 执行异常，参数：{}", fileKeys, e);
        }
    }
}
