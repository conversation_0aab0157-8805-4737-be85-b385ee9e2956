package cn.lili.controller.feign.order;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.aftersale.service.AfterSaleService;
import cn.lili.modules.order.order.client.AfterSaleClient;
import cn.lili.modules.store.entity.dos.Store;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
@RestController
@RequiredArgsConstructor
public class AfterSaleFeignController implements AfterSaleClient {

    private final AfterSaleService afterSaleService;

    @Override
    public void editAfterSaleStoreInfo(Store store) {
        afterSaleService.update(new LambdaUpdateWrapper<AfterSale>().eq(AfterSale::getStoreId,store.getExtendId()).set(AfterSale::getStoreName,store.getStoreName()));
    }

    @Override
    public void editAfterSaleUserInfo(User user) {
        afterSaleService.update(new LambdaUpdateWrapper<AfterSale>().eq(AfterSale::getMemberId,user.getId()).set(AfterSale::getMemberName,user.getNickName()));
    }

    @Override
    public List<AfterSale> queryByOrderSn(String orderSn) {
        return afterSaleService.queryByOrderSn(orderSn);
    }

    @Override
    public Boolean isAfterSale(String orderSn) {
        return afterSaleService.afterSaleUnComplete(orderSn);
    }

    @Override
    public Double getReturnSuccessRate(String storeId) {
        return afterSaleService.getReturnSuccessRate(storeId);
    }
}
