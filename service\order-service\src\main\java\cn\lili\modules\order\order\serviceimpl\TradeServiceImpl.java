package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.order.cart.entity.dto.MemberCouponDTO;
import cn.lili.modules.order.cart.entity.dto.TradeCouponDTO;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.dos.Trade;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.vo.OrderVO;
import cn.lili.modules.order.order.entity.vo.ReceiptVO;
import cn.lili.modules.order.order.entity.vo.TradeDetailVO;
import cn.lili.modules.order.order.integration.OrderIntegrationHandler;
import cn.lili.modules.order.order.mapper.TradeMapper;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.ReceiptService;
import cn.lili.modules.order.order.service.TradeService;
import cn.lili.modules.payment.client.WalletPointClient;
import cn.lili.modules.payment.entity.dos.CombinePaymentLog;
import cn.lili.modules.payment.entity.dos.PaymentLog;
import cn.lili.modules.payment.entity.dto.PaymentCallback;
import cn.lili.modules.payment.entity.dto.UserPointUpdateDTO;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.payment.entity.enums.UserPointServiceEnum;
import cn.lili.modules.promotion.client.KanjiaActivityClient;
import cn.lili.modules.promotion.client.MemberCouponClient;
import cn.lili.mybatis.util.SceneHelp;
import cn.lili.routing.OrderRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 交易业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:39 下午
 */
@Service
@RequiredArgsConstructor
public class TradeServiceImpl extends ServiceImpl<TradeMapper, Trade> implements TradeService {

    private final Cache<Object> cache;

    private final OrderService orderService;

    private final OrderIntegrationHandler orderIntegrationHandler;

    private final MemberCouponClient memberCouponClient;

    private final WalletPointClient walletPointClient;

    private final KanjiaActivityClient kanjiaActivityClient;

    private final AmqpSender amqpSender;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final ReceiptService receiptService;
    @Override
    @Transactional
    public Trade createTrade(TradeDTO tradeDTO) {

        //创建订单预校验
        createTradeCheck(tradeDTO);
        //创建交易
        Trade trade = new Trade(tradeDTO);
        //交易编号
        String key = CachePrefix.TRADE.getPrefix() + trade.getSn();
        //交易预处理 如积分扣减、优惠券状态变更、设置砍价活动无效等
        pretreatment(tradeDTO);
        //添加交易
        this.save(trade);
        //添加订单
        orderService.intoDB(tradeDTO);
        // 发票
        receiptHandler(tradeDTO);
        //写入缓存，给消费者调用
        // 0402 hutool json 转换 有问题 无法转换 flowPrice 为 0 的情况 会报错 暂时用gson
        cache.put(key, GsonUtils.toJson(tradeDTO));

        //构建订单创建消息
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getOrder()).routingKey(OrderRoutingKey.ORDER_CREATE).message(key).build());

        //如果交易金额为0，直接支付
        if (trade.getFlowPrice() <= 0) {
            this.paymentCallbackZero(trade.getSn());
        }
        return trade;
    }

    @Override
    public Trade getBySn(String sn) {
        LambdaQueryWrapper<Trade> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Trade::getSn, sn);
        return this.getOne(queryWrapper);
    }

    @Override
    public Trade getTrade(String tradeSn, AuthUser authUser) {
        LambdaQueryWrapper<Trade> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Trade::getSn, tradeSn);
        // 只过滤会员被黑进行支付
        if (authUser.getScene().equals(SceneEnums.MEMBER)) {
            queryWrapper.eq(Trade::getMemberId, authUser.getId());
        }
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional
    public void payTrade(PaymentCallback paymentCallback) {

        // 如果是合并支付
        if (Boolean.TRUE.equals(paymentCallback.getIsCombine())) {
            //对订单做处理
            for (PaymentLog paymentLog : paymentCallback.getPaymentLogs()) {
                orderIntegrationHandler.payOrder(paymentLog);
            }
            //对合并支付日志做处理
            CombinePaymentLog combinePaymentLog = paymentCallback.getCombinePaymentLog();
            //修改交易信息
            Trade trade = this.getBySn(combinePaymentLog.getOrderSn());
            trade.setOrderStatus(OrderStatusEnum.PAID.name());
            trade.setPaymentMethod(combinePaymentLog.getPaymentMethod());
            trade.setReceivableNo(combinePaymentLog.getCombineOutTradeNo());
            this.updateById(trade);
        }
        // 否则根据支付日志进行处理
        else {
            Order order = orderIntegrationHandler.payOrder(paymentCallback.getPaymentLog());
            //否则根据支付日志进行处理
            PaymentLog paymentLog = paymentCallback.getPaymentLog();
            //修改交易信息
            Trade trade = this.getBySn(order.getTradeSn());

            trade.setOrderStatus(OrderStatusEnum.PAID.name());
            trade.setPaymentMethod(paymentLog.getPaymentMethod());
            trade.setReceivableNo(paymentLog.getOutTradeNo());
            this.updateById(trade);
        }
    }

    @Override
    public void paymentOrder(PaymentCallback paymentCallback) {
        //对订单做处理
        orderIntegrationHandler.payOrder(paymentCallback.getPaymentLog());
        //修改交易信息
        Trade trade = this.getBySn(paymentCallback.getPaymentLog().getOrderSn());
        if (trade != null) {
            trade.setOrderStatus(OrderStatusEnum.PAID.name());
            trade.setPaymentMethod(paymentCallback.getPaymentLog().getPaymentMethod());
            trade.setReceivableNo(paymentCallback.getPaymentLog().getOutTradeNo());
            this.updateById(trade);
        }
    }

    @Override
    public void paymentCallbackZero(String sn) {
        Trade trade = this.getBySn(sn);
        trade.setOrderStatus(OrderStatusEnum.PAID.name());
        trade.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
        trade.setReceivableNo("-1");
        this.updateById(trade);

        orderIntegrationHandler.payOrderZero(sn);
    }

    @Override
    public void updateTradePrice(String tradeSn) {
        baseMapper.updateTradePrice(tradeSn);
    }

    @Override
    public TradeDetailVO getTradeDetail(String orderSn) {

        SceneHelp.sceneHandler();

        TradeDetailVO tradeDetailVO = new TradeDetailVO();

        tradeDetailVO.setTrade(this.getBySn(orderService.getBySn(orderSn)
                .getTradeSn()));
        orderService.getByTradeSn(tradeDetailVO.getTrade()
                        .getSn())
                .forEach(order -> tradeDetailVO.getOrderDetailVOList()
                        .add(orderService.queryDetail(order.getSn())));

        return tradeDetailVO;
    }

    @Override
    @Transactional
    public void systemCancel(String sn, String reason) {

        Trade trade = this.getBySn(sn);
        if (trade == null) {
            return;
        }
        if (!trade.getOrderStatus().equals(OrderStatusEnum.UNPAID.name())) {
            throw new ServiceException(ResultCode.TRADE_STATUS_ERROR);
        }
        trade.setOrderStatus(OrderStatusEnum.CANCELLED.name());
        this.saveOrUpdate(trade);
        //取消订单
        orderService.getByTradeSn(sn).forEach(order -> {
                    if(order.getOrderStatus().equals(OrderStatusEnum.UNPAID.name())){
                        orderIntegrationHandler.systemCancel(order.getSn(), reason, true);
                    }
                }
        );
    }

    /**
     * 交易信息 - 预处理
     * 如积分扣减、优惠券状态变更、设置砍价活动无效等
     *
     * @param tradeDTO 交易信息
     */
    private void pretreatment(TradeDTO tradeDTO) {

        //优惠券预处理
        couponPretreatment(tradeDTO);
        //积分预处理
        pointPretreatment(tradeDTO);
        //砍价订单预处理
        kanjiaPretreatment(tradeDTO);
        //拼团订单预处理
        checkPintuan(tradeDTO);
    }

    /**
     * 检测是否为拼团订单 不能参与自己发起的拼团活动
     *
     * @param tradeDTO 交易信息
     */
    private void checkPintuan(TradeDTO tradeDTO) {
        //检测是否为拼团订单
        if (tradeDTO.getParentOrderSn() != null) {
            //判断用户不能参与自己发起的拼团活动
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getSn, tradeDTO.getParentOrderSn());
            Order parentOrder = orderService.getOne(queryWrapper, false);
            if (parentOrder.getBuyerId().equals(Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId())) {
                throw new ServiceException(ResultCode.PINTUAN_JOIN_ERROR);
            }
        }
    }

    /**
     * 优惠券预处理
     * 下单同时，扣除优惠券
     *
     * @param tradeDTO 购物车视图
     */
    private void couponPretreatment(TradeDTO tradeDTO) {
        // 优惠券处理
        List<MemberCouponDTO> memberCouponDTOList = new ArrayList<>();
        // 平台优惠券
        if (null != tradeDTO.getPlatformCoupon()) {
            memberCouponDTOList.add(tradeDTO.getPlatformCoupon());
        }
        // 会员优惠券
        List<TradeCouponDTO> storeCoupons = tradeDTO.getStoreCoupons();
        if (storeCoupons != null) {
            memberCouponDTOList.addAll(storeCoupons.stream().map(TradeCouponDTO::getMemberCouponDTO).toList());
        }
        if (memberCouponDTOList.isEmpty()) {
            return;
        }
        //优惠券使用
        List<String> ids = memberCouponDTOList.stream().map(e -> e.getMemberCoupon().getId()).toList();
        memberCouponClient.used(tradeDTO.getMemberId(), ids);

    }

    /**
     * 创建交易，积分处理
     *
     * @param tradeDTO 购物车视图
     */
    private void pointPretreatment(TradeDTO tradeDTO) {

        //需要支付积分
        if (tradeDTO.getPriceDetailDTO() != null && tradeDTO.getPriceDetailDTO().getPayPoint() != null
            && tradeDTO.getPriceDetailDTO().getPayPoint() > 0) {
            StringBuilder orderSns = new StringBuilder();
            for (CartVO item : tradeDTO.getCartList()) {
                orderSns.append(item.getSn());
            }
            walletPointClient.updateMemberPoint(
                UserPointUpdateDTO.builder().userId(tradeDTO.getMemberId()).snReference(tradeDTO.getSn()).description(
                        UserPointUpdateDTO.generateConsumeDescription(orderSns.toString(),
                            tradeDTO.getPriceDetailDTO().getPayPoint()))
                    .points(tradeDTO.getPriceDetailDTO().getPayPoint())
                    .userPointServiceEnum(UserPointServiceEnum.CONSUME).description(
                        UserPointServiceEnum.CONSUME.generateDescription(
                            new Object[] {orderSns, tradeDTO.getPriceDetailDTO().getPayPoint()})).build());

        }
    }


    /**
     * 创建交易、砍价处理
     *
     * @param tradeDTO 购物车视图
     */
    private void kanjiaPretreatment(TradeDTO tradeDTO) {
        tradeDTO.getSkuList().forEach(item -> {
            if (item.getPromotionTypeEnum() != null && item.getPromotionTypeEnum().equals(PromotionTypeEnum.KANJIA)) {
                kanjiaActivityClient.endKanjiaActivity(item.getPromotionId());
            }
        });
    }

    /**
     * 创建订单最后一步校验
     *
     * @param tradeDTO 购物车视图
     */
    private void createTradeCheck(TradeDTO tradeDTO) {

        // 自提订单校验
        if (tradeDTO.getDeliveryMethodEnum().equals(DeliveryMethodEnum.SELF_PICK_UP)) {
            if (tradeDTO.getStoreAddress() == null) {
                throw new ServiceException(ResultCode.STORE_ADDRESS_NOT_EXIST);
            }
        } else {
            //创建订单如果没有收获地址，
            UserAddress userAddress = tradeDTO.getUserAddress();
            if (userAddress == null && !GoodsTypeEnum.VIRTUAL_GOODS.name()
                .equals(tradeDTO.getCheckedSkuList().get(0).getGoodsSku().getGoodsType())) {
                throw new ServiceException(ResultCode.MEMBER_ADDRESS_NOT_EXIST);
            }

            // 订单配送区域校验
            if (tradeDTO.getNotSupportFreight() != null && !tradeDTO.getNotSupportFreight().isEmpty()) {
                StringBuilder stringBuilder = new StringBuilder("包含商品有-");
                tradeDTO.getNotSupportFreight().forEach(sku -> stringBuilder.append(sku.getGoodsSku().getGoodsName()));
                throw new ServiceException(ResultCode.ORDER_NOT_SUPPORT_DISTRIBUTION, stringBuilder.toString());
            }

            // 购物车校验
            if (tradeDTO.getCartList().stream().noneMatch(CartVO::getChecked)) {
                throw new ServiceException(ResultCode.ORDER_NOT_EXIST_VALID);
            }
        }
    }


    /**
     * 发票处理
     *
     * @param tradeDTO 交易信息
     */
    private void receiptHandler(TradeDTO tradeDTO) {
        // 根据交易sn查询订单信息
        List<OrderVO> orderList = tradeDTO.getOrderVO();
        // 获取发票信息
        ReceiptVO receiptVO = tradeDTO.getReceiptVO();
        // 如果需要获取发票则保存发票信息
        if (Boolean.TRUE.equals(tradeDTO.getNeedReceipt()) && !orderList.isEmpty()) {
            List<Receipt> receipts = new ArrayList<>();
            for (OrderVO orderVO : orderList) {
                Receipt receipt = new Receipt();
                BeanUtil.copyProperties(receiptVO, receipt);
                receipt.setMemberId(orderVO.getBuyerId());
                receipt.setMemberName(orderVO.getNickname());
                receipt.setStoreId(orderVO.getStoreId());
                receipt.setStoreName(orderVO.getStoreName());
                receipt.setOrderSn(orderVO.getSn());
                receipt.setReceiptDetail(JSONUtil.toJsonStr(orderVO.getOrderItems()));
                receipt.setReceiptPrice(orderVO.getFlowPrice());
                receipt.setReceiptStatus(0);
                receipts.add(receipt);
            }
            // 保存发票
            receiptService.saveBatch(receipts);
        }
    }

}