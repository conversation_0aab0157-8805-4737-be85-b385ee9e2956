package cn.lili.modules.page.client;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.page.entity.dos.PageData;
import cn.lili.modules.page.entity.dto.PageDataListParams;
import cn.lili.modules.page.entity.dto.PageDataSaveDTO;
import cn.lili.modules.page.entity.dto.PageDataSearchParams;
import cn.lili.modules.page.entity.dto.PageDataShowParams;
import cn.lili.modules.page.entity.vos.PageDataVO;
import cn.lili.modules.page.fallback.PageDataFallback;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 页面数据服务客户端
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "pageData", fallback = PageDataFallback.class)
public interface PageDataClient {

    /**
     * 获取页面详情
     *
     * @param id 页面ID
     * @return 页面数据
     */
    @GetMapping("/feign/system/pageData/{id}")
    PageData getById(@PathVariable String id);

    /**
     * C端获取页面信息
     *
     * @param pageDataShowParams 页面展示参数
     * @return 页面数据VO
     */
    @PostMapping("/feign/system/pageData/show")
    PageDataVO getPageData(@RequestBody PageDataShowParams pageDataShowParams);

    /**
     * 获取页面分页列表
     *
     * @param params 分页查询参数
     * @return 页面分页数据
     */
    @PostMapping("/feign/system/pageData/page")
    Page<PageData> getPageDataList(@RequestBody PageDataListParams params);

    /**
     * 添加页面
     *
     * @param pageDataSaveDTO 页面保存DTO
     * @return 页面数据
     */
    @PostMapping("/feign/system/pageData")
    PageData addPageData(@RequestBody PageDataSaveDTO pageDataSaveDTO);

    /**
     * 修改页面
     *
     * @param pageDataSaveDTO 页面保存DTO
     * @return 页面数据
     */
    @PutMapping("/feign/system/pageData")
    PageData updatePageData(@RequestBody PageDataSaveDTO pageDataSaveDTO);

    /**
     * 更新页面状态
     *
     * @param id 页面ID
     * @param enable 是否启用
     * @return 页面数据
     */
    @PutMapping("/feign/system/pageData/{id}/status")
    PageData updateStatus(@PathVariable String id, @RequestParam Boolean enable);

    /**
     * 发布首页
     *
     * @param id 页面ID
     * @return 页面数据
     */
    @PutMapping("/feign/system/pageData/{id}/release")
    PageData releaseIndex(@PathVariable String id);

    /**
     * 删除页面
     *
     * @param id 页面ID
     * @return 是否成功
     */
    @DeleteMapping("/feign/system/pageData/{id}")
    Boolean deletePageData(@PathVariable String id);

    /**
     * 根据页面类型和扩展ID获取页面
     *
     * @param pageType 页面类型
     * @param extendId 扩展ID
     * @return 页面数据VO
     */
    @GetMapping("/feign/system/pageData/type/{pageType}")
    PageDataVO getPageDataByType(@PathVariable String pageType, @RequestParam(required = false) String extendId);

    /**
     * 检查页面是否存在
     *
     * @param pageType 页面类型
     * @param extendId 扩展ID
     * @return 是否存在
     */
    @GetMapping("/feign/system/pageData/exists")
    Boolean checkPageDataExists(@RequestParam String pageType, @RequestParam(required = false) String extendId);

    /**
     * 获取启用的页面列表
     *
     * @param pageType 页面类型
     * @return 页面列表
     */
    @GetMapping("/feign/system/pageData/enabled")
    java.util.List<PageData> getEnabledPageDataList(@RequestParam(required = false) String pageType);
}
