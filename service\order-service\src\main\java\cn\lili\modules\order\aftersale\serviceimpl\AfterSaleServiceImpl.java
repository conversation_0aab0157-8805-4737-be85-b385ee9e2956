package cn.lili.modules.order.aftersale.serviceimpl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.TempCachePrefix;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.ObjectFieldEnum;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.common.vo.PageVO;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.lililogs.logs.annotation.SystemLogPoint;
import cn.lili.modules.member.client.UserAddressClient;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.member.entity.dto.AddressSearchParams;
import cn.lili.modules.member.entity.enums.AddressTypeEnum;
import cn.lili.modules.order.aftersale.aop.AfterSaleLogPoint;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.aftersale.entity.dto.AfterSaleDTO;
import cn.lili.modules.order.aftersale.entity.dto.AfterSaleForceEndParams;
import cn.lili.modules.order.aftersale.entity.dto.AfterSaleItem;
import cn.lili.modules.order.aftersale.entity.dto.AfterSaleMessageDTO;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleApplyVO;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleSearchParams;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleStatisticsVO;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleVO;
import cn.lili.modules.order.aftersale.mapper.AfterSaleMapper;
import cn.lili.modules.order.aftersale.service.AfterSaleService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.VerificationCode;
import cn.lili.modules.order.order.entity.dto.AfterSaleExportDTO;
import cn.lili.modules.order.order.entity.dto.OrderStatusStatisticsDTO;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.DeliverStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import cn.lili.modules.order.order.entity.enums.PayStatusEnum;
import cn.lili.modules.order.order.entity.enums.VerificationStatusEnum;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.RefundFlowService;
import cn.lili.modules.order.order.service.VerificationCodeService;
import cn.lili.modules.order.trade.entity.enums.AfterSaleRefundWayEnum;
import cn.lili.modules.order.trade.entity.enums.AfterSaleStatusEnum;
import cn.lili.modules.order.trade.entity.enums.AfterSaleTypeEnum;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.statistics.entity.dto.StatisticsQueryParam;
import cn.lili.modules.statistics.util.StatisticsDateUtil;
import cn.lili.modules.system.client.ExportLogClient;
import cn.lili.modules.system.client.LogisticsClient;
import cn.lili.modules.system.client.RefundAddressClient;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dos.ExportLog;
import cn.lili.modules.system.entity.dos.Logistics;
import cn.lili.modules.system.entity.dos.RefundAddress;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.ExportDTO;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.enums.ExportStatusEnum;
import cn.lili.modules.system.entity.enums.ExportTypeEnum;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.entity.vo.Traces;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.mybatis.util.SceneHelp;
import cn.lili.routing.AfterSaleRoutingKey;
import cn.lili.routing.ExportRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 售后业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
@RequiredArgsConstructor
public class AfterSaleServiceImpl extends ServiceImpl<AfterSaleMapper, AfterSale> implements AfterSaleService {

    /**
     * 订单
     */
    private final OrderService orderService;

    /**
     * 退款流水
     */
    private final RefundFlowService refundFlowService;
    /**
     * 订单货物
     */
    private final OrderItemService orderItemService;
    /**
     * 物流公司
     */
    private final LogisticsClient logisticsClient;
    /**
     * 系统设置
     */
    private final SettingClient settingClient;
    /**
     * RocketMQ配置
     */
    private final AmqpSender amqpSender;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 地址
     */
    private final UserAddressClient addressClient;

    /**
     * 导出记录
     */
    private final ExportLogClient exportLogClient;

    /**
     * 验证码
     */
    private final VerificationCodeService verificationCodeService;

    private final Cache cache;

    private final RefundAddressClient refundAddressClient;

    @Override
    public Page<AfterSaleVO> getAfterSalePages(AfterSaleSearchParams saleSearchParams) {
        return baseMapper.queryByParams(PageUtil.initPage(saleSearchParams), saleSearchParams.queryWrapper());
    }

    @Override
    public AfterSaleStatisticsVO getAfterSaleStatistics() {
        QueryWrapper<AfterSale> queryWrapper = new QueryWrapper<>();
        if (!UserContext.isManager()) {
            queryWrapper.and(
                i -> i.eq("store_id", UserContext.getCurrentId()).or().eq("supplier_id", UserContext.getCurrentId()));
        }
        return baseMapper.getAfterSaleCount(queryWrapper);
    }

    @Override
    public void exportAfterSaleOrder(AfterSaleSearchParams saleSearchParams) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        //获取导出数据
        if (currentUser.getScene().name().equals(SceneEnums.STORE.name()) || currentUser.getScene().name().equals(SceneEnums.SUPPLIER.name())) {
            saleSearchParams.setStoreId(currentUser.getExtendId());
        }
        QueryWrapper<AfterSaleVO> wrapper = saleSearchParams.queryWrapper();
        wrapper.orderByDesc("create_time");
        List<AfterSaleExportDTO> list = this.baseMapper.getAfterSaleExportList(wrapper);
        if (list.isEmpty()) {
            throw new ServiceException(ResultCode.EXPORT_DATA_ERROR);
        }
        List<Object> exportList = list.stream()
                .map(Object.class::cast) // 进行类型转换
                .toList();

        // 保存导出记录
        ExportLog exportLog = new ExportLog();
        String fileName = "订单售后列表-" + System.currentTimeMillis();
        exportLog.setFileName(fileName);
        if (currentUser.getScene().name().equals(SceneEnums.STORE.name()) || currentUser.getScene().name().equals(SceneEnums.SUPPLIER.name())) {
            exportLog.setStoreId(currentUser.getExtendId());
            exportLog.setStoreName(currentUser.getExtendName());
        } else {
            exportLog.setStoreId("-1");
            exportLog.setStoreName("平台");
        }
        exportLog.setOperator(currentUser.getNickName());
        exportLog.setExportNum(exportList.size());
        exportLog.setType(ExportTypeEnum.AFTER_SALE.name());
        exportLog.setStatus(ExportStatusEnum.EXPORTING.name());
        ExportLog saveExport = exportLogClient.saveExport(exportLog);

        // 发送MQ消息，异步处理导出数据
        ExportDTO exportDTO = new ExportDTO();
        exportDTO.setExportList(exportList);
        exportDTO.setExportId(saveExport.getId());
        exportDTO.setFileName(fileName);
        exportDTO.setExportTypeEnum(ExportTypeEnum.AFTER_SALE);
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getExport()).routingKey(ExportRoutingKey.EXPORT_DATA).message(JSONUtil.toJsonStr(exportDTO)).build());
    }

    @Override
    public AfterSaleVO getAfterSale(String sn) {
        AfterSaleVO afterSaleVO = this.baseMapper.getAfterSaleVO(sn);
        //判断是否存在缓存
        if (!cache.hasKey(TempCachePrefix.AFTER_SALE_CHECK.getPrefix(sn))) {
            //修改订单状态
            this.updateOrderAfterSaleApplying(afterSaleVO.getOrderSn());
            //缓存订单更新售后状态结果
            cache.put(TempCachePrefix.AFTER_SALE_CHECK.getPrefix(sn), sn, 600L);
        }
        return afterSaleVO;
    }

    @Override
    public AfterSaleApplyVO getAfterSaleVO(String sn) {

        AfterSaleApplyVO afterSaleApplyVO = new AfterSaleApplyVO();

        //获取订单货物判断是否可申请售后
        OrderItem orderItem = orderItemService.getBySn(sn);

        //未申请售后订单货物或部分售后才能进行申请
        if (!orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.NOT_APPLIED.name()) && !orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.PART_AFTER_SALE.name())) {
            throw new ServiceException(ResultCode.AFTER_SALES_BAN);
        }

        //获取售后类型
        Order order = orderService.getBySn(orderItem.getOrderSn());

        //订单未支付，不能申请申请售后
        if (order.getPaymentMethod() == null) {
            throw new ServiceException(ResultCode.AFTER_SALES_NOT_PAY_ERROR);
        }
        //判断支付方式是否为线上支付
        if (order.getPaymentMethod().equals(PaymentMethodEnum.BANK_TRANSFER.name())) {
            afterSaleApplyVO.setRefundWay(AfterSaleRefundWayEnum.OFFLINE.name());
        } else {
            afterSaleApplyVO.setRefundWay(AfterSaleRefundWayEnum.ORIGINAL.name());
        }
        //判断订单类型，虚拟订单只支持退款 || 未发货订单只支持退款
        if (order.getOrderType().equals(OrderTypeEnum.VIRTUAL.name()) || order.getDeliverStatus().equals(
                DeliverStatusEnum.UNDELIVERED.name())) {
            afterSaleApplyVO.setReturnMoney(true);
            afterSaleApplyVO.setReturnGoods(false);
        } else {
            afterSaleApplyVO.setReturnMoney(true);
            afterSaleApplyVO.setReturnGoods(true);
        }

        PriceDetailDTO priceDetailDTO = orderItem.getPriceDetailDTO();
        afterSaleApplyVO.setAccountType(order.getPaymentMethod());
        afterSaleApplyVO.setApplyRefundPrice(CurrencyUtil.div(CurrencyUtil.sub(priceDetailDTO.getFlowPrice(), priceDetailDTO.getFreightPrice()), orderItem.getNum()));
        afterSaleApplyVO.setNum(orderItem.getNum() - orderItem.getReturnNum());
        afterSaleApplyVO.setGoodsId(orderItem.getGoodsId());
        afterSaleApplyVO.setGoodsName(orderItem.getGoodsName());
        afterSaleApplyVO.setImage(orderItem.getImage());
        afterSaleApplyVO.setGoodsPrice(orderItem.getGoodsPrice());
        afterSaleApplyVO.setSkuId(orderItem.getSkuId());
        afterSaleApplyVO.setMemberId(order.getBuyerId());
        afterSaleApplyVO.setStoreId(order.getStoreId());
        return afterSaleApplyVO;
    }

    @Override
    @AfterSaleLogPoint(sn = "#rvt.sn", description = "'售后申请:售后编号['+#rvt.sn+']'")
    @SystemLogPoint(description = "售后-售后申请", customerLog = "'售后申请:售后编号['+#rvt.sn+']'")
    @Transactional
    public AfterSale saveAfterSale(AfterSaleDTO afterSaleDTO) {

        //检查当前订单是否可申请售后
        this.checkAfterSaleType(afterSaleDTO);

        //添加售后
        return addAfterSale(afterSaleDTO);
    }

    @Override
    @AfterSaleLogPoint(sn = "#rvt.sn", description = "'售后申请:售后编号['+#rvt.sn+']'")
    @SystemLogPoint(description = "售后-售后申请", customerLog = "'售后申请:售后编号['+#rvt.sn+']'")
    @Transactional
    public AfterSale fullRefund(String orderSn, String reason) {

        Order order = orderService.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        if(Boolean.TRUE.equals(order.getFullRefund())){
            throw new ServiceException(ResultCode.ORDER_CANCEL_ERROR);
        }
        AuthUser authUser = UserContext.getCurrentExistUser();

        AfterSale afterSale = new AfterSale();
    // 判断订单是否可以申请售后
    if (order.getPayStatus().equals(PayStatusEnum.PAID.name())
        && List.of(
                OrderStatusEnum.TAKE.name(),
                OrderStatusEnum.UNDELIVERED.name(),
                OrderStatusEnum.STAY_PICKED_UP.name(),
                OrderStatusEnum.PARTS_DELIVERED.name())
            .contains(order.getOrderStatus())) {
            afterSale.setOrderItemSn(orderSn);
            afterSale.setServiceType(AfterSaleTypeEnum.RETURN_MONEY.name());
            afterSale.setRefundWay(AfterSaleRefundWayEnum.ORIGINAL.name());
            afterSale.setReason(reason);
            afterSale.setApplyRefundPrice(order.getFlowPrice());
            afterSale.setActualRefundPrice(order.getFlowPrice());
            afterSale.setServiceOrderType(OrderTypeEnum.NORMAL.name());
            afterSale.setAccountType(order.getPaymentMethod());
            afterSale.setMemberId(order.getBuyerId());

            afterSale.setMemberId(authUser.getExtendId());
            afterSale.setMemberName(authUser.getNickName());

            afterSale.setStoreId(order.getStoreId());
            afterSale.setStoreName(order.getStoreName());
            afterSale.setIsProxy(order.getIsProxy());
            afterSale.setSupplierId(order.getSupplierId());

            //写入交易流水号
            afterSale.setTradeSn(order.getTradeSn());
            afterSale.setOrderSn(order.getSn());
            afterSale.setPayOrderNo(order.getOutTradeNo());
            afterSale.setFullRefund(true);
            afterSale.setServiceStatus(AfterSaleStatusEnum.APPLY.name());

            afterSale.setAccountType(order.getPaymentMethod());

            afterSale.setSn(SnowFlake.createStr("AS"));

            // 写入售后商品信息
            List<OrderItem> orderItems = orderItemService.getByOrderSn(orderSn);

            List<AfterSaleItem> afterSaleItems = orderItems.stream().map(orderItem -> {
                AfterSaleItem afterSaleItem = new AfterSaleItem();
                afterSaleItem.setGoodsId(orderItem.getGoodsId());
                afterSaleItem.setGoodsName(orderItem.getGoodsName());
                afterSaleItem.setGoodsImage(orderItem.getImage());
                afterSaleItem.setSkuId(orderItem.getSkuId());
                afterSaleItem.setNum(orderItem.getNum());
                afterSaleItem.setGoodsPrice(orderItem.getUnitPrice());
                return afterSaleItem;
            }).toList();

            afterSale.setAfterSaleItems(JSONUtil.toJsonStr(afterSaleItems));

            //添加售后
            this.save(afterSale);

            this.updateOrderItemAfterSaleStatus(afterSale);

            orderService.fullRefund(orderSn);
        }
        return afterSale;

    }
    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "'买家退货,物流填写:单号['+#afterSaleSn+']，物流单号为['+#logisticsNo+']'")
    @SystemLogPoint(description = "售后-买家退货,物流填写", customerLog = "'买家退货,物流填写:单号['+#afterSaleSn+']，物流单号为['+#logisticsNo+']'")
    @Override
    @Transactional
    public AfterSale buyerDelivery(String afterSaleSn, String logisticsNo, String logisticsId, Date mDeliverTime) {

        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);

        //判断为已审核通过，待邮寄的售后服务
        if (!afterSale.getServiceStatus().equals(AfterSaleStatusEnum.PASS.name())) {
            throw new ServiceException(ResultCode.AFTER_STATUS_ERROR);
        }

        //查询会员回寄的物流公司信息
        Logistics logistics = logisticsClient.getById(logisticsId);

        //判断物流公司是否为空
        if (logistics == null) {
            throw new ServiceException(ResultCode.AFTER_STATUS_ERROR);
        }

        afterSale.setMLogisticsCode(logistics.getId());
        afterSale.setMLogisticsName(logistics.getName());
        afterSale.setMLogisticsNo(logisticsNo);
        afterSale.setMDeliverTime(mDeliverTime);
        //修改售后单状态
        afterSale.setServiceStatus(AfterSaleStatusEnum.BUYER_RETURN.name());

        //根据售后编号修改售后单
        this.updateAfterSale(afterSale);
        return afterSale;
    }

    @Override
    public Traces deliveryTraces(String afterSaleSn) {

        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);
        return logisticsClient.getLogistic(afterSale.getMLogisticsCode(), afterSale.getMLogisticsNo(), afterSale.getMLogisticsNo());
    }

    @Override
    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "'售后-平台退款:单号['+#afterSaleSn+']，备注为['+#remark+']'")
    @SystemLogPoint(description = "售后-手动团平台退款", customerLog = "'售后-平台退款:单号['+#afterSaleSn+']，备注为['+#remark+']'")
    @Transactional
    public AfterSale refund(String afterSaleSn, String remark) {
        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);
        afterSale.setServiceStatus(AfterSaleStatusEnum.COMPLETE.name());

        //根据售后编号修改售后单
        this.updateAfterSale(afterSale);

        //平台退款
        paymentRefund(afterSale);
        //发送退款消息
        AfterSaleMessageDTO afterSaleMessageDTO = new AfterSaleMessageDTO();
        BeanUtil.copyProperties(afterSale, afterSaleMessageDTO);
        this.sendAfterSaleMessage(afterSaleMessageDTO);
        // 修改订单状态
        updateOrderAfterSaleApplying(afterSale.getOrderSn());
        return afterSale;
    }


    /**
     * 调用退款
     *
     * @param afterSale 售后
     */
    private void paymentRefund(AfterSale afterSale) {
        OrderItem orderItem = orderItemService.getBySn(afterSale.getOrderItemSn());
        Order order = orderService.getBySn(afterSale.getOrderSn());

        //记录退款金额
        Double actualRefundPrice = afterSale.getApplyRefundPrice();
        //根据售后状态。修改OrderItem订单中正在售后商品数量及状态
        refundFlowService.generatorRefundFlow(afterSale, orderItem, order);

        // 更新售后退款时间
        afterSale.setRefundTime(new Date());
        // 如果实际退款金额不等于申请退款金额，则修改退款金额
        if (!actualRefundPrice.equals(afterSale.getActualRefundPrice())) {
            this.updateById(afterSale);
        }

    }

    @Override
    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "'售后-买家确认解决:单号['+#afterSaleSn+']'")
    @SystemLogPoint(description = "售后-买家确认解决", customerLog = "'售后-买家确认解决:单号['+#afterSaleSn+']'")
    @Transactional
    public AfterSale complete(String afterSaleSn) {
        AfterSale afterSale = this.getBySn(afterSaleSn);
        afterSale.setServiceStatus(AfterSaleStatusEnum.COMPLETE.name());
        this.updateAfterSale(afterSale);
        // 修改订单状态
        updateOrderAfterSaleApplying(afterSale.getOrderSn());
        return afterSale;
    }

    @Override
    public List<AfterSale> queryByOrderSn(String orderSn) {
        return this.list(new LambdaQueryWrapper<AfterSale>().eq(AfterSale::getOrderSn, orderSn));
    }

    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "#serviceStatus.equals('PASS')?" +
            "'审核售后:售后编号['+#afterSaleSn+'],处理结果[通过]':'审核售后:售后编号['+#afterSaleSn+'],处理结果[拒绝],原因['+#remark+']'")
    @SystemLogPoint(description = "售后-商家收货", customerLog = "#serviceStatus.equals('PASS')?" +
            "'审核售后:售后编号['+#afterSaleSn+'],处理结果[通过]':'审核售后:售后编号['+#afterSaleSn+'],处理结果[拒绝],原因['+#remark+']'")
    @Override
    @Transactional
    public AfterSale review(String afterSaleSn, String serviceStatus, String remark, String refundAddressId) {
        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);

        //判断为待审核的售后服务
        if (!afterSale.getServiceStatus().equals(AfterSaleStatusEnum.APPLY.name())) {
            throw new ServiceException(ResultCode.AFTER_SALES_DOUBLE_ERROR);
        }

        //强制退款金额=售后申请金额
        afterSale.setActualRefundPrice(afterSale.getApplyRefundPrice());

        //判断审核状态
        //如果售后类型为：退款，审核状态为已通过并且退款方式为原路退回，售后单状态为已完成。
        //如果售后类型为：退款，审核状态已通过并且退款方式为线下退回，售后单状态为待退款。
        //如果售后类型不为退款，售后单状态为：已通过。
        AfterSaleStatusEnum afterSaleStatusEnum;
        //审核通过判定
        if (serviceStatus.equals(AfterSaleStatusEnum.PASS.name())) {
            //如果为退款操作 && 原路支付 则售后单状态为已完成
            if (afterSale.getServiceType().equals(AfterSaleTypeEnum.RETURN_MONEY.name()) && afterSale.getRefundWay().equals(AfterSaleRefundWayEnum.ORIGINAL.name())) {
                //调用退款
                paymentRefund(afterSale);

                //修改售后单状态为已完成
                afterSaleStatusEnum = AfterSaleStatusEnum.COMPLETE;
            }
            // 如果退货操作 && 线下支付 则售后单状态为待退款
            else if (afterSale.getServiceType().equals(AfterSaleTypeEnum.RETURN_MONEY.name())) {
                afterSaleStatusEnum = AfterSaleStatusEnum.WAIT_REFUND;
            }
            //否则为退货操作 审核通过，则标记为申请通过，等待买家发货
            else {
//                AddressSearchParams addressSearchParams = new AddressSearchParams();
//                addressSearchParams.setExtendId(afterSale.getStoreId());
//                addressSearchParams.setIsDefault(true);
//                addressSearchParams.setType(AddressTypeEnum.RECEIVE);
//                UserAddress userAddress = addressClient.getByParams(addressSearchParams);
//                if (userAddress == null) {
//                    throw new ServiceException(ResultCode.RECEIVE_ADDRESS_NOT_HAVE);
//                }
                afterSale.setRefundAddressId(refundAddressId);
                afterSaleStatusEnum = AfterSaleStatusEnum.PASS;
            }
        } else {
            // 审核不通过
            afterSaleStatusEnum = AfterSaleStatusEnum.REFUSE;
        }

        afterSale.setServiceStatus(afterSaleStatusEnum.name());
        afterSale.setAuditRemark(remark);

        //根据售后编号修改售后单
        this.updateAfterSale(afterSale);
        //根据售后状态。修改OrderItem订单中正在售后商品数量及状态
        this.updateOrderItemAfterSaleStatus(afterSale);
        // 修改订单状态
        updateOrderAfterSaleApplying(afterSale.getOrderSn());
        //发送售后消息
        this.sendAfterSaleMessage(afterSale);

        return afterSale;
    }


    @Override
    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "#serviceStatus.equals('PASS')?" +
            "'售后-商家收货:单号['+#afterSaleSn+'],处理结果[商家收货]':'售后-商家收货:单号['+#afterSaleSn+'],处理结果[商家拒收],原因['+#remark+']'")
    @SystemLogPoint(description = "售后-商家收货", customerLog = "#serviceStatus.equals('PASS')?" +
            "'售后-商家收货:单号['+#afterSaleSn+'],处理结果[商家收货]':'售后-商家收货:单号['+#afterSaleSn+'],处理结果[商家拒收],原因['+#remark+']'")
    @Transactional
    public AfterSale storeConfirm(String afterSaleSn, String serviceStatus, String remark) {
        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);
        SceneHelp.objectAuthentication(afterSale, ObjectFieldEnum.STORE_ID, ObjectFieldEnum.MANAGER_ID, ObjectFieldEnum.SUPPLIER_ID);
        //判断是否为已邮寄售后单
        if (!afterSale.getServiceStatus().equals(AfterSaleStatusEnum.BUYER_RETURN.name())) {
            throw new ServiceException(ResultCode.AFTER_STATUS_ERROR);
        }
        AfterSaleStatusEnum afterSaleStatusEnum;
        //判断审核状态
        //在线支付 则直接进行退款
        if (AfterSaleStatusEnum.PASS.name().equals(serviceStatus) && afterSale.getRefundWay().equals(AfterSaleRefundWayEnum.ORIGINAL.name())) {
            //调用退款
            paymentRefund(afterSale);
            afterSaleStatusEnum = AfterSaleStatusEnum.COMPLETE;
        } else if (AfterSaleStatusEnum.PASS.name().equals(serviceStatus)) {
            afterSaleStatusEnum = AfterSaleStatusEnum.WAIT_REFUND;
        } else {
            afterSaleStatusEnum = AfterSaleStatusEnum.SELLER_TERMINATION;
        }
        afterSale.setServiceStatus(afterSaleStatusEnum.name());
        afterSale.setAuditRemark(remark);

        //根据售后编号修改售后单
        this.updateAfterSale(afterSale);
        //根据售后状态。修改OrderItem订单中正在售后商品数量及状态
        this.updateOrderItemAfterSaleStatus(afterSale);
        // 修改订单状态
        updateOrderAfterSaleApplying(afterSale.getOrderSn());
        //发送售后消息
        this.sendAfterSaleMessage(afterSale);
        return afterSale;
    }

    @Override
    public void getAfterCount(OrderStatusStatisticsDTO orderStatusStatisticsDTO) {

        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        long count = this.count(new LambdaQueryWrapper<AfterSale>().eq(AfterSale::getMemberId, currentUser.getId()).in(AfterSale::getServiceStatus, AfterSaleStatusEnum.APPLY.name(), AfterSaleStatusEnum.BUYER_RETURN.name(), AfterSaleStatusEnum.PASS.name()));
        orderStatusStatisticsDTO.setAfterSaleCount(count);
    }

    @Override
    public void forceEndAfterSale() {
        Setting setting = settingClient.get(SettingEnum.ORDER_SETTING.name());
        //订单设置
        OrderSetting orderSetting = JSONUtil.toBean(setting.getSettingValue(), OrderSetting.class);
        if (orderSetting == null) {
            throw new ServiceException(ResultCode.ORDER_SETTING_ERROR);
        }
        //订单关闭售后申请时间 = 当前时间 - 自动关闭售后申请天数
        DateTime receiveTime = DateUtil.offsetDay(DateUtil.date(), -orderSetting.getAfterSaleForceEnd());

        AfterSaleForceEndParams afterSaleForceEndParams = new AfterSaleForceEndParams();

        afterSaleForceEndParams.setEndTime(receiveTime);

        //如果订单设置中的自动关闭售后申请天数为0，则默排查近7天的售后单
        if (orderSetting.getAfterSaleForceEnd() == 0) {
            afterSaleForceEndParams.setAfterSaleForceEnd(7);
        } else {
            afterSaleForceEndParams.setAfterSaleForceEnd(orderSetting.getAfterSaleForceEnd());
        }

        // 获取待售后订单
        List<AfterSale> afterSales = this.list(afterSaleForceEndParams.queryWrapper());
        //判断是否有符合条件的售后，关闭允许售后申请处理
        if (!afterSales.isEmpty()) {
            try {
                afterSales.forEach(afterSale ->
                    review(afterSale.getSn(), AfterSaleStatusEnum.REFUSE.name(), "处理超时,系统结束售后申请", null)
                );
            } catch (Exception e) {
                log.error("关闭允许售后申请失败", e);
            }
        }
    }

    @Override
    public Boolean afterSaleUnComplete(String orderSn) {
        return this.count(new LambdaQueryWrapper<AfterSale>()
                .eq(AfterSale::getOrderSn, orderSn)
                .in(AfterSale::getServiceStatus, AfterSaleStatusEnum.APPLY.name(), AfterSaleStatusEnum.PASS.name(),
                        AfterSaleStatusEnum.BUYER_RETURN.name(), AfterSaleStatusEnum.SELLER_CONFIRM.name(),
                        AfterSaleStatusEnum.WAIT_REFUND.name()))
                > 0;
    }

    /**
     * 获取售后未完成的售后申请单
     */
    private List<AfterSale> unComplete(String orderSn) {
        return this.list(new LambdaQueryWrapper<AfterSale>()
                .eq(AfterSale::getOrderSn, orderSn)
                .in(AfterSale::getServiceStatus, AfterSaleStatusEnum.APPLY.name(), AfterSaleStatusEnum.PASS.name(),
                        AfterSaleStatusEnum.BUYER_RETURN.name(), AfterSaleStatusEnum.SELLER_CONFIRM.name(),
                        AfterSaleStatusEnum.WAIT_REFUND.name()));
    }
    @Override
    @AfterSaleLogPoint(sn = "#afterSaleSn", description = "'售后-买家取消:单号['+#afterSaleSn+']'")
    @SystemLogPoint(description = "售后-取消售后", customerLog = "'售后-买家取消:单号['+#afterSaleSn+']'")
    @Transactional
    public AfterSale cancel(String afterSaleSn) {

        //根据售后单号获取售后单
        AfterSale afterSale = this.getBySn(afterSaleSn);

        //判断售后单是否可以申请售后
        //如果售后状态为：待审核、已通过则可进行申请售后
        if (afterSale.getServiceStatus().equals(AfterSaleStatusEnum.APPLY.name()) || afterSale.getServiceStatus().equals(AfterSaleStatusEnum.PASS.name())) {

            afterSale.setServiceStatus(AfterSaleStatusEnum.BUYER_CANCEL.name());

            //根据售后编号修改售后单
            this.updateAfterSale(afterSale);
            //根据售后状态。修改OrderItem订单中正在售后商品数量及状态
            this.updateOrderItemAfterSaleStatus(afterSale);
            // 全退订单需将标记还原
            if (Boolean.TRUE.equals(afterSale.getFullRefund())) {
                orderService.revokeFullRefund(afterSale.getOrderSn());
            }
            // 修改订单状态
            updateOrderAfterSaleApplying(afterSale.getOrderSn());
            return afterSale;
        }else{
            throw new ServiceException(ResultCode.AFTER_SALES_DOUBLE_ERROR);
        }
    }

    @Override
    public long applyNum(String serviceType) {
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        LambdaQueryWrapper<AfterSale> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AfterSale::getServiceStatus, AfterSaleStatusEnum.APPLY.name());
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(serviceType), AfterSale::getServiceType, serviceType);
        queryWrapper.eq(CharSequenceUtil.equals(authUser.getScene().name(), SceneEnums.STORE.name()), AfterSale::getStoreId, authUser.getExtendId());
        return this.count(queryWrapper);
    }

    @Override
    public UserAddress getStoreAfterSaleAddressDTO(String sn) {
        AfterSale afterSale = this.getBySn(sn);
//        AddressSearchParams addressSearchParams = new AddressSearchParams();
//        // 判断商品是否为代发商品
//        if (Boolean.TRUE.equals(afterSale.getIsProxy())) {
//            // 代发商品需读取所属供应商的回寄地址
//            addressSearchParams.setExtendId(afterSale.getSupplierId());
//            addressSearchParams.setScene(SceneEnums.SUPPLIER);
//        } else {
//            addressSearchParams.setExtendId(afterSale.getStoreId());
//            addressSearchParams.setScene(SceneEnums.STORE);
//        }
//        addressSearchParams.setType(AddressTypeEnum.RECEIVE);
//        return addressClient.getByParams(addressSearchParams);
        UserAddress result = new UserAddress();

        RefundAddress refundAddress = refundAddressClient.getRefundAddress(afterSale.getRefundAddressId());
        result.setName(refundAddress.getName());
        result.setMobile(refundAddress.getPhone());
        result.setConsigneeAddressPath(refundAddress.getAddressPath());
        result.setConsigneeAddressIdPath(refundAddress.getAddressIdPath());
        result.setDetail(refundAddress.getDetailAddress());

        return result;
    }

    @Override
    public Page<AfterSale> getStatistics(StatisticsQueryParam statisticsQueryParam, PageVO pageVO) {

        LambdaQueryWrapper<AfterSale> queryWrapper = new LambdaQueryWrapper<>();
        Date[] dates = StatisticsDateUtil.getDateArray(statisticsQueryParam);
        queryWrapper.between(AfterSale::getCreateTime, dates[0], dates[1]);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(statisticsQueryParam.getStoreId()), AfterSale::getStoreId, statisticsQueryParam.getStoreId());

        return this.page(PageUtil.initPage(pageVO), queryWrapper);
    }

    /**
     * 创建售后
     *
     * @param afterSaleDTO 售后
     * @return 售后
     */
    private AfterSale addAfterSale(AfterSaleDTO afterSaleDTO) {
        //写入其他属性
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());

        AfterSale afterSale = new AfterSale();
        BeanUtil.copyProperties(afterSaleDTO, afterSale);

        //写入会员信息
        afterSale.setMemberId(authUser.getExtendId());
        afterSale.setMemberName(authUser.getNickName());

        //写入商家信息
        OrderItem orderItem = orderItemService.getBySn(afterSaleDTO.getOrderItemSn());
        Order order = orderService.getBySn(orderItem.getOrderSn());
        afterSale.setStoreId(order.getStoreId());
        afterSale.setStoreName(order.getStoreName());
        afterSale.setIsProxy(order.getIsProxy());
        afterSale.setSupplierId(order.getSupplierId());


        //写入交易流水号
        afterSale.setTradeSn(order.getTradeSn());
        afterSale.setOrderSn(order.getSn());
        afterSale.setPayOrderNo(order.getOutTradeNo());
        afterSale.setOrderItemSn(orderItem.getSn());

        //写入状态
        afterSale.setServiceStatus(AfterSaleStatusEnum.APPLY.name());

        //创建售后单号
        afterSale.setSn(SnowFlake.createStr("A"));

        //是否包含图片
        if (afterSaleDTO.getImages() != null) {
            afterSale.setAfterSaleImage(afterSaleDTO.getImages());
        }

        AfterSaleItem afterSaleItem = new AfterSaleItem();
        //写入订单商品信息
        afterSaleItem.setGoodsImage(orderItem.getImage());
        afterSaleItem.setGoodsName(orderItem.getGoodsName());
        afterSaleItem.setSpecs(orderItem.getSpecs());
        afterSaleItem.setNum(afterSaleDTO.getNum());
        afterSaleItem.setGoodsId(orderItem.getGoodsId());
        afterSaleItem.setSkuId(orderItem.getSkuId());
        afterSaleItem.setGoodsPrice(orderItem.getUnitPrice());
        afterSale.setAfterSaleItems(JSONUtil.toJsonStr(Collections.singletonList(afterSaleItem)));

        if (afterSaleDTO.getNum().equals(orderItem.getNum())) {
            Double refundPrice = CurrencyUtil.sub(orderItem.getPriceDetailDTO().getFlowPrice(), orderItem.getPriceDetailDTO().getFreightPrice());
            if (null != orderItem.getServiceFee() && order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) && order.getPayStatus().equals(PayStatusEnum.PAID.name())) {
                refundPrice = CurrencyUtil.sub(refundPrice, orderItem.getServiceFee());
            }
            //计算退回金额
            afterSale.setApplyRefundPrice(refundPrice);
        } else {
            Double refundPrice = CurrencyUtil.sub(orderItem.getPriceDetailDTO().getFlowPrice(), orderItem.getPriceDetailDTO().getFreightPrice());
            if (null != orderItem.getServiceFee()  && order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) && order.getPayStatus().equals(PayStatusEnum.PAID.name())) {
                refundPrice = CurrencyUtil.sub(refundPrice, orderItem.getServiceFee());
            }
            //单价计算
            double utilPrice = CurrencyUtil.div(refundPrice, orderItem.getNum());
            afterSale.setApplyRefundPrice(CurrencyUtil.mul(afterSaleDTO.getNum(), utilPrice));
        }
        //添加售后
        this.save(afterSale);
        //发送售后消息
        AfterSaleMessageDTO afterSaleMessageDTO = new AfterSaleMessageDTO();
        BeanUtil.copyProperties(afterSale, afterSaleMessageDTO);
        this.sendAfterSaleMessage(afterSaleMessageDTO);
        //根据售后状态。修改OrderItem订单中正在售后商品数量及状态
        this.updateOrderItemAfterSaleStatus(afterSale);
        //设置订单存在售后
        this.updateOrderAfterSaleApplying(orderItem.getOrderSn());
        return afterSale;
    }

    /**
     * 修改OrderItem订单中正在售后的商品数量及OrderItem订单状态
     *
     * <AUTHOR>
     */
    private void updateOrderItemAfterSaleStatus(AfterSale afterSale) {
        // 根据售后状态。修改OrderItem订单中正在售后商品数量及状态
        AfterSaleStatusEnum afterSaleStatusEnum = AfterSaleStatusEnum.valueOf(afterSale.getServiceStatus());
        // 整单售后则需要对每个订单项进行修改
        if (Boolean.TRUE.equals(afterSale.getFullRefund())) {
            List<OrderItem> orderItemList = orderItemService.getByOrderSn(afterSale.getOrderSn());
            switch (afterSaleStatusEnum) {
                //判断当前售后的状态---申请中
                case APPLY: {
                    orderItemList.forEach(orderItem -> orderItem.setReturnNum(orderItem.getNum()));
                    break;
                }
                //判断当前售后的状态---已拒绝,买家取消售后,卖家终止售后
                case REFUSE, BUYER_CANCEL, SELLER_TERMINATION: {
                    orderItemList.forEach(orderItem -> orderItem.setReturnNum(0));
                    orderService.revokeFullRefund(afterSale.getOrderSn());
                    break;
                }
                default:
                    break;
            }
            //修改orderItem订单
            this.updateOrderItem(orderItemList);
        } else {
            //根据商品skuId及订单sn获取子订单
            OrderItem orderItem = orderItemService.getOne(
                new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderSn, afterSale.getOrderSn())
                    .eq(OrderItem::getSkuId, afterSale.getAfterSaleItemArray().getFirst().getSkuId()));

            if (orderItem == null) {
                throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
            }

            switch (afterSaleStatusEnum) {
                //判断当前售后的状态---申请中
                case APPLY: {
                    orderItem.setReturnNum(orderItem.getReturnNum() + afterSale.getAfterSaleItemArray().getFirst().getNum());
                    break;
                }
                //判断当前售后的状态---已拒绝,买家取消售后,卖家终止售后
                case REFUSE, BUYER_CANCEL, SELLER_TERMINATION: {
                    orderItem.setReturnNum(orderItem.getReturnNum() - afterSale.getAfterSaleItemArray().getFirst().getNum());
                    break;
                }
                default:
                    break;
            }
            //修改orderItem订单
            this.updateOrderItem(orderItem);
        }
    }


    /**
     * 检查当前订单状态是否为可申请当前售后类型的状态
     *
     * @param afterSaleDTO 售后
     */
    private void checkAfterSaleType(AfterSaleDTO afterSaleDTO) {

        //判断数据是否为空
        if (null == afterSaleDTO || CharSequenceUtil.isEmpty(afterSaleDTO.getOrderItemSn())) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }

        //获取订单货物判断是否可申请售后
        OrderItem orderItem = orderItemService.getBySn(afterSaleDTO.getOrderItemSn());

        //未申请售后或部分售后订单货物才能进行申请
        if (!orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.NOT_APPLIED.name()) && !orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.PART_AFTER_SALE.name())) {
            throw new ServiceException(ResultCode.AFTER_SALES_BAN);
        }

        //申请商品数量不能超过商品总数量-售后商品数量
        int canReturnNum = orderItem.getReturnNum() != null ? orderItem.getNum() - orderItem.getReturnNum() : orderItem.getNum();
        if (afterSaleDTO.getNum() > canReturnNum) {
            throw new ServiceException(ResultCode.AFTER_GOODS_NUMBER_ERROR, ResultCode.AFTER_GOODS_NUMBER_ERROR.appendMessage(",可申请数量为：" + canReturnNum));
        }


        //获取售后类型
        Order order = orderService.getBySn(orderItem.getOrderSn());
        // 虚拟订单或自提订单有额外校验步骤
        if (OrderTypeEnum.unDelivery(order)) {
            List<VerificationCode> verificationCodes =
                    verificationCodeService.getVerificationCodeList(orderItem.getOrderSn());
            // 还可以核销的数量
            canReturnNum = Math.toIntExact(verificationCodes.stream()
                    .filter(verificationCode -> verificationCode.getStatus()
                            .equals(VerificationStatusEnum.UNUSED.name())|| verificationCode.getStatus()
                            .equals(VerificationStatusEnum.VERIFIED.name())).count());
            //除了判定这次申请的数量不能超过可核销的数量，还需要判定申请中的
            List<AfterSale> afterSales = this.unComplete(order.getSn());
            // 申请中的数量
            if (afterSales != null && !afterSales.isEmpty()) {
                for (AfterSale afterSale : afterSales) {
                    List<AfterSaleItem> afterSaleItems = afterSale.getAfterSaleItemArray();
                    for (AfterSaleItem afterSaleItem : afterSaleItems) {
                        canReturnNum -= afterSaleItem.getNum();
                    }
                }
            }

            // 申请商品数量不能超过商品总数量-售后商品数量
            if (afterSaleDTO.getNum() > canReturnNum) {
                if (canReturnNum <= 0) {
                    throw new ServiceException(ResultCode.AFTER_GOODS_NUMBER_ERROR,
                            ResultCode.AFTER_GOODS_NUMBER_ERROR.appendMessage("该订单当前无法申请售后，请联系客服"));
                }
                throw new ServiceException(ResultCode.AFTER_GOODS_NUMBER_ERROR,
                        ResultCode.AFTER_GOODS_NUMBER_ERROR.appendMessage(",可申请数量为：" + canReturnNum));
            }
        }

        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.valueOf(afterSaleDTO.getServiceType());
        switch (afterSaleTypeEnum) {
            case RETURN_MONEY -> {
                //只处理已付款的售后
                if (!PayStatusEnum.PAID.name().equals(order.getPayStatus())) {
                    throw new ServiceException(ResultCode.AFTER_SALES_BAN);
                }
                //如果为线下支付银行信息不能为空
                if (AfterSaleRefundWayEnum.OFFLINE.name().equals(afterSaleDTO.getRefundWay())) {
                    boolean emptyBankParam =
                        CharSequenceUtil.isEmpty(afterSaleDTO.getBankDepositName()) || CharSequenceUtil.isEmpty(
                            afterSaleDTO.getBankAccountName()) || CharSequenceUtil.isEmpty(
                            afterSaleDTO.getBankAccountNumber());
                    if (emptyBankParam) {
                        throw new ServiceException(ResultCode.RETURN_MONEY_OFFLINE_BANK_ERROR);
                    }

                }
            }
            case RETURN_GOODS -> {
                //是否为有效状态
                boolean availableStatus = CharSequenceUtil.equalsAny(order.getOrderStatus(), OrderStatusEnum.DELIVERED.name(),
                        OrderStatusEnum.COMPLETED.name());
                if (!PayStatusEnum.PAID.name().equals(order.getPayStatus()) && availableStatus) {
                    throw new ServiceException(ResultCode.AFTER_SALES_BAN);
                }
            }
        }

    }

    /**
     * 根据sn获取信息
     *
     * @param sn 订单sn
     * @return 售后信息
     */
    private AfterSale getBySn(String sn) {
        QueryWrapper<AfterSale> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        if (UserContext.getCurrentUser() != null) {
            switch (Objects.requireNonNull(UserContext.getCurrentUser()).getScene()) {
                case MEMBER -> queryWrapper.eq("member_id", UserContext.getCurrentUser().getExtendId());
                case SUPPLIER ->
                        queryWrapper.nested(n -> n.eq("supplier_id", UserContext.getCurrentUser().getExtendId()).or().eq("store_id",
                                UserContext.getCurrentUser().getExtendId()));
                case STORE ->
                        queryWrapper.nested(n -> n.eq("store_id", UserContext.getCurrentUser().getExtendId()).or().eq("member_id",
                                UserContext.getCurrentUser().getExtendId()));
                default -> {
                }
            }
        }
        return this.getOne(queryWrapper);
    }

    /**
     * 根据售后编号修改售后单
     *
     * @param afterSale   售后单
     */
    private void updateAfterSale(AfterSale afterSale) {
        //修改售后单状态
        LambdaUpdateWrapper<AfterSale> queryWrapper = Wrappers.lambdaUpdate();
        queryWrapper.eq(AfterSale::getSn, afterSale.getSn());

        // 售后完成时，如果是虚拟订单，则需要销毁核销码
        if (afterSale.getServiceStatus().equals(AfterSaleStatusEnum.COMPLETE.name())) {
            Order order = orderService.getBySn(afterSale.getOrderSn());
            // 订单为核销订单，并且订单未完成的情况下，对订单进行销毁
            if (OrderTypeEnum.unDelivery(order)) {
                verificationCodeService.destruction(afterSale.getOrderSn(),
                        afterSale.getAfterSaleItemArray().getFirst().getSkuId(),
                        afterSale.getAfterSaleItemArray().getFirst().getNum());
            }

        }

        this.update(afterSale, queryWrapper);
    }

    /**
     * 发送售后消息
     *
     * @param afterSale 售后对象
     */
    private void sendAfterSaleMessage(AfterSale afterSale) {
        applicationEventPublisher.publishEvent(
            TransactionCommitSendMQEvent.builder()
                .source("售后状态变更")
                .exchange(amqpExchangeProperties.getAfterSale())
                .routingKey(AfterSaleRoutingKey.AFTER_SALE_STATUS_CHANGE)
                .message(afterSale)
                .build());
    }

    /**
     * 发送售后消息
     *
     * @param afterSaleMessageDTO 售后消息DTO
     */
    private void sendAfterSaleMessage(AfterSaleMessageDTO afterSaleMessageDTO) {
        applicationEventPublisher.publishEvent(
            TransactionCommitSendMQEvent.builder()
                .source("售后状态变更")
                .exchange(amqpExchangeProperties.getAfterSale())
                .routingKey(AfterSaleRoutingKey.AFTER_SALE_STATUS_CHANGE)
                .message(afterSaleMessageDTO)
                .build());
    }

    /**
     * 功能描述:  修改orderItem订单
     *
     * @param orderItem 售后订单
     * <AUTHOR>
     **/
    private void updateOrderItem(OrderItem orderItem) {
        //订单状态不能为新订单,已失效订单或未申请订单才可以去修改订单信息
        OrderItemAfterSaleStatusEnum afterSaleTypeEnum = OrderItemAfterSaleStatusEnum.valueOf(orderItem.getAfterSaleStatus());
        switch (afterSaleTypeEnum) {
            //售后状态为：未申请 部分售后 已申请
            case NOT_APPLIED, PART_AFTER_SALE, ALREADY_APPLIED -> {
                //通过正在售后商品总数修改订单售后状态
                if (orderItem.getReturnNum().equals(orderItem.getNum())) {
                    //修改订单的售后状态--已申请
                    orderItem.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.ALREADY_APPLIED.name());
                } else if (orderItem.getReturnNum().equals(0)) {
                    //修改订单的售后状态--未申请
                    orderItem.setAfterSaleStatus(PromotionTypeEnum.isCanAfterSale(orderItem.getPromotionType()) ? OrderItemAfterSaleStatusEnum.NOT_APPLIED.name() : OrderItemAfterSaleStatusEnum.EXPIRED.name());
                } else {
                    //修改订单的售后状态--部分售后
                    orderItem.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.PART_AFTER_SALE.name());
                }
            }
            default -> {
            }
        }
        orderItemService.update(new LambdaUpdateWrapper<OrderItem>().eq(OrderItem::getSn, orderItem.getSn()).set(OrderItem::getAfterSaleStatus,
                orderItem.getAfterSaleStatus()).set(OrderItem::getReturnNum, orderItem.getReturnNum()));
    }

    /**
     * 功能描述:  修改orderItem订单
     *
     * @param orderItems 售后订单
     * <AUTHOR>
     **/
    private void updateOrderItem(List<OrderItem> orderItems) {
        orderItems.forEach(orderItem -> {
            if (orderItem.getReturnNum() > 0) {
                orderItem.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.ALREADY_APPLIED.name());
            } else {
                orderItem.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.NEW.name());
            }
        });
        orderItemService.updateBatchById(orderItems);
    }

    /**
     * 功能描述:  修改订单状态为售后申请中
     *
     * @param orderSn 订单编号
     */
    private void updateOrderAfterSaleApplying(String orderSn) {

        //查看此订单是否存在未完成的售后
        long count = this.count(new LambdaQueryWrapper<AfterSale>().eq(AfterSale::getOrderSn, orderSn)
            .in(AfterSale::getServiceStatus, AfterSaleStatusEnum.APPLY.name(), AfterSaleStatusEnum.PASS.name(),
                AfterSaleStatusEnum.BUYER_RETURN.name(), AfterSaleStatusEnum.WAIT_REFUND.name(),
                AfterSaleStatusEnum.SELLER_CONFIRM.name()));
        orderService.setAfterSaleApplying(orderSn, count > 0);
    }

    @Override
    public Double getReturnSuccessRate(String storeId) {
        return this.baseMapper.getReturnSuccessRate(storeId);
    }

}
