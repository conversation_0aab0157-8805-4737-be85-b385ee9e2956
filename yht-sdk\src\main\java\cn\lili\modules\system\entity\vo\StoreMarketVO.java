package cn.lili.modules.system.entity.vo;

import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dos.StoreMarket;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@Data
@NoArgsConstructor
public class StoreMarketVO extends StoreMarket {



    String street;

    public StoreMarketVO(StoreMarket bean) {
        BeanUtil.copyProperties(bean, this);
    }

    public StoreMarketVO(StoreMarket bean, String street) {
        BeanUtil.copyProperties(bean, this);
        this.street = street;
    }

}


