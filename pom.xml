<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!--spring cloud alibaba parent-->
    <parent>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-build</artifactId>
        <version>4.1.0</version>
        <relativePath/>
    </parent>

    <groupId>cn.lili.sbc</groupId>
    <artifactId>yht-sbc</artifactId>
    <version>1.0.2</version>

    <modules>
        <module>gateway</module>
        <module>framework</module>
        <module>yht-sdk</module>
        <module>service</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <revision>latest</revision>

        <!-- 日志路径 -->
        <log.path>./logs</log.path>

        <!-- Project revision -->
        <spring.cloud.alibaba.revision>2023.0.1.2</spring.cloud.alibaba.revision>

        <!-- Spring Cloud -->
        <spring.cloud.version>2023.0.1</spring.cloud.version>

        <!--镜像地址-->
        <docker-registry>harbor.dllll.xyz/sbc</docker-registry>

        <!-- Maven Plugin Versions -->
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
        <maven-source-plugin.version>2.2.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.3.0</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <gmavenplus-plugin.version>1.6</gmavenplus-plugin.version>
        <jacoco.version>0.8.3</jacoco.version>

        <!--项目其他基础依赖-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>true</skipTests>
        <maven.javadoc.skip>true</maven.javadoc.skip>

        <!-- 依赖版本 -->
        <jakarta.servlet.version>6.1.0-M2</jakarta.servlet.version>
        <springdoc-openapi.version>2.6.0</springdoc-openapi.version>
        <shardingsphere.version>5.4.1</shardingsphere.version>
        <mysql-connector.version>8.3.0</mysql-connector.version>
        <alipay-sdk-version>4.39.31.ALL</alipay-sdk-version>
        <mybatis-plus-version>3.5.6</mybatis-plus-version>
        <Hutool-version>5.8.27</Hutool-version>
        <minio-version>8.5.9</minio-version>
        <redisson>3.17.4</redisson>
        <guava>33.1.0-jre</guava>
        <aliyun-version>4.6.4</aliyun-version>
        <aliyun-sdk-oss-version>3.17.4</aliyun-sdk-oss-version>
        <aliyun-sdk-dysms-version>2.0.24</aliyun-sdk-dysms-version>
        <jwt-version>0.12.5</jwt-version>
        <druid-version>1.2.22</druid-version>
        <simple-http-version>1.0.5</simple-http-version>
        <beetl-version>3.16.0.RELEASE</beetl-version>
        <poi-version>5.2.5</poi-version>
        <logstash-logback-encoder>7.4</logstash-logback-encoder>
        <zxing>3.5.3</zxing>
        <slf4j-api>2.0.12</slf4j-api>
        <xk-time>3.2.4</xk-time>
        <commons-text>1.11.0</commons-text>
        <xxl-job>2.4.0</xxl-job>
        <lombok-version>1.18.32</lombok-version>
        <owasp-java-html-sanitizer>20240325.1</owasp-java-html-sanitizer>
        <skywalking-version>9.2.0</skywalking-version>
        <jackson-version>2.17.0</jackson-version>

        <fastjson.version>2.0.50</fastjson.version>
        <groovy.version>3.0.21</groovy.version>
        <huaweicloud.version>3.23.9.1</huaweicloud.version>
        <cos.version>5.6.210</cos.version>
        <tencentcloud.version>3.1.996</tencentcloud.version>
        <kuaidi100-api.version>1.0.12</kuaidi100-api.version>
    </properties>

    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok-version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <!-- Spring Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
            </plugin>
            <!-- 打镜像工具-->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <imageName>${docker-registry}/${project.artifactId}:1.0.2
                    </imageName>
                    <baseImage>openjdk:21-jdk</baseImage>
                    <env>
                        <JAVA_OPTS>--add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED</JAVA_OPTS>
                    </env>
                    <entryPoint>exec java $JVM_OPTS $JAVA_OPTS -jar /${project.build.finalName}.jar</entryPoint>
                    <!--                    <pushImage>true</pushImage>-->
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

        </plugins>
        <resources>
            <!--让bootstrap.yml可以读取pom文件中的配置-->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <layout>default</layout>
            <!-- 是否开启发布版构件下载 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- 是否开启快照版构件下载 -->
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <!-- 使用-P参数显示激活一个profile 例如 mvn package –Prelease -->
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
                <nacos.server>192.168.1.182:30848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <nacos.namespaceId>tb_s2b2b2c</nacos.namespaceId>
                <nacos.group>DEFAULT_GROUP</nacos.group>
                <nacos.refresh>true</nacos.refresh>
            </properties>
            <activation>
                <!-- 设置默认激活这个配置 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 发布环境 -->
            <id>release</id>
            <properties>
                <profiles.active>release</profiles.active>
                <nacos.server>nacos.yht-middleware:8848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <nacos.namespaceId>middle</nacos.namespaceId>
                <nacos.group>DEFAULT_GROUP</nacos.group>
                <nacos.refresh>false</nacos.refresh>
            </properties>
        </profile>
    </profiles>
</project>