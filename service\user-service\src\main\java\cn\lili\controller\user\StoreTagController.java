package cn.lili.controller.user;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.dto.StoreTagDTO;
import cn.lili.modules.store.entity.dto.StoreTagSearchParams;
import cn.lili.modules.store.entity.enums.StoreTagTypeEnum;
import cn.lili.modules.store.entity.vos.StoreTagVO;
import cn.lili.modules.store.service.StoreTagService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商家标签管理
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@RequestMapping("/user/store/tag")
@Tag(name = "商家标签管理")
@RequiredArgsConstructor
public class StoreTagController {

    private final StoreTagService storeTagService;

    @Operation(summary = "获取标签分页列表")
    @GetMapping
    public ResultMessage<Page<StoreTagVO>> getStoreTagPage(StoreTagSearchParams searchParams) {
        return ResultUtil.data(storeTagService.getStoreTagPage(searchParams));
    }

    @Operation(summary = "获取标签详情")
    @GetMapping("/{id}")
    public ResultMessage<StoreTagVO> getStoreTagDetail(@PathVariable String id) {
        return ResultUtil.data(storeTagService.getStoreTagDetail(id));
    }

    @Operation(summary = "添加标签")
    @PostMapping
    public ResultMessage<StoreTag> addStoreTag(@Valid @RequestBody StoreTagDTO storeTagDTO) {
        return ResultUtil.data(storeTagService.addStoreTag(storeTagDTO));
    }

    @Operation(summary = "编辑标签")
    @PutMapping
    public ResultMessage<StoreTag> editStoreTag(@Valid @RequestBody StoreTagDTO storeTagDTO) {
        return ResultUtil.data(storeTagService.editStoreTag(storeTagDTO));
    }

    @Operation(summary = "删除标签")
    @DeleteMapping("/{id}")
    public ResultMessage<Boolean> deleteStoreTag(@PathVariable String id) {
        return ResultUtil.data(storeTagService.deleteStoreTag(id));
    }

    @Operation(summary = "批量删除标签")
    @DeleteMapping("/batch")
    public ResultMessage<Boolean> batchDeleteStoreTag(@RequestParam List<String> ids) {
        return ResultUtil.data(storeTagService.batchDeleteStoreTag(ids));
    }

    @Operation(summary = "启用/禁用标签")
    @PutMapping("/{id}/status")
    public ResultMessage<Boolean> updateTagStatus(@PathVariable String id, @RequestParam Boolean enabled) {
        return ResultUtil.data(storeTagService.updateTagStatus(id, enabled));
    }

    @Operation(summary = "批量更新标签状态")
    @PutMapping("/batch/status")
    public ResultMessage<Boolean> batchUpdateTagStatus(@RequestParam List<String> ids, @RequestParam Boolean enabled) {
        return ResultUtil.data(storeTagService.batchUpdateTagStatus(ids, enabled));
    }

    @Operation(summary = "获取启用的标签列表")
    @GetMapping("/enabled")
    public ResultMessage<List<StoreTag>> getEnabledTags() {
        return ResultUtil.data(storeTagService.getEnabledTags());
    }

    @Operation(summary = "根据标签类型获取标签列表")
    @GetMapping("/type/{tagType}")
    public ResultMessage<List<StoreTag>> getTagsByType(@PathVariable String tagType) {
        return ResultUtil.data(storeTagService.getTagsByType(tagType));
    }

    @Operation(summary = "检查标签名称是否存在")
    @GetMapping("/check")
    public ResultMessage<Boolean> checkTagNameExists(@RequestParam String tagName, @RequestParam(required = false) String excludeId) {
        return ResultUtil.data(storeTagService.checkTagNameExists(tagName, excludeId));
    }

    @Operation(summary = "获取标签类型枚举")
    @GetMapping("/types")
    public ResultMessage<StoreTagTypeEnum[]> getTagTypes() {
        return ResultUtil.data(StoreTagTypeEnum.values());
    }

    @Operation(summary = "获取使用指定标签的店铺数量")
    @GetMapping("/store-count")
    public ResultMessage<Long> getStoreCountByTag(@RequestParam String tagName) {
        return ResultUtil.data(storeTagService.getStoreCountByTag(tagName));
    }

    @Operation(summary = "初始化默认标签")
    @PostMapping("/init")
    public ResultMessage<Object> initDefaultTags() {
        storeTagService.initDefaultTags();
        return ResultUtil.success();
    }
}
