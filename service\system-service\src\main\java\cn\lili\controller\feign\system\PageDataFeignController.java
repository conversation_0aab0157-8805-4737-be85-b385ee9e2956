package cn.lili.controller.feign.system;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.other.service.PageDataService;
import cn.lili.modules.page.client.PageDataClient;
import cn.lili.modules.page.entity.dos.PageData;
import cn.lili.modules.page.entity.dto.PageDataListParams;
import cn.lili.modules.page.entity.dto.PageDataSaveDTO;
import cn.lili.modules.page.entity.dto.PageDataSearchParams;
import cn.lili.modules.page.entity.dto.PageDataShowParams;
import cn.lili.modules.page.entity.enums.PageTypeEnum;
import cn.lili.modules.page.entity.vos.PageDataVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 页面数据服务 Feign 控制器
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class PageDataFeignController implements PageDataClient {

    private final PageDataService pageDataService;

    @Override
    public PageData getById(String id) {
        try {
            return pageDataService.getById(id);
        } catch (Exception e) {
            log.error("PageDataFeignController.getById 执行异常，参数：id={}", id, e);
            return null;
        }
    }

    @Override
    public PageDataVO getPageData(PageDataShowParams pageDataShowParams) {
        try {
            return pageDataService.getPageData(pageDataShowParams);
        } catch (Exception e) {
            log.error("PageDataFeignController.getPageData 执行异常，参数：{}", pageDataShowParams, e);
            return new PageDataVO(null);
        }
    }

    @Override
    public Page<PageData> getPageDataList(PageDataListParams params) {
        try {
            if (params == null || params.getPageVO() == null || params.getSearchParams() == null) {
                log.error("PageDataFeignController.getPageDataList 参数为空");
                return new Page<>();
            }
            return pageDataService.getPageDataList(params.getPageVO(), params.getSearchParams());
        } catch (Exception e) {
            log.error("PageDataFeignController.getPageDataList 执行异常，参数：{}", params, e);
            Page<PageData> page = new Page<>();
            page.setRecords(List.of());
            page.setTotal(0);
            if (params != null && params.getPageVO() != null) {
                page.setCurrent(params.getPageVO().getPageNumber());
                page.setSize(params.getPageVO().getPageSize());
            } else {
                page.setCurrent(1);
                page.setSize(10);
            }
            return page;
        }
    }

    @Override
    public PageData addPageData(PageDataSaveDTO pageDataSaveDTO) {
        try {
            return pageDataService.addPageData(pageDataSaveDTO);
        } catch (Exception e) {
            log.error("PageDataFeignController.addPageData 执行异常，参数：{}", pageDataSaveDTO, e);
            return null;
        }
    }

    @Override
    public PageData updatePageData(PageDataSaveDTO pageDataSaveDTO) {
        try {
            return pageDataService.updatePageData(pageDataSaveDTO);
        } catch (Exception e) {
            log.error("PageDataFeignController.updatePageData 执行异常，参数：{}", pageDataSaveDTO, e);
            return null;
        }
    }

    @Override
    public PageData updateStatus(String id, Boolean enable) {
        try {
            return pageDataService.updateStatus(id, enable);
        } catch (Exception e) {
            log.error("PageDataFeignController.updateStatus 执行异常，参数：id={}, enable={}", id, enable, e);
            return null;
        }
    }

    @Override
    public PageData releaseIndex(String id) {
        try {
            PageData pageData = pageDataService.getById(id);
            if (pageData != null) {
                return pageDataService.releaseIndex(pageData);
            }
            return null;
        } catch (Exception e) {
            log.error("PageDataFeignController.releaseIndex 执行异常，参数：id={}", id, e);
            return null;
        }
    }

    @Override
    public Boolean deletePageData(String id) {
        try {
            pageDataService.deletePageData(id);
            return true;
        } catch (Exception e) {
            log.error("PageDataFeignController.deletePageData 执行异常，参数：id={}", id, e);
            return false;
        }
    }

    @Override
    public PageDataVO getPageDataByType(String pageType, String extendId) {
        try {
            PageDataShowParams params = new PageDataShowParams();
            params.setPageType(PageTypeEnum.valueOf(pageType));
            if (CharSequenceUtil.isNotEmpty(extendId)) {
                params.setExtendId(extendId);
            }
            return pageDataService.getPageData(params);
        } catch (Exception e) {
            log.error("PageDataFeignController.getPageDataByType 执行异常，参数：pageType={}, extendId={}", pageType, extendId, e);
            return new PageDataVO(null);
        }
    }

    @Override
    public Boolean checkPageDataExists(String pageType, String extendId) {
        try {
            LambdaQueryWrapper<PageData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PageData::getPageType, pageType);
            queryWrapper.eq(PageData::getEnable, true);
            
            if (CharSequenceUtil.isNotEmpty(extendId)) {
                queryWrapper.eq(PageData::getExtendId, extendId);
            }
            
            return pageDataService.count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("PageDataFeignController.checkPageDataExists 执行异常，参数：pageType={}, extendId={}", pageType, extendId, e);
            return false;
        }
    }

    @Override
    public List<PageData> getEnabledPageDataList(String pageType) {
        try {
            LambdaQueryWrapper<PageData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PageData::getEnable, true);
            
            if (CharSequenceUtil.isNotEmpty(pageType)) {
                queryWrapper.eq(PageData::getPageType, pageType);
            }
            
            queryWrapper.orderByDesc(PageData::getCreateTime);
            
            return pageDataService.list(queryWrapper);
        } catch (Exception e) {
            log.error("PageDataFeignController.getEnabledPageDataList 执行异常，参数：pageType={}", pageType, e);
            return List.of();
        }
    }
}
