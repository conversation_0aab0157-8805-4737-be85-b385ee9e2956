package cn.lili.modules.kit;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.DateUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.common.utils.SpringContextUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.enums.ConnectEnum;
import cn.lili.modules.member.client.ConnectClient;
import cn.lili.modules.member.entity.dto.ConnectQueryDTO;
import cn.lili.modules.payment.entity.dos.CombinePaymentLog;
import cn.lili.modules.payment.entity.dos.PaymentLog;
import cn.lili.modules.payment.entity.dos.SubMerchantInformation;
import cn.lili.modules.payment.entity.dto.CashierParam;
import cn.lili.modules.payment.entity.dto.PayParam;
import cn.lili.modules.payment.entity.dto.PayParamItem;
import cn.lili.modules.payment.entity.dto.PaymentWakeupParam;
import cn.lili.modules.payment.entity.enums.PaymentClientEnum;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.payment.service.CombinePaymentLogService;
import cn.lili.modules.payment.service.PaymentLogService;
import cn.lili.modules.payment.service.SubMerchantInformationService;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.PointSetting;
import cn.lili.modules.system.entity.dto.payment.PaymentSupportSetting;
import cn.lili.modules.system.entity.dto.payment.WechatPaymentSetting;
import cn.lili.modules.system.entity.dto.payment.dto.PaymentSupportItem;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.wallet.service.WalletService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 收银台工具
 *
 * <AUTHOR>
 * @since 2020-12-19 09:25
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CashierSupport {

    private final ConnectClient connectClient;

    /**
     * 预存款
     */
    private final WalletService walletService;

    /**
     * 配置
     */
    private final SettingClient settingClient;

    /**
     * 支付唤醒
     */
    private final PaymentLogService paymentLogService;

    /**
     * 组合支付
     */
    private final CombinePaymentLogService combinePaymentLogService;

    /**
     * 子商户信息
     */
    private final SubMerchantInformationService subMerchantInformationService;

    /**
     * 生成支付表单支付,如果是线下支付、余额支付会直接返回结果
     *
     * @param payParam 支付参数
     * @return 支付消息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMessage<Object> generatePaymentForm(PayParam payParam) {

        if (payParam.getTotalAmount() <= 0) {
            throw new ServiceException(ResultCode.PAYMENT_AMOUNT_ERROR);
        }

        //获取支付客户端
        PaymentClientEnum paymentClientEnum = payParam.getPaymentClientEnum();
        //获取支付插件
        Payment payment = (Payment) SpringContextUtil.getBean(payParam.getPaymentMethodEnum().getPlugin());
        log.info("支付请求：客户端：{},支付类型：{},请求：{}", payParam.getPaymentClientEnum().name(),
                payParam.getPaymentMethodEnum().name(), payParam);

        //组织支付唤醒参数
        PaymentWakeupParam paymentWakeupParam = handler(payParam);

        ResultMessage<Object> resultMessage;
        //支付方式调用
        switch (paymentClientEnum) {
            case H5 -> resultMessage = payment.h5pay(paymentWakeupParam);
            case APP -> resultMessage = payment.appPay(paymentWakeupParam);
            case JSAPI -> resultMessage = payment.jsApiPay(paymentWakeupParam);
            case NATIVE -> resultMessage = payment.nativePay(paymentWakeupParam);
            case MP -> resultMessage = payment.mpPay(paymentWakeupParam);
            default -> {
                return null;
            }
        }

        if (Boolean.TRUE.equals(payParam.getIsCombine())) {
            combinePaymentLogService.save(paymentWakeupParam.getCombinePaymentLog());
            //保存支付日志
            paymentLogService.saveBatch(paymentWakeupParam.getPaymentLogs());

        } else {
            paymentLogService.save(paymentWakeupParam.getPaymentLog());
        }

        return resultMessage;

    }

    /**
     * 支付 支持的支付方式
     *
     * @param clientType 客户端类型
     * @return 支持的支付方式
     */
    public List<String> support(ClientTypeEnum clientType) {
        // 支付方式 循环获取
        Setting setting = settingClient.get(SettingEnum.PAYMENT_SUPPORT.name());
        PaymentSupportSetting paymentSupportSetting =
                JSONUtil.toBean(setting.getSettingValue(), PaymentSupportSetting.class);
        // 支付方式
        for (PaymentSupportItem paymentSupportItem : paymentSupportSetting.getPaymentSupportItems()) {
            // 如果支持当前客户端
            if (paymentSupportItem.getClient().equals(clientType.name())) {

                // 如果不支持余额支付，移除余额支付
                if (paymentSupportItem.getSupports().contains(PaymentMethodEnum.WALLET.name())
                    && !paymentSupportSetting.getWalletFlag()) {
                    paymentSupportItem.getSupports().remove(PaymentMethodEnum.WALLET.name());
                }
                return paymentSupportItem.getSupports();
            }
        }
        throw new ServiceException(ResultCode.PAY_NOT_SUPPORT);
    }

    /**
     * 支付回调
     *
     * @param paymentMethodEnum 支付渠道枚举
     * @return 回调消息
     */
    public void callback(PaymentMethodEnum paymentMethodEnum) {

        log.info("支付回调：支付类型：{}", paymentMethodEnum.name());

        // 获取支付插件
        Payment payment = (Payment) SpringContextUtil.getBean(paymentMethodEnum.getPlugin());
        payment.callBack();
    }

    /**
     * 支付通知
     *
     * @param paymentMethodEnum 支付渠道
     */
    public void notify(PaymentMethodEnum paymentMethodEnum) {

        log.info("支付异步通知：支付类型：{}", paymentMethodEnum.name());

        // 获取支付插件
        Payment payment = (Payment) SpringContextUtil.getBean(paymentMethodEnum.getPlugin());
        payment.paymentNotify();
    }

    /**
     * 获取收银台参数
     *
     * @param cashierParam 支付参数
     */
    public void paramsHandler(CashierParam cashierParam) {
        cashierParam.setSupport(support(cashierParam.getClientType()));
        if (cashierParam.getSupport().isEmpty()) {
            throw new ServiceException(ResultCode.PAY_NOT_SUPPORT);
        }
        // 如果支持余额支付，获取余额回填
        if (cashierParam.getSupport().stream().anyMatch(support -> support.equals(PaymentMethodEnum.WALLET.name()))) {
            cashierParam.setWalletValue(walletService.getWalletVO(UserContext.getCurrentId()).getBalance());
        }
    }

    /**
     * 支付结果
     *
     * @param sn 支付单号
     * @return 支付结果
     */
    public Boolean paymentResult(String sn, String orderType) {
        return paymentLogService.getPaymentResult(sn, orderType);
    }

    /**
     * 支付唤醒修饰
     *
     * @param payParam 支付参数
     * @return 支付唤醒参数
     */
    private PaymentWakeupParam handler(PayParam payParam) {

        // 微信支付时，需获取 openid
        if (payParam.getPaymentMethodEnum().equals(PaymentMethodEnum.WECHAT)) {

            if (payParam.getPaymentClientEnum().equals(PaymentClientEnum.MP)) {
                payParam.setOpenid(connectClient.queryConnect(
                        ConnectQueryDTO.builder().userId(UserContext.getCurrentId())
                                .unionType(ConnectEnum.WECHAT_MP_OPEN_ID.name()).build()).getUnionId());
                SubMerchantInformation subMerchantInformation =
                        subMerchantInformationService.getSubMerchantInformationByUserId(
                                payParam.getPayParamItems().getFirst().getPayeeId(), PaymentMethodEnum.WECHAT);
                if (subMerchantInformation != null) {
                    payParam.setWxAppid(subMerchantInformation.getSubMpAppid());
                }

            } else if (payParam.getPaymentClientEnum().equals(PaymentClientEnum.JSAPI)) {
                payParam.setOpenid(connectClient.queryConnect(
                        ConnectQueryDTO.builder().userId(UserContext.getCurrentId())
                                .unionType(ConnectEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name()).build()).getUnionId());
                SubMerchantInformation subMerchantInformation =
                        subMerchantInformationService.getSubMerchantInformationByUserId(
                                payParam.getPayParamItems().getFirst().getPayeeId(), PaymentMethodEnum.WECHAT);
                if (subMerchantInformation != null) {
                    payParam.setWxAppid(subMerchantInformation.getSubAppid());
                }
            }


            // 填充子商户号
            payParam.getPayParamItems().forEach(payParamItem -> {
                //如果收款人ID为空
                if (CharSequenceUtil.isEmpty(payParamItem.getPayeeId()) || Long.parseLong(payParamItem.getPayeeId()) < 0) {

                } else {
                    /*SubMerchantInformation subMerchantInformation =
                            subMerchantInformationService.getSubMerchantInformationByUserId(payParamItem.getPayeeId(), PaymentMethodEnum.WECHAT);

                    if (subMerchantInformation == null) {
                        throw new ServiceException(ResultCode.PAY_ERROR, payParamItem.getDescription() + "-未配置微信支付子商户号，请使用其他方式支付");
                    }*/

                    Setting setting = settingClient.get(SettingEnum.WECHAT_PAYMENT.name());
                    WechatPaymentSetting wechatPaymentSetting = JSONUtil.toBean(setting.getSettingValue(), WechatPaymentSetting.class);
                    if (wechatPaymentSetting == null || CharSequenceUtil.isEmpty(wechatPaymentSetting.getSubMchId())) {
                        throw new ServiceException(ResultCode.PAY_ERROR, payParamItem.getDescription() + "-未配置微信支付子商户号，请使用其他方式支付");
                    }
                    payParamItem.setMchId(wechatPaymentSetting.getSubMchId());
                }

            });
        }

        // 生成支付单号
        payParam.getPayParamItems().forEach(payParamItem -> {
            payParamItem.setOutTradeNo(SnowFlake.createStr("OTN"));
        });

        // 支付唤醒参数
        PaymentWakeupParam paymentWakeupParam = new PaymentWakeupParam();

        paymentWakeupParam.setIsCombine(payParam.getIsCombine());
        //合单支付逻辑处理
        if (Boolean.TRUE.equals(payParam.getIsCombine())) {
            //合单信息
            CombinePaymentLog combinePaymentLog =
                    CombinePaymentLog.builder().paymentClient(payParam.getPaymentClientEnum().name())
                            .paymentMethod(payParam.getPaymentMethodEnum().name()).orderSn(payParam.getCombineSn())
                            .price(payParam.getTotalAmount()).payerId(payParam.getPayerId())
                            .combineOutTradeNo(SnowFlake.createStr("COT"))
                            .timeoutExpress(DateUtil.offsetMinute(payParam.getTimeoutExpress())).build();

            List<PaymentLog> paymentLogs = new ArrayList<>(payParam.getPayParamItems().size());
            for (PayParamItem payParamItem : payParam.getPayParamItems()) {
                paymentLogs.add(PaymentLog.builder().orderSn(payParamItem.getSn())
                        .paymentScene(payParam.getPaymentSceneEnums().name())
                        .description(payParamItem.getDescription())
                        .paymentClient(payParam.getPaymentClientEnum().name())
                        .paymentMethod(payParam.getPaymentMethodEnum().name())
                        .price(payParamItem.getPrice())
                        .isCombine(true)
                        .combineOutTradeNo(combinePaymentLog.getCombineOutTradeNo())
                        .outTradeNo(payParamItem.getOutTradeNo())
                        .timeoutExpress(DateUtil.offsetMinute(payParam.getTimeoutExpress()))
                        .payerId(UserContext.getCurrentId())
                        .payeeId(payParamItem.getPayeeId())
                        .openid(payParam.getOpenid())
                        .mchId(payParamItem.getMchId())
                        .build());
            }

            paymentWakeupParam.setCombinePaymentLog(combinePaymentLog);
            paymentWakeupParam.setPaymentLogs(paymentLogs);

        } else {

            PayParamItem payParamItem = payParam.getPayParamItems().getFirst();
            paymentWakeupParam.setPaymentLog(
                    PaymentLog.builder().orderSn(payParamItem.getSn())
                            .paymentScene(payParam.getPaymentSceneEnums().name())
                            .description(payParamItem.getDescription())
                            .paymentClient(payParam.getPaymentClientEnum().name())
                            .paymentMethod(payParam.getPaymentMethodEnum().name())
                            .price(payParamItem.getPrice())
                            .isCombine(false)
                            .outTradeNo(payParamItem.getOutTradeNo())
                            .timeoutExpress(DateUtil.offsetMinute(payParam.getTimeoutExpress()))
                            .payerId(UserContext.getCurrentId())
                            .payeeId(payParamItem.getPayeeId())
                            .openid(payParam.getOpenid())
                            .mchId(payParamItem.getMchId())
                            .build());

        }
        paymentWakeupParam.setPayParam(payParam);
        return paymentWakeupParam;
    }
}
