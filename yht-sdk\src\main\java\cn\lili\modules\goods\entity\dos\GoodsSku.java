package cn.lili.modules.goods.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.WhetherEnum;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.goods.entity.dto.Wholesale;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.goods.entity.enums.SupplierEnum;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.search.entity.dos.EsSupplierGoodsIndex;
import cn.lili.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 商品sku
 *
 * <AUTHOR>
 * @since 2020-02-23 9:14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_goods_sku")
@Schema(title = "商品sku对象")
@NoArgsConstructor
public class GoodsSku extends BaseSceneEntity {

    public static final String ID = "id";
    public static final String SN_KEY = "sn";
    public static final String PRICE_KEY = "price";
    public static final String COST_KEY = "cost";
    public static final String WEIGHT_KEY = "weight";
    public static final String QUANTITY_KEY = "quantity";

    public static final String IMAGES_KEY = "images";

    @Serial
    private static final long serialVersionUID = 4865908658161118934L;

    @Schema(title = "商品id")
    private String goodsId;

    @Schema(title = "规格信息json", hidden = true)
    private String specs;

    @Schema(title = "规格信息")
    private String simpleSpecs;

    @Schema(title = "配送模版id")
    private String freightTemplateId;

    @Schema(title = "是否是促销商品")
    private Boolean promotionFlag;

    @Schema(title = "促销价")
    private Double promotionPrice;

    @Schema(title = "商品全称")
    private String goodsName;

    @Schema(title = "spu名称")
    private String spuName;

    @Length(max = 30, message = "商品规格编号太长，不能超过30个字符")
    @Schema(title = "商品编号")
    private String sn;

    @Schema(title = "品牌id")
    private String brandId;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "计量单位")
    private String goodsUnit;

    @Schema(title = "卖点")
    private String sellingPoint;

    @Schema(title = "重量")
    @Max(value = 99999999, message = "重量不能超过99999999")
    private Double weight;
    /**
     * @see GoodsMarketEnum
     */
    @Schema(title = "上架状态")
    private String marketEnable;

    @Schema(title = "商品详情")
    private String intro;

    @Max(value = 99999999, message = "价格不能超过99999999")
    @Schema(title = "商品价格")
    private Double price;

    @Max(value = 99999999, message = "成本价格99999999")
    @Schema(title = "成本价格")
    private Double cost;

    @Schema(title = "浏览数量")
    private Integer viewCount;

    @Schema(title = "购买数量")
    private Integer buyCount;

    @Max(value = 99999999, message = "库存不能超过99999999")
    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "商品好评率")
    private Double grade;

    @Schema(title = "缩略图路径")
    private String thumbnail;

    @Schema(title = "大图路径")
    private String big;

    @Schema(title = "小图路径")
    private String small;

    @Schema(title = "原图路径")
    private String original;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "评论数量")
    private Integer commentNum;

    @Schema(title = "卖家id")
    private String storeId;

    @Schema(title = "卖家名字")
    private String storeName;


    /**
     * @see GoodsAuthEnum
     */
    @Schema(title = "审核状态")
    private String authFlag;

    @Schema(title = "审核信息")
    private String authMessage;

    @Schema(title = "下架原因")
    private String underMessage;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "商品相册")
    private String goodsGallery;

    @Schema(title = "商品视频")
    private String goodsVideo;

    @Schema(title = "是否为推荐商品")
    private Boolean recommend;

    /**
     * @see SalesModeEnum
     */
    @Schema(title = "销售模式")
    private String salesModel;
    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型")
    private String goodsType;


    @Schema(title = "供应商ID")
    private String supplierId;
    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "供应商商品ID")
    private String supplierGoodsId;
    @Schema(title = "供应商SKU ID")
    private String supplierSkuId;

    /**
     * @see WhetherEnum
     */
    @Schema(title = "支持代发")
    private Boolean supportProxy;

    @Schema(title = "支持采购")
    private Boolean supportPurchase;


    /**
     * @see SupplierEnum
     */
    @Schema(title = "供应商类型")
    private String supplierEnum;

    @Schema(title = "是否是代理商品")
    private Boolean isProxyGoods = false;


    @Schema(title = "采购规则")
    private String purchaseRule;

    @Schema(title = "是否商品会员")
    private Boolean isMemberGoods = false;
    /**
     * @see cn.lili.modules.goods.entity.dto.Wholesale
     */
    @Schema(title = "批发商品消费规则列表 List<Wholesale>")
    private String wholesale;

    @Schema(title = "预警数量")
    private Integer alertQuantity;

    @Schema(title = "图片压缩文件")
    private String imageZipFile;

    @Schema(title = "是否是实拍商品")
    private Boolean isRealShoot;

    @Schema(title = "是否现货商品")
    private Boolean isInStock;

    @Schema(title = "搜同款商品ID")
    private String productId;

    @Schema(title = "店铺logo")
    private String storeLogo;

    @Schema(title = "预售截止时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preSaleDeadline;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney = false;


    /**
     * 设置规格商品的基本商品信息
     *
     * @param goods 基本商品信息
     */
    public GoodsSku(Goods goods) {
        BeanUtils.copyProperties(goods, this);
        //商品基本信息
        this.goodsId = goods.getId();
        this.goodsName = goods.getGoodsName();
        this.goodsType = goods.getGoodsType();
        this.selfOperated = goods.getSelfOperated();
        this.sellingPoint = goods.getSellingPoint();
        this.categoryPath = goods.getCategoryPath();
        this.brandId = goods.getBrandId();
        this.marketEnable = goods.getMarketEnable();
        this.intro = goods.getIntro();
        this.mobileIntro = goods.getMobileIntro();
        this.goodsUnit = goods.getGoodsUnit();
        this.grade = 100D;
        this.goodsVideo = goods.getGoodsVideo();
        //商品状态
        this.authFlag = goods.getAuthFlag();
        this.salesModel = goods.getSalesModel();
        //卖家信息
        this.storeId = goods.getStoreId();
        this.storeName = goods.getStoreName();
        this.storeLogo = goods.getStoreLogo();
        this.storeCategoryPath = goods.getStoreCategoryPath();
        this.freightTemplateId = goods.getTemplateId();
        this.recommend = goods.getRecommend();
        this.isRealShoot = goods.getIsRealShoot();
        this.isInStock = goods.getIsInStock();
        this.isBackMoney = goods.getIsBackMoney();
        this.setUpdateTime(goods.getUpdateTime());
        this.setCreateTime(goods.getCreateTime());
        this.setImageZipFile(goods.getImageZipFile());
    }

    public Integer getBuyCount() {
        if (buyCount == null) {
            return 0;
        }
        return buyCount;
    }

    public List<Wholesale> getWholesaleList() {
        if (CharSequenceUtil.isNotEmpty(wholesale)) {
            return JSONUtil.toList(JSONUtil.parseArray(wholesale), Wholesale.class);
        } else {
            return new ArrayList<>();
        }
    }

    public Integer getAlertQuantity() {
        if (alertQuantity == null) {
            return 0;
        }
        return alertQuantity;
    }

    public Wholesale machineWholesale(Integer num) {
        List<Wholesale> wholesaleList = getWholesaleList();
        if (wholesaleList == null || wholesaleList.isEmpty()) {
            return null;
        } else {
            Wholesale mach = null;
            for (Wholesale wholesale1 : wholesaleList) {

                if (num >= wholesale1.getNum()) {
                    mach = wholesale1;
                }
            }
            return mach;
        }
    }

    public Double getWeight() {
        if (weight == null) {
            return 0d;
        }
        return weight;
    }

    public String realSkuId() {
        if (isProxyGoods != null && isProxyGoods) {
            return this.getSupplierSkuId();
        }
        return this.getId();
    }
    @Override
    public Date getCreateTime() {
        if (super.getCreateTime() == null) {
            return new Date();
        } else {
            return super.getCreateTime();
        }
    }

    public Boolean getIsMemberGoods() {
        if (isMemberGoods == null) {
            return false;
        }
        return isMemberGoods;
    }

    //处理批发规则
    public void handlerWholeSale(Goods goods) {
        // 不支持采购则不处理
        if (Boolean.FALSE.equals(goods.getSupportPurchase())) {
            return;
        }
        //非供应商商品不处理
        if (!goods.getScene().equals(SceneEnums.SUPPLIER.name())) {
            return;
        }
        // 如果不是采购商品不处理
        if (Boolean.FALSE.equals(goods.getSupportPurchase())) {
            return;
        }

        //采购规则
        this.purchaseRule = goods.getPurchaseRule();
        // 商品报价模式 直接赋值
//        if (goods.getPurchaseRule().equals(PurchaseRuleEnum.SPU.name())) {
        this.wholesale = goods.getWholesale();
        this.price = goods.getPrice();
//        }
        //否则 采购规则为SKU 根据最小起批量和价格生成
//        else {
//            List<Wholesale> wholesaleList = new ArrayList<>();
//            Wholesale wholesale1 = new Wholesale();
//            wholesale1.setNum(goods.getMinimum());
//            wholesale1.setPrice(this.getPrice());
//            wholesaleList.add(wholesale1);
//            this.wholesale = JSONUtil.toJsonStr(wholesaleList);
//        }
    }

    public Class<? extends EsGoodsIndex> getEsIndexClass() {
        return SceneEnums.SUPPLIER.name().equals(this.getScene()) ? EsSupplierGoodsIndex.class : EsGoodsIndex.class;
    }


    @Override
    public GoodsSku clone() throws CloneNotSupportedException {
        return (GoodsSku) super.clone();
    }
}