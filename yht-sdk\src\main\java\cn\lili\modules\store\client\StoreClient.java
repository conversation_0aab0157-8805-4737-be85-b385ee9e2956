package cn.lili.modules.store.client;

import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreVerify;
import cn.lili.modules.store.entity.dto.StoreSearchPageParams;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.params.StoreParams;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.store.fallback.StoreFallback;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 店铺 client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.USER_SERVICE, contextId = "store", fallback = StoreFallback.class)
public interface StoreClient {

    /**
     * 自定义创建店铺，直接持久化保存DO
     * 用于中台调用，非标准业务流程调用
     *
     * @param store
     * @return
     */
    @PostMapping("/feign/store/custom/add")
    Store customAdd(@RequestBody Store store);


    /**
     * 自定义编辑店铺，持久层修改店铺
     * 用于中台调用，非标准业务流程调用
     *
     * @param store
     * @return
     */
    @PostMapping("/feign/store/custom/add/edit")
    Store customEdit(@RequestBody Store store);

    /**
     * 获取店铺详情
     *
     * @param id 店铺id
     * @return 店铺详情
     */
    @GetMapping("/feign/store/query/getStoreDetailVO/{id}")
    StoreVO getStoreDetailVO(@PathVariable("id") String id);


    /**
     * 获取当前登录店铺信息
     *
     * @return 店铺信息DO
     */
    @GetMapping("/feign/store/{storeId}/getStore")
    StoreVO getStore(@PathVariable String storeId);

    /**
     * 获取当前登录店铺信息
     *
     * @return 店铺信息DO
     */
    @GetMapping("/feign/store/{storeId}/getStoreDO")
    Store getStoreDO(@PathVariable String storeId);

    /**
     * 列表查询
     *
     * @param searchParams 查询参数
     * @return 店铺列表
     */
    @PostMapping("/feign/store/list")
    List<Store> list(@RequestBody StoreSearchParams searchParams);


    /**
     * 更新评分
     *
     * @param storeParams 店铺参数
     * @return 店铺
     */
    @PutMapping("/feign/store/settlement/updateScore")
    void updateScore(@RequestBody StoreParams storeParams);

    @PutMapping("/feign/store/settlement/updateStoreGoodsNum")
    void updateStoreGoodsNum(@RequestParam String storeId, @RequestParam Long num);

    /**
     * 分页查询店铺
     *
     * @param storeSearchPageParams 查询参数
     * @return 店铺分页
     */
    @PostMapping("/feign/store/page")
    Page<StoreVO> page(@RequestBody StoreSearchPageParams storeSearchPageParams);

    /**
     * 根据用户ID和场景获取店铺信息VO
     *
     * @param extendId 场景id
     * @param scene    场景
     * @return 店铺信息VO
     */
    @GetMapping("/feign/store/getStoreDetailBySceneAndUserId")
    Store getStoreDetailBySceneAndUserId(@RequestParam String extendId, @RequestParam SceneEnums scene);

    /**
     * 根据店铺管理员ID获取店铺信息
     *
     * @param managerId 店铺管理员ID
     * @param scene     场景
     * @return 店铺信息
     */
    @GetMapping("/feign/store/getStoreByManagerId")
    Store getStoreByManagerId(@RequestParam String managerId, @RequestParam SceneEnums scene);

    /**
     * 获取店铺认证信息
     *
     * @return 店铺审核信息
     */
    @GetMapping("/feign/store/getStoreVerify")
    StoreVerify getStoreVerify();


    /**
     * 注册店铺
     *
     * @param user 用户
     * @return 店铺
     */
    @PostMapping("/feign/store/registerUser")
    Store registerByUser(@RequestBody User user);

    /**
     * 获取店铺经营类目
     * @param storeId 店铺id
     * @return 店铺经营类目
     */
    @GetMapping("/feign/store/getBusinessCategory")
    String getBusinessCategory(@RequestParam String storeId);

    /**
     * 更新店铺统计数据
     * @param storeId 店铺id
     * @param num 数量
     * @param field 字段
     */
    @PutMapping("/feign/store/updateStoreStatistics")
    void updateStoreStatistics(@RequestParam String storeId,@RequestParam Double num,@RequestParam String field);
}
