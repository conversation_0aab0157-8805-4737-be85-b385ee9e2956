package cn.lili.controller.feign.store;

import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreVerify;
import cn.lili.modules.store.entity.dto.StoreSearchPageParams;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.params.StoreParams;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.store.service.StoreVerifyService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 店铺 feign client
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-17 18:06
 */
@RestController
@RequiredArgsConstructor
public class StoreFeignController implements StoreClient {

    private final StoreService storeService;

    private final StoreVerifyService storeVerifyService;

    @Override
    public StoreVO getStoreDetailVO(String id) {
        return storeService.getStoreDetailVO(id);
    }


    @Override
    @Transactional
    public Store customAdd(Store store) {
        return storeService.customAdd(store);
    }

    @Override
    @Transactional
    public Store customEdit(Store store) {
        return storeService.customEdit(store);
    }


    @Override
    public StoreVO getStore(String storeId) {
        return storeService.getStoreDetailVO(storeId);
    }

    @Override
    public Store getStoreDO(String storeId) {
        return storeService.getById(storeId);
    }

    /**
     * 列表查询
     *
     * @param searchParams 查询参数
     * @return 店铺列表
     */
    @Override
    public List<Store> list(StoreSearchParams searchParams) {
        return storeService.list(searchParams);
    }


    /**
     * 更新评分
     *
     * @param storeParams 店铺参数
     * @return 操作结果
     */
    @Override
    @Transactional
    public void updateScore(StoreParams storeParams) {

        storeService.updateScore(storeParams.getStoreId(),
                storeParams.getDeliveryScore(),
                storeParams.getServiceScore(),
                storeParams.getDescriptionScore());
    }

    @Override
    public void updateStoreGoodsNum(String storeId, Long num) {
        storeService.updateStoreGoodsNum(storeId, num);
    }

    @Override
    public Page<StoreVO> page(StoreSearchPageParams storeSearchPageParams) {
        return storeService.findByConditionPage(storeSearchPageParams.getStoreSearchParams(), storeSearchPageParams.getPage());
    }

    @Override
    public Store getStoreDetailBySceneAndUserId(String extendId, SceneEnums scene) {
        return storeService.getStoreDetailBySceneAndUserId(extendId, scene);
    }

    @Override
    public Store getStoreByManagerId(String managerId, SceneEnums scene) {
        return storeService.getStoreByManagerId(managerId, scene);
    }

    @Override
    public StoreVerify getStoreVerify() {
        return storeVerifyService.getCurrentStoreVerify();
    }

    @Override
    public Store registerByUser(User user) {
        return storeService.registerByUser(user);
    }

    @Override
    public String getBusinessCategory(String storeId) {
        return storeService.getBusinessCategory(storeId);
    }

    @Override
    public void updateStoreStatistics(String storeId, Double num, String field) {
        storeService.updateStoreStatistics(storeId, num, field);
    }
}
