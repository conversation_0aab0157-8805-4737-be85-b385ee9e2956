package cn.lili.modules.goods.entity.dos;

import cn.hutool.core.util.StrUtil;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 商品参数，如尺寸、颜色、材质等
 * 分类sku参数，和其他参数
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@TableName("li_goods_params")
@Schema(title = "商品参数")
public class GoodsParams extends BaseStandardEntity {

    @NotEmpty(message = "商品参数名称不能为空")
    @Schema(title = "商品参数名称")
    private String paramsName;

    @NotEmpty(message = "商品参数值不能为空")
    @Schema(title = "商品参数值")
    private String paramsValue;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsParamsTypeEnum
     */
    @NotEmpty(message = "商品参数类型")
    @Schema(title = "商品参数类型")
    private String paramsType;

    @NotEmpty(message = "商品参数所属分类")
    @Schema(title = "商品参数所属分类")
    private String categoryId;

    public boolean validateParams() {
        if (StrUtil.isEmpty(paramsName)) {
            ValidateParamsUtil.throwInvalidParamError("商品参数名不能为空");
        }
        if (StrUtil.isEmpty(paramsValue)) {
            ValidateParamsUtil.throwInvalidParamError("商品参数值不能为空");
        }
        if (StrUtil.isEmpty(categoryId)) {
            ValidateParamsUtil.throwInvalidParamError("商品参数所属分类不能为空");
        }
        return true;
    }
}
