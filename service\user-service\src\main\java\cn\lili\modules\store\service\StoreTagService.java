package cn.lili.modules.store.service;

import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.dto.StoreTagDTO;
import cn.lili.modules.store.entity.dto.StoreTagSearchParams;
import cn.lili.modules.store.entity.vos.StoreTagVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商家标签业务层
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface StoreTagService extends IService<StoreTag> {

    /**
     * 分页查询标签列表
     *
     * @param searchParams 查询参数
     * @return 标签分页列表
     */
    Page<StoreTagVO> getStoreTagPage(StoreTagSearchParams searchParams);

    /**
     * 添加标签
     *
     * @param storeTagDTO 标签信息
     * @return 标签
     */
    StoreTag addStoreTag(StoreTagDTO storeTagDTO);

    /**
     * 编辑标签
     *
     * @param storeTagDTO 标签信息
     * @return 标签
     */
    StoreTag editStoreTag(StoreTagDTO storeTagDTO);

    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 是否成功
     */
    Boolean deleteStoreTag(String id);

    /**
     * 批量删除标签
     *
     * @param ids 标签ID列表
     * @return 是否成功
     */
    Boolean batchDeleteStoreTag(List<String> ids);

    /**
     * 启用/禁用标签
     *
     * @param id 标签ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    Boolean updateTagStatus(String id, Boolean enabled);

    /**
     * 批量更新标签状态
     *
     * @param ids 标签ID列表
     * @param enabled 启用状态
     * @return 是否成功
     */
    Boolean batchUpdateTagStatus(List<String> ids, Boolean enabled);

    /**
     * 获取启用的标签列表
     *
     * @return 启用的标签列表
     */
    List<StoreTag> getEnabledTags();

    /**
     * 根据标签类型获取标签列表
     *
     * @param tagType 标签类型
     * @return 标签列表
     */
    List<StoreTag> getTagsByType(String tagType);

    /**
     * 检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param excludeId 排除的ID（编辑时使用）
     * @return 是否存在
     */
    Boolean checkTagNameExists(String tagName, String excludeId);

    /**
     * 获取标签详情
     *
     * @param id 标签ID
     * @return 标签VO
     */
    StoreTagVO getStoreTagDetail(String id);

    /**
     * 获取使用指定标签的店铺数量
     *
     * @param tagName 标签名称
     * @return 店铺数量
     */
    Long getStoreCountByTag(String tagName);

    /**
     * 初始化默认标签
     */
    void initDefaultTags();
}
