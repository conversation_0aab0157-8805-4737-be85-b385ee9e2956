package cn.lili.modules.order.order.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ServiceFeeDTO {

    @Schema(title = "服务费一级名称")
    private String name;

    @Schema(title = "服务费金额")
    private Double price;

    @Schema(title = "服务费一级id")
    private String id;

    @Schema(title = "商品数量")
    private Integer nums;

    @Schema(title = "服务费总金额")
    private Double totalPrice;

    @Schema(title = "子服务id")
    private String subId;

    @Schema(title = "子服务名称")
    private String subName;

    @Schema(title = "店铺id")
    private String storeId;

}
