package cn.lili.modules.order.aftersale.mapper;

import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleStatisticsVO;
import cn.lili.modules.order.aftersale.entity.vo.AfterSaleVO;
import cn.lili.modules.order.order.entity.dto.AfterSaleExportDTO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 售后数据处理层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:34 下午
 */
public interface AfterSaleMapper extends BaseMapper<AfterSale> {

    /**
     * 获取售后VO分页
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 售后VO分页
     */
    @Select("SELECT * FROM li_after_sale ${ew.customSqlSegment}")
    Page<AfterSaleVO> queryByParams(Page<AfterSaleVO> page, @Param(Constants.WRAPPER) Wrapper<AfterSaleVO> queryWrapper);

    /**
     * 根据售后编号获取售后VO
     *
     * @param sn 售后编号
     * @return 售后VO
     */
    @Select("SELECT * FROM li_after_sale WHERE sn=#{sn}")
    AfterSaleVO getAfterSaleVO(String sn);

    /**
     * 获取售后订单导出数据
     * @param queryWrapper
     * @return
     */
    @Select("SELECT * FROM li_after_sale ${ew.customSqlSegment}")
    List<AfterSaleExportDTO> getAfterSaleExportList(@Param(Constants.WRAPPER) Wrapper<AfterSaleVO> queryWrapper);

    /**
     * 统计售后状态为申请中、待卖家收货、等待平台退款的售后单数量
     *
     */
    @Select("SELECT " + "count(case when (service_status = 'APPLY') then 1 end) as apply_after_sale,"
        + "count(case when (service_status = 'BUYER_RETURN') then 1 end) as buyer_return_after_sale ,"
        + "count(case when (service_status = 'SELLER_TERMINATION') then 1 end) as seller_termination ,"
        + "count(case when (service_status = 'BUYER_CANCEL') then 1 end) as buyer_cancel ,"
        + "count(case when (service_status = 'PASS') then 1 end) as pass ,"
        + "count(case when (service_status = 'REFUSE') then 1 end) as refuse ,"
        + "count(case when (service_status = 'WAIT_REFUND') then 1 end) as wait_refund_after_sale, "
        + "count(case when (service_status = 'COMPLETE') then 1 end) as return_money_after_sale"
        + " from li_after_sale ${ew.customSqlSegment}")
    AfterSaleStatisticsVO getAfterSaleCount(@Param(Constants.WRAPPER) Wrapper<AfterSale> queryWrapper);

    /**
     * 获取店铺退货成功率
     * 计算方式：售后成功数/(售后拒绝数+售后成功数)
     *
     * @param storeId 店铺ID
     * @return 退货成功率（百分比）
     */
    @Select("SELECT " +
            "CASE " +
            "    WHEN (COUNT(CASE WHEN service_status = 'COMPLETE' AND service_type = 'RETURN_GOODS' THEN 1 END) + " +
            "          COUNT(CASE WHEN service_status = 'REFUSE' AND service_type = 'RETURN_GOODS' THEN 1 END)) = 0 THEN 0.0 " +
            "    ELSE ROUND((COUNT(CASE WHEN service_status = 'COMPLETE' THEN 1 END) * 100.0 / " +
            "               (COUNT(CASE WHEN service_status = 'COMPLETE' AND service_type = 'RETURN_GOODS' THEN 1 END) + " +
            "                COUNT(CASE WHEN service_status = 'REFUSE' AND service_type = 'RETURN_GOODS' THEN 1 END))), 2) " +
            "END " +
            "FROM li_after_sale " +
            "WHERE store_id = #{storeId}")
    Double getReturnSuccessRate(@Param("storeId") String storeId);
}