package cn.lili.timetask.handler.impl.store;

import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.order.order.client.AfterSaleClient;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.timetask.handler.EveryDayExecute;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class StoreStatisticsExecute implements EveryDayExecute {

    private final StoreClient storeClient;

    private final GoodsClient goodsClient;

    private final OrderClient orderClient;

    private final AfterSaleClient afterSaleClient;

    @Override
    public void execute() {
        StoreSearchParams storeSearchParams = new StoreSearchParams();
        storeSearchParams.setStoreStatus(StoreStatusEnum.OPEN.name());
        //获取所有开启的店铺
        List<Store> storeList = storeClient.list(storeSearchParams);
        for (Store store : storeList) {
            try {
                Double goodsRealRate = 0D;
                Double onTimeDeliveryRate = 0D;
                Double returnSuccessRate = 0D;
                // 获取商品实拍率
                getGoodsRealRate(store, goodsRealRate);
                // 获取商品发货及时率
                getOnTimeDeliveryRate(store, onTimeDeliveryRate);
                // 获取退货成功率
                getReturnSuccessRate(store, returnSuccessRate);
            } catch (Exception e) {
                log.error("店铺id为{},更新商品统计数据失败", store.getId(), e);
            }
        }
    }

    private void getOnTimeDeliveryRate(Store store, Double onTimeDeliveryRate) {
        Long paymentOrderCount = orderClient.getPaymentOrderCountByStore(store.getId());
        if (paymentOrderCount > 0) {
            // 使用数据库查询获取支付时间和发货时间相差在24小时之内的订单数量
            Long quickDeliveryCount = orderClient.getQuickDeliveryOrderCount(store.getId());

            // 计算准时发货率（24小时内发货的订单占比）
            if (quickDeliveryCount != null && quickDeliveryCount > 0) {
                onTimeDeliveryRate = CurrencyUtil.mul(CurrencyUtil.div(quickDeliveryCount, paymentOrderCount), 100D);
            }
        }
        // 更新店铺准时发货率统计
        storeClient.updateStoreStatistics(store.getId(), onTimeDeliveryRate, "onTimeDeliveryRate");
    }

    private void getGoodsRealRate(Store store, Double goodsRealRate) {
        List<Goods> goodsList = goodsClient.countGoodsNum(store.getId());
        if (CollectionUtils.isNotEmpty(goodsList)) {
            long totalNum = goodsList.size();
            // 获取实拍商品数
            long realShootGoodsNum = goodsList.stream().filter(Goods::getIsRealShoot).count();
            goodsRealRate = CurrencyUtil.mul(CurrencyUtil.div(realShootGoodsNum, totalNum), 100D);
        }
        storeClient.updateStoreStatistics(store.getId(), goodsRealRate, "goodsRealRate");
    }

    private void getReturnSuccessRate(Store store, Double returnSuccessRate) {
        try {
            // 检查afterSaleClient是否可用
            if (afterSaleClient == null) {
                log.warn("AfterSaleClient不可用，跳过退货成功率统计");
                return;
            }

            // 调用售后服务获取退货成功率
            returnSuccessRate = afterSaleClient.getReturnSuccessRate(store.getId());

            if (returnSuccessRate == null) {
                returnSuccessRate = 0.0;
            }
        } catch (Exception e) {
            log.warn("获取店铺{}退货成功率失败: {}", store.getId(), e.getMessage());
            returnSuccessRate = 0.0;
        }

        // 更新店铺退货成功率统计
        storeClient.updateStoreStatistics(store.getId(), returnSuccessRate, "returnSuccessRate");
    }

}
