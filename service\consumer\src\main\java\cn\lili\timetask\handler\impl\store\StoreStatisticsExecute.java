package cn.lili.timetask.handler.impl.store;

import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.timetask.handler.EveryDayExecute;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class StoreStatisticsExecute implements EveryDayExecute {

    private final StoreClient storeClient;

    private final GoodsClient goodsClient;

    private final OrderClient orderClient;

    @Override
    public void execute() {
        StoreSearchParams storeSearchParams = new StoreSearchParams();
        storeSearchParams.setStoreStatus(StoreStatusEnum.OPEN.name());
        //获取所有开启的店铺
        List<Store> storeList = storeClient.list(storeSearchParams);
        for (Store store : storeList) {
            try {
                Double goodsRealRate = 0D;
                Double onTimeDeliveryRate = 0D;
                getGoodsRealRate(store, goodsRealRate);

                OrderSearchParams orderSearchParams = new OrderSearchParams();
                orderSearchParams.setStoreId(store.getId());
                orderSearchParams.setOrderStatus(OrderStatusEnum.PAID.name());
                List<Order> orders = orderClient.queryListByParams(orderSearchParams);
                if (CollectionUtils.isNotEmpty(orders)) {
                    long totalNum = orders.size();

                }
            } catch (Exception e) {
                log.error("店铺id为{},更新商品统计数据失败", store.getId(), e);
            }
        }
    }

    private void getGoodsRealRate(Store store, Double goodsRealRate) {
        List<Goods> goodsList = goodsClient.countGoodsNum(store.getId());
        if (CollectionUtils.isNotEmpty(goodsList)) {
            long totalNum = goodsList.size();
            // 获取实拍商品数
            long realShootGoodsNum = goodsList.stream().filter(Goods::getIsRealShoot).count();
            goodsRealRate = CurrencyUtil.mul(CurrencyUtil.div(realShootGoodsNum, totalNum), 100D);
        }
        storeClient.updateStoreStatistics(store.getId(), goodsRealRate, "goodsRealRate");
    }
}
