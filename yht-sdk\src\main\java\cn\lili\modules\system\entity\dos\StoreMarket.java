package cn.lili.modules.system.entity.dos;

import cn.lili.common.utils.StringUtils;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 市场信息
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_store_market")
@Schema(title = "商家归属市场")
@EqualsAndHashCode(callSuper = true)
public class StoreMarket extends BaseStandardEntity {

    private static final long serialVersionUID = -3936333221051322353L;

    @Schema(title = "省份名称")
    private String province;

    @Schema(title = "城市名称")
    private String city;

    @Schema(title = "区县名称")
    private String district;

    @Schema(title = "乡镇/街道ID,关联区域表")
    @NotNull(message = "区域不能为空")
    private String regionId;

    @Schema(title = "详细地址")
    @NotNull(message = "详细地址不能为空")
    private String address;

    @Schema(title = "是否启用")
    private boolean openStatus = true;

    @Schema(title = "市场名称")
    @NotNull(message = "市场名称不能为空")
    private String marketName;

    @Schema(title = "区域id路径，含省、市、区、镇四级的id")
    @NotNull(message = "区域不能为空")
    private String regionIdPath;

    @Schema(title = "区域名称路径,含省、市、区、镇四级的名称")
    @NotNull(message = "区域名称不能为空")
    private String regionNamePath;

    public boolean validateParams() {
        if (StringUtils.isBlank(marketName)){
            ValidateParamsUtil.throwInvalidParamError("市场名称不能为空");
        }
        if (StringUtils.isBlank(address)){
            ValidateParamsUtil.throwInvalidParamError("详细地址不能为空");
        }
        if (StringUtils.isBlank(regionNamePath)){
            ValidateParamsUtil.throwInvalidParamError("区域不能为空");
        }
        if (StringUtils.isBlank(regionIdPath)){
            ValidateParamsUtil.throwInvalidParamError("区域不能为空");
        }

        return true;
    }
}