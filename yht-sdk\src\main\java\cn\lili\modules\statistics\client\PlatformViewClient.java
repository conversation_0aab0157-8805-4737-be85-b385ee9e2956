package cn.lili.modules.statistics.client;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.statistics.entity.dos.PlatformViewData;
import cn.lili.modules.statistics.fallback.PlatformViewFallback;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 **/

@FeignClient(name = ServiceConstant.STATISTICS_SERVICE, contextId = "platform-view", fallback = PlatformViewFallback.class)
public interface PlatformViewClient {


    /**
     * 批量保存
     *
     * @param platformViewDataList 平台pv统计集合
     * @return 是否操作成功
     */
    @PostMapping("/feign/platform-view/save/batch")
    boolean saveBatch(@RequestBody List<PlatformViewData> platformViewDataList);

    /**
     * 获取店铺统计数据
     *
     * @return 店铺统计数据
     */
    @PostMapping("/feign/platform-view/get/store/statistics")
    Page<StoreRankStatisticsVO> getStoreStatistics(@RequestBody StoreRankStatisticsParams params);
}
