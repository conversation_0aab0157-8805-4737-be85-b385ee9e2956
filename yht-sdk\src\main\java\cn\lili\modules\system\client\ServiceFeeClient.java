package cn.lili.modules.system.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.fallback.ServiceFeeFallback;
import cn.lili.modules.system.fallback.SettingFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 系统配置 client
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "service-fee",fallback = ServiceFeeFallback.class)
public interface ServiceFeeClient {

    /**
     * 查询服务费列表
     * @param ids
     * @return
     */
    @GetMapping("/feign/serviceFee/getServiceFeeListByIds/{ids}")
    List<ServiceFee> getServiceFeeListByIds(@PathVariable List<String> ids);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("/feign/serviceFee/getById/{id}")
    ServiceFee getById (String id);

    /**
     * 获取默认的服务费列表
     * @return
     */
    @GetMapping("/feign/serviceFee/getDefaultServiceFeeList")
    List<ServiceFee> getDefaultServiceFeeList();
}
