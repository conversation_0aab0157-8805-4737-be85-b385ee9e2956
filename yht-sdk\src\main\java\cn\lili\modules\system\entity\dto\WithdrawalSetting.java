package cn.lili.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 提现配置
 *
 * <AUTHOR>
 * @since 2020/11/30 15:23
 */
@Data
public class WithdrawalSetting implements Serializable {

    private static final long serialVersionUID = -3872782530832122976L;
    /**
     * 提现是否需要申请
     */
    private Boolean apply;

    @Schema(title = "提现周期，取值：WEEK")
    private String withdrawType; //取值：'WEEK'

    @Schema(title = "每周几，取值：1-7")
    private Integer weekDay; //星期几,取值：1-7

    private String minPrice;

    private String type;
}
