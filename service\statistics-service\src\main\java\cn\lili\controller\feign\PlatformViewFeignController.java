package cn.lili.controller.feign;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.statistics.client.PlatformViewClient;
import cn.lili.modules.statistics.entity.dos.PlatformViewData;
import cn.lili.modules.statistics.service.PlatformViewService;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 **/
@RestController
@RequiredArgsConstructor
public class PlatformViewFeignController implements PlatformViewClient {

    private final PlatformViewService platformViewService;

    /**
     * 批量保存
     *
     * @param platformViewDataList 平台pv统计集合
     * @return 是否操作成功
     */
    @Override
    public boolean saveBatch(List<PlatformViewData> platformViewDataList) {
        return platformViewService.saveBatch(platformViewDataList);
    }

    @Override
    public Page<StoreRankStatisticsVO> getStoreStatistics(StoreRankStatisticsParams params) {
        return platformViewService.getStoreStatistics(params);
    }


}
