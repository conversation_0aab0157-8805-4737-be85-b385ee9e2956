# 店铺功能增强 - 2025-07-23

## 功能概述

根据产品需求，为 `/user/store` 接口添加新的查询方式，支持前端传递 1,2,3,4,5 查询不同类型的店铺：

1. **推荐店铺** - 有标签的优质店铺
2. **上新店铺** - 最新入驻的店铺，按创建时间降序
3. **实拍店铺** - 提供实拍商品的店铺
4. **发货快店铺** - 发货速度快的店铺
5. **热销店铺** - 按商品数量（销量）降序排序的热门店铺

## 数据库变更

### 新增字段

在 `li_store` 表中添加以下字段：

- `store_tag` VARCHAR(255) - 店铺标签
- `real_photo` TINYINT(1) - 是否实拍商品（0-否，1-是）
- `fast_delivery` TINYINT(1) - 是否发货快（0-否，1-是）

### 新增索引

为提高查询性能，添加以下索引：

- `idx_store_tag` - 店铺标签索引
- `idx_real_photo` - 实拍商品索引
- `idx_fast_delivery` - 发货快索引
- `idx_create_time` - 创建时间索引
- `idx_goods_num` - 商品数量索引

## API 变更

### 查询参数扩展

`StoreSearchParams` 新增以下参数：

- `storeTag` - 店铺标签筛选
- `sortType` - 排序方式
  - `CREATE_TIME_ASC` - 创建时间升序
  - `CREATE_TIME_DESC` - 创建时间降序
  - `SALES_ASC` - 销量升序
  - `SALES_DESC` - 销量降序
- `realPhoto` - 是否实拍商品筛选
- `fastDelivery` - 是否发货快筛选
- `queryType` - 查询类型（1-推荐，2-上新，3-实拍，4-发货快，5-热销）

### 新增接口

1. **根据查询类型获取店铺列表**
   - `GET /user/store/type/{queryType}`
   - 路径参数：`queryType` - 查询类型（1-5）

2. **设置店铺标签**
   - `PUT /user/store/{storeId}/tag`
   - 参数：`storeTag` - 店铺标签

3. **设置店铺实拍状态**
   - `PUT /user/store/{storeId}/realPhoto`
   - 参数：`realPhoto` - 是否实拍商品

4. **设置店铺发货快状态**
   - `PUT /user/store/{storeId}/fastDelivery`
   - 参数：`fastDelivery` - 是否发货快

5. **商家标签管理**
   - `GET /user/store/tag` - 获取标签分页列表
   - `POST /user/store/tag` - 添加标签
   - `PUT /user/store/tag` - 编辑标签
   - `DELETE /user/store/tag/{id}` - 删除标签
   - `PUT /user/store/tag/{id}/status` - 启用/禁用标签
   - `GET /user/store/tag/enabled` - 获取启用的标签列表

## 使用示例

### 根据查询类型获取店铺
```
# 查询推荐店铺
GET /user/store/type/1?page=1&size=10

# 查询上新店铺
GET /user/store/type/2?page=1&size=10

# 查询实拍店铺
GET /user/store/type/3?page=1&size=10

# 查询发货快店铺
GET /user/store/type/4?page=1&size=10

# 查询热销店铺
GET /user/store/type/5?page=1&size=10
```

### 使用查询参数方式
```
GET /user/store?queryType=1&page=1&size=10
GET /user/store?queryType=3&realPhoto=true&page=1&size=10
```

### 设置店铺标签
```
PUT /user/store/123456/tag?storeTag=优质商家
```

### 设置店铺实拍状态
```
PUT /user/store/123456/realPhoto?realPhoto=true
```

### 设置店铺发货快状态
```
PUT /user/store/123456/fastDelivery?fastDelivery=true
```

## 部署说明

1. 执行数据库迁移脚本：`store_enhancement.sql`
2. 重启相关服务
3. 验证新功能是否正常工作

## 注意事项

- 新字段 `real_photo` 默认值为 0（非实拍）
- 排序功能默认按创建时间降序
- 标签字段支持模糊查询
- 建议在生产环境执行前先在测试环境验证
