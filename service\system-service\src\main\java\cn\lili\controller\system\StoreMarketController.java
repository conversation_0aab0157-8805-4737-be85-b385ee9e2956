package cn.lili.controller.system;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.entity.params.StoreMarketSearchParams;
import cn.lili.modules.system.entity.vo.ServiceFeeVO;
import cn.lili.modules.system.entity.vo.StoreMarketVO;
import cn.lili.modules.system.service.ServiceFeeService;
import cn.lili.modules.system.service.StoreMarketService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "管理后台,商家市场")
@RequestMapping("/system/store/market")
@RequiredArgsConstructor
public class StoreMarketController {

    private final StoreMarketService storeMarketService;

    @Operation(summary = "获取市场列表")
    @GetMapping(value = "/list")
    public ResultMessage<Page<StoreMarketVO>> getList(StoreMarketSearchParams storeMarketSearchParams) {
        return ResultUtil.data(storeMarketService.getOpenList(storeMarketSearchParams));
    }

    @Operation(summary = "获取市场列表")
    @GetMapping(value = "/listAll")
    public ResultMessage<List<StoreMarket>> listAll(String city) {
        return ResultUtil.data(storeMarketService.listAll(city));
    }

    @Operation(summary = "添加市场")
    @PostMapping
    public ResultMessage<StoreMarket> save(@RequestBody StoreMarket storeMarket) {
        //storeMarket.validateParams();
        storeMarketService.addOrUpdate(storeMarket);
        return ResultUtil.data(storeMarket);
    }

    @Operation(summary = "编辑市场")
    @PutMapping(value = "/{id}")
    public ResultMessage<StoreMarket> update(@NotNull @PathVariable String id, @RequestBody StoreMarket storeMarket) {
        storeMarket.setId(id);
        //storeMarket.validateParams();
        storeMarketService.addOrUpdate(storeMarket);
        return ResultUtil.data(storeMarket);
    }

    @Operation(summary = "删除市场")
    @DeleteMapping(value = "/{id}")
    public ResultMessage<Object> delAllByIds(@PathVariable String id) {
        StoreMarket market = storeMarketService.getById(id);
        if(null!=market){
            market.setDeleteFlag(true);
            storeMarketService.addOrUpdate(market);
            return ResultUtil.success();
        }
        return ResultUtil.error();
    }
}
