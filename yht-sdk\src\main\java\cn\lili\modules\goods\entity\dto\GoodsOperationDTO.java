package cn.lili.modules.goods.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.jetbrains.annotations.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品编辑DTO
 *
 * <AUTHOR>
 * @since 2020-02-24 19:27:20
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GoodsOperationDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -509667581371776913L;

    @Schema(hidden = true)
    private String goodsId;

    @Schema(title = "商品价格")
    private Double price;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "品牌id")
    private String brandId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "详情")
    private String intro;

    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "是否立即发布")
    private Boolean release;

    @Schema(title = "是否是推荐商品")
    private Boolean recommend;

    @Schema(title = "商品参数")
    private List<GoodsParamsDTO> goodsParamsDTOList;

    @Schema(title = "商品图片")
    private List<String> goodsGalleryList;

    @Schema(title = "运费模板id,不需要运费模板时值是0")
    private String templateId;

    @Schema(title = "SKU列表")
    private List<GoodsSkuOperationDTO> skuList;

    @Schema(title = "卖点")
    private String sellingPoint;

    @Schema(title = "销售模式")
    private String salesModel;

    @Schema(title = "商品单位")
    private String goodsUnit;

    @Schema(title = "商品描述")
    private String info;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型")
    private String goodsType;

    /**
     * 商品视频
     */
    @Schema(title = "商品视频")
    private String goodsVideo;

    @Schema(title = "第三方id")
    private String thirdPartId;

    @Schema(title = "卖家id")
    private String storeId;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "保留字段1", hidden = true)
    private String field1;

    @Schema(title = "保留字段2", hidden = true)
    private String field2;

    @Schema(title = "保留字段3", hidden = true)
    private String field3;

    @Schema(title = "保留字段4", hidden = true)
    private String field4;

    @Schema(title = "保留字段5", hidden = true)
    private String field5;

    @Schema(title = "扩展字段，可自由存储，数据库为text格式", hidden = true)
    private String ext;

    @Schema(title = "是否是会员商品")
    private Boolean isMemberGoods;

    @Schema(title = "商品规格")
    private String specs;

    @Schema(title = "起售数量")
    private Integer minimum;

    /**
     * 批发采购规则
     */
    @Schema(title = "批发采购规则")
    private List<Wholesale> wholesaleList;

    @Schema(title = "是否是实拍商品")
    private Boolean isRealShoot;

    @Schema(title = "是否现货商品")
    private Boolean isInStock;

    /**
     * 图片资源包
     */
    @Schema(title = "图片资源包")
    private String imageZipFile;

    public GoodsOperationDTO(GoodsImportDTO goodsImportDTO) {
        this.price = goodsImportDTO.getPrice();
        this.goodsName = goodsImportDTO.getGoodsName();
        this.intro = goodsImportDTO.getIntro();
        this.mobileIntro = goodsImportDTO.getIntro();
        this.quantity = goodsImportDTO.getQuantity();
        this.goodsGalleryList = goodsImportDTO.getGoodsGalleryList();
        this.templateId = goodsImportDTO.getTemplate();
        this.sellingPoint = goodsImportDTO.getSellingPoint();
        this.salesModel = Boolean.TRUE.equals(goodsImportDTO.getSupportPurchase()) ? SalesModeEnum.WHOLESALE.name()
                : SalesModeEnum.RETAIL.name();
        this.goodsUnit = goodsImportDTO.getGoodsUnit();
        this.goodsType = goodsImportDTO.getGoodsType();
        this.release = goodsImportDTO.getRelease();
        this.recommend = false;
        this.isMemberGoods = false;

        GoodsSkuOperationDTO goodsSkuOperationDTO = getGoodsSkuOperationDTO(goodsImportDTO);

        List<GoodsSkuOperationDTO> goodsSkuOperationDTOS = new ArrayList<>();
        goodsSkuOperationDTOS.add(goodsSkuOperationDTO);
        this.skuList = goodsSkuOperationDTOS;
    }

    /**
     * 获取商品sku操作DTO
     *
     * @param goodsImportDTO 商品导入DTO
     * @return 商品sku操作DTO
     */
    private static @NotNull GoodsSkuOperationDTO getGoodsSkuOperationDTO(GoodsImportDTO goodsImportDTO) {
        GoodsSkuOperationDTO goodsSkuOperationDTO = new GoodsSkuOperationDTO();
        goodsSkuOperationDTO.setSn(goodsImportDTO.getSn());
        goodsSkuOperationDTO.setPrice(goodsImportDTO.getPrice());
        goodsSkuOperationDTO.setCost(goodsImportDTO.getCost());
        goodsSkuOperationDTO.setQuantity(goodsImportDTO.getQuantity());
        goodsSkuOperationDTO.setWeight(goodsImportDTO.getWeight());
        goodsSkuOperationDTO.setSpecs(goodsImportDTO.getSpecs());
        goodsSkuOperationDTO.setImages(goodsImportDTO.getSpecsImage());
        return goodsSkuOperationDTO;
    }

    public String getGoodsName() {
        //对商品对名称做一个极限处理。这里没有用xss过滤是因为xss过滤为全局过滤，影响很大。
        // 业务中，全局代码中只有商品名称不能拥有英文逗号，是由于商品名称存在一个数据库联合查询，结果要根据逗号分组
        return goodsName.replace(",", "");
    }

    /**
     * 验证商品参数
     *
     * @return 验证结果
     */
    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(goodsName, 2, 50)) {
            ValidateParamsUtil.throwInvalidParamError("商品名称不能为空，且长度在 2-50 个字符");
        }
        if (!ValidateParamsUtil.isValidString(categoryPath, "^\\d+(,\\d+){2}$")) {
            ValidateParamsUtil.throwInvalidParamError("商品分类必须选择，且需要选择到三级目录");
        }
        if (!ValidateParamsUtil.isValidString(storeCategoryPath, "^\\d+(,\\d+){0,9}$", false)) {
            ValidateParamsUtil.throwInvalidParamError("选择的店铺分类不能超过10组");
        }
//        if (!ValidateParamsUtil.isValidDoubleValue(price, 0, 99999999)) {
//            ValidateParamsUtil.throwInvalidParamError("商品价格必须在0到99,999,999之间");
//        }
        // if (!ValidateParamsUtil.isValidIntValue(quantity, 0, 99999999, false)) {
        //     ValidateParamsUtil.throwInvalidParamError("商品库存数量必须在0到99,999,999之间");
        // }
        if (!ValidateParamsUtil.isValidListLength(goodsGalleryList, 1, 10, true)) {
            ValidateParamsUtil.throwInvalidParamError("商品图片必须上传且不超过10张图片");
        }
        if (!ValidateParamsUtil.isValidString(goodsUnit, 1, 20)) {
            ValidateParamsUtil.throwInvalidParamError("商品单位不正确，单位应为2-20字符");
        }
        if (!ValidateParamsUtil.isValidEnumValue(salesModel, SalesModeEnum.class, false)) {
            salesModel = SalesModeEnum.RETAIL.name();
        }
        if (!ValidateParamsUtil.isValidEnumValue(goodsType, GoodsTypeEnum.class, false)) {
            goodsType = GoodsTypeEnum.PHYSICAL_GOODS.name();
        }
        // 0表示无需运费
        if (CharSequenceUtil.isBlank(templateId)) {
            templateId = "0";
        }
        // 如果 sku 集合为空，则默认添加一个默认 sku
        if (this.skuList == null || this.skuList.isEmpty() || this.skuList.stream().anyMatch(sku -> CharSequenceUtil.isEmpty(sku.getSpecs()) || "{\"\":\"\"}".equals(sku.getSpecs()))) {
            GoodsSkuOperationDTO goodsSkuOperationDTO = new GoodsSkuOperationDTO();
            goodsSkuOperationDTO.setPrice(this.getPrice());
            goodsSkuOperationDTO.setQuantity(this.getQuantity());
            goodsSkuOperationDTO.setCost(this.getPrice());
            goodsSkuOperationDTO.setWeight(0.0);
            goodsSkuOperationDTO.setSpecs("{\"默认\":\"默认\"}");
            skuList = new ArrayList<>();
            skuList.add(goodsSkuOperationDTO);
        }
        if (!ValidateParamsUtil.isValidListLength(skuList, 1, 25, false)) {
            ValidateParamsUtil.throwInvalidParamError("sku 数量最多25个");
        }
        if (!ValidateParamsUtil.isValidString(sellingPoint, 0, 255, false)) {
            ValidateParamsUtil.throwInvalidParamError("商品卖点不能超过255个字符");
        }

        this.checkSaleMode();
        return true;
    }

    /**
     * 校验销售模式
     */
    private void checkSaleMode() {
        //如果支持采购，采购规则判定
        if (SalesModeEnum.WHOLESALE.name().equals(this.getSalesModel())) {
            //批发规则大于3条 抛出异常
            if (this.getWholesaleList().size() > 3) {
                throw new ServiceException(ResultCode.PURCHASE_RULE_MAX_THREE);
            }
            //如果起售数量为空
            if (minimum == null && !this.getWholesaleList().isEmpty()) {
                // 起售数量为最小的批发数量
                this.getWholesaleList().stream().min(Comparator.comparingInt(Wholesale::getNum))
                        .ifPresent(wholesale -> minimum = wholesale.getNum());
            }
        } else {
            // 不支持采购，采购规则为空.处理其他商品编辑为代理商品时的问题
            this.setWholesaleList(null);
        }

        //采购规则校验
        if (this.getWholesaleList() != null) {
            //数量校验 如果有同一数量规则，抛出异常
            Map<Integer, List<Wholesale>> map =
                    this.getWholesaleList().stream().collect(Collectors.groupingBy(Wholesale::getNum));
            if (map.size() < this.getWholesaleList().size()) {
                throw new ServiceException(ResultCode.PURCHASE_RULE_NUM_DUPLICATE);
            }

            //排序
            this.getWholesaleList().sort(Comparator.comparingInt(Wholesale::getNum));
            //阶梯价格校验
            for (int i = 0; i < this.getWholesaleList().size() - 1; i++) {
                if (this.getWholesaleList().get(i).getPrice() < this.getWholesaleList().get(i + 1).getPrice()) {
                    throw new ServiceException(ResultCode.PURCHASE_RULE_PRICE_ERROR);
                }
            }
        }
    }

    // 对商品部分字段做默认值处理
    public String getTemplateId() {
        if (CharSequenceUtil.isEmpty(templateId)) {
            return "0";
        }
        return templateId;
    }

    public Boolean getRelease() {
        if (release == null) {
            return true;
        }
        return release;
    }

    public Boolean getRecommend() {
        if (recommend == null) {
            return true;
        }
        return recommend;
    }

    public String getGoodsType() {
        if (CharSequenceUtil.isEmpty(goodsType)) {
            return GoodsTypeEnum.PHYSICAL_GOODS.name();
        }
        return goodsType;
    }

    public String getGoodsUnit() {
        if (CharSequenceUtil.isEmpty(goodsUnit)) {
            return "个";
        }
        return goodsUnit;
    }

    public Integer getQuantity() {
        if (quantity == null) {
            return 0;
        }
        return quantity;
    }
}
