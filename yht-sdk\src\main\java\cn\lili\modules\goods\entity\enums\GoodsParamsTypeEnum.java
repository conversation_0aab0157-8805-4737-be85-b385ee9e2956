package cn.lili.modules.goods.entity.enums;

/**
 * 商品类型
 *
 * <AUTHOR>
 * @since 2021/5/28 4:23 下午
 */
public enum GoodsParamsTypeEnum {

    /**
     * "实物商品"
     */
    SKU_PARAMS("SKU参数"),
    /**
     * "虚拟商品"
     */
    OTHER_PARAMS("其他参数");

    private final String description;


    GoodsParamsTypeEnum(String description) {
        this.description = description;
    }

    public String description() {
        return description;
    }
}
