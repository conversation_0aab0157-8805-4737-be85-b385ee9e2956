package cn.lili.modules.goods.entity.vos;

import cn.lili.modules.goods.entity.dto.GoodsParamsDTO;
import cn.lili.modules.goods.entity.dto.Wholesale;
import cn.lili.modules.promotion.tools.PromotionTools;
import cn.lili.modules.search.entity.dos.EsGoodsAttribute;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsBuyerVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4272825079226548453L;

    @Schema(title = "sku信息")
    private GoodsSkuBuyerVO data;

    @Schema(title = "商品规格参数")
    private List<GoodsSkuSpecVO> specs;

    @Schema(title = "分类名称")
    private List<String> categoryName;

    @Schema(title = "商品参数")
    private List<GoodsParamsDTO> goodsParamsDTOList;

    @Schema(title = "商品图片")
    private List<String> goodsGalleryList;

    @Schema(title = "sku列表")
    private List<GoodsSkuVO> skuList;

    @Schema(title = "批发商品消费规则列表")
    private List<Wholesale> wholesaleList;

    @Schema(title = "运费模版信息")
    private FreightTemplateVO freightTemplate;

    @Schema(title = "促销信息")
    private Map<String, Object> promotionMap;

    @Schema(title = "资源包")
    private String imageZipFile;

    @Schema(title = "直营店铺-是否允许购买商品")
    private Boolean isBuyGoods;

    public GoodsBuyerVO(EsGoodsIndex goodsIndex) {
        BeanUtils.copyProperties(goodsIndex, this);
    }


    public Map<String, Object> getPromotionMap() {
        return PromotionTools.filterInvalidPromotionsMap(this.promotionMap);
    }
}
