package cn.lili.modules.order.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderFlow;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.payment.entity.dto.PaymentCallback;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @author: ftyy
 * @date: 2022-01-13 17:21
 * @description: 订单服务 Fallback
 */
public class OrderFallback implements OrderClient {

    @Override
    public Order getBySn(String sn) {
        throw new ServiceException();
    }

    @Override
    public void paymentCallback(PaymentCallback paymentCallback) {
        throw new ServiceException();
    }

    @Override
    public void systemCancel(String orderSn, String reason,Boolean refundMoney) {
        throw new ServiceException();
    }

    @Override
    public Double getPaymentTotal(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public OrderDetailVO queryDetail(String sn) {
        throw new ServiceException();
    }

    @Override
    public List<Order> getByTradeSn(String sn) {
        throw new ServiceException();
    }

    @Override
    public List<Order> queryListByParams(OrderSearchParams orderSearchParams) {
        throw new ServiceException();
    }

    @Override
    public long queryCountByPromotion(
            String orderPromotionType, String payStatus, String parentOrderSn, String orderSn) {
        throw new ServiceException();
    }

    @Override
    public void checkFictitiousOrder(String pintuanId, Integer requiredNum, Boolean fictitious) {
        throw new ServiceException();
    }

    @Override
    public void save(Order order) {
        throw new ServiceException();
    }

    @Override
    public void afterOrderConfirm(String orderSn) {
        throw new ServiceException();
    }

    /**
     * 自动成团订单处理
     *
     * @param pintuanId     拼团活动id
     * @param parentOrderSn 拼团订单sn
     */
    @Override
    public void agglomeratePintuanOrder(String pintuanId, String parentOrderSn) {
        throw new ServiceException();
    }

    @Override
    public void updateByStoreInfo(Store store) {
        throw new ServiceException();
    }

    @Override
    public void storeRefundReconcilePayment(AfterSale afterSale) {
        throw new ServiceException();
    }
    @Override
    public List<Order> listByTradeSn(String tradeSn) {
        throw new ServiceException();
    }

    @Override
    public Page<StoreRankStatisticsVO> getSaleAmountByStore(StoreRankStatisticsParams params) {
        throw new ServiceException();
    }

    @Override
    public Long getPaymentOrderCountByStore(String storeId) {
        throw new ServiceException();
    }

    @Override
    public Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams) {
        throw new ServiceException();
    }

    @Override
    public Order delivery(String orderSn, String logisticsNo, String logisticsId) {
        throw new ServiceException();
    }

    @Override
    public Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO) {
        throw new ServiceException();
    }

    @Override
    public void cancel(String orderSn, String reason) {
        throw new ServiceException();
    }

    @Override
    public void complete(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public void getBatchDeliverList(List<String> logisticsName) {
        throw new ServiceException();
    }

    @Override
    public Traces getTraces(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public Map<String, Long> pendingPaymentOrderNum(String supplierId) {
        throw new ServiceException();
    }

    @Override
    public void everyDayTask() {
        throw new ServiceException();
    }

    @Override
    public List<OrderFlow> orderFlowList(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public void updateByMemberInfo(User user) {
        throw new ServiceException();
    }

    @Override
    public void paidToSupplier() {
        throw new ServiceException();
    }

    @Override
    public Long getQuickDeliveryOrderCount(String storeId) {
        // 返回0而不是抛出异常，避免影响应用启动
        return 0L;
    }
}
