package cn.lili.modules.system.service;

import cn.lili.modules.system.entity.dos.Logistics;
import cn.lili.modules.system.entity.dos.SensitiveWords;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dto.LogisticsSetting;
import cn.lili.modules.system.entity.vo.ServiceFeeVO;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 服务费管理业务层
 *
 * <AUTHOR>
 * @since 2025-7-25
 */
public interface ServiceFeeService extends IService<ServiceFee> {


    /**
     * 获取已开启的服务费列表
     *
     * @return 服务费列表
     */
    List<ServiceFeeVO> getOpenList();


    boolean addOrUpdateFee(ServiceFee serviceFee);

    List<ServiceFee> getServiceFeeListByIds(List<String> ids);

    List<ServiceFee> getDefaultServiceFeeList();



}