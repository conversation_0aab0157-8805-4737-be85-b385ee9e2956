package cn.lili.modules.order.cart.render.impl;

import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.RenderStepEnums;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.cart.render.CartRenderStep;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;

import java.util.ArrayList;
import java.util.List;

import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.system.client.ServiceFeeClient;
import cn.lili.modules.system.entity.dos.ServiceFee;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 购物车渲染，将购物车中的各个商品，拆分到每个商家，形成购物车VO
 *
 * <AUTHOR>
 * @see CartVO
 */
@Service
@RequiredArgsConstructor
public class CartPriceRender implements CartRenderStep {


    @Override
    public RenderStepEnums step() {
        return RenderStepEnums.CART_PRICE;
    }

    @Override
    public void render(TradeDTO tradeDTO) {

        // 计算商品加个
        recountPrice(tradeDTO);

        //价格过滤 在购物车商品失效时，需要对价格进行初始化操作
        initPriceDTO(tradeDTO);

        // 处理阶段情况，如果购物车商品价格都为 0，最后一个商品设置成交金额为 0.01
        minPriceHandler(tradeDTO);
        buildServiceFee(tradeDTO);
        //构造cartVO
        buildCartPrice(tradeDTO);
        buildTradePrice(tradeDTO);
    }

    private void buildServiceFee (TradeDTO tradeDTO) {
        if (CollectionUtils.isNotEmpty(tradeDTO.getServiceFeeDTOList())) {
            tradeDTO.getPriceDetailDTO().setServiceFeeDTOList(tradeDTO.getServiceFeeDTOList());
        }
    }

    /**
     * 最低价格处理
     *
     * @param tradeDTO 购物车展示信息
     */
    private void minPriceHandler(TradeDTO tradeDTO) {
        tradeDTO.getCartList().forEach(cartVO -> {
            // 如果购物车商品价格都为 0，最后一个商品设置成交金额为 0.01
            if (cartVO.getCheckedSkuList().size() == cartVO.getCheckedSkuList().stream().filter(cartSkuVO -> {
                return cartSkuVO.getPriceDetailDTO().getFlowPrice() == 0;
            }).toList().size()) {

                //二次判定，如果是特殊购物车，不做最低金额处理，例如，赠品订单。
                //目前根据购物车商品的 locked 属性判定，如果 locked 为 true，则不做最低金额处理
                if (cartVO.getCheckedSkuList().stream().allMatch(CartSkuVO::getLocked)) {
                    return;
                }

                //最后一个商品设置成交金额为 0.01
                PriceDetailDTO priceDetailDTO =
                    cartVO.getCheckedSkuList().getLast().getPriceDetailDTO();
                // 如果有优惠券金额，优惠券金额减去 0.01
                if (priceDetailDTO.getCouponPrice() > 0) {
                    priceDetailDTO.resetCouponPrice(CurrencyUtil.sub(priceDetailDTO.getCouponPrice(), 0.01));
                }
                // 否则，优惠金额减去 0.01
                else if(priceDetailDTO.getDiscountPrice() > 0){
                    priceDetailDTO.resetDiscountPrice(CurrencyUtil.sub(priceDetailDTO.getDiscountPrice(), 0.01));
                }
                priceDetailDTO.minPriceHandler();
            }
        });
    }

    /**
     * 计算价格
     *
     * @param tradeDTO
     */
    private void recountPrice(TradeDTO tradeDTO) {
        tradeDTO.getCartList().forEach(cartVO -> cartVO.getSkuList().forEach(cartSkuVO -> cartSkuVO.getPriceDetailDTO().recount()));
    }

    /**
     * 特殊情况下对购物车金额进行护理
     *
     * @param tradeDTO
     */
    private void initPriceDTO(TradeDTO tradeDTO) {
        tradeDTO.getCartList().forEach(cartVO -> cartVO.setPriceDetailDTO(new PriceDetailDTO()));
        tradeDTO.setPriceDetailDTO(new PriceDetailDTO());
    }

    /**
     * 购物车价格
     *
     * @param tradeDTO 购物车展示信息
     */
    void buildCartPrice(TradeDTO tradeDTO) {
        //购物车列表
        List<CartVO> cartVOS = tradeDTO.getCartList();

        cartVOS.forEach(cartVO -> {

            cartVO.getPriceDetailDTO().accumulationPriceDTO(
                    cartVO.getCheckedSkuList().stream().filter(CartSkuVO::getChecked)
                            .map(CartSkuVO::getPriceDetailDTO).toList()
            );
            List<Integer> skuNum = cartVO.getSkuList().stream().filter(CartSkuVO::getChecked)
                    .map(CartSkuVO::getNum).toList();
            for (Integer num : skuNum) {
                cartVO.addGoodsNum(num);
            }
        });
    }


    /**
     * 初始化购物车
     *
     * @param tradeDTO 购物车展示信息
     */
    void buildTradePrice(TradeDTO tradeDTO) {
        tradeDTO.getPriceDetailDTO().accumulationPriceDTO(
                tradeDTO.getCartList().stream().map(CartVO::getPriceDetailDTO).toList());
        // 计算服务费
        if (CollectionUtils.isNotEmpty(tradeDTO.getPriceDetailDTO().getServiceFeeDTOList())) {
            Double serviceFee = tradeDTO.getPriceDetailDTO().getServiceFeeDTOList().stream().mapToDouble(ServiceFeeDTO::getTotalPrice).sum();
            tradeDTO.getPriceDetailDTO().setServiceFee(serviceFee);
            tradeDTO.getPriceDetailDTO().setFlowPrice(CurrencyUtil.add(tradeDTO.getPriceDetailDTO().getFlowPrice(), serviceFee));
        }
    }

}
