package cn.lili.modules.goods.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 *
 * 商品参数搜索条件
 *
 * <AUTHOR>
 * @since 2020-02-23 9:14:33
 */
@Data
@Schema(title = "商品参数搜索条件")
public class GoodsParamsSearchParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 4892783539320159200L;

    @TableField(value = "params_name")
    @Schema(title = "参数名称")
    private String paramsName;

    @TableField(value = "params_type")
    @Schema(title = "参数类型")
    private String paramsType;

}