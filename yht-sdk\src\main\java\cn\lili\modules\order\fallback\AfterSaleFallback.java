package cn.lili.modules.order.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.order.client.AfterSaleClient;
import cn.lili.modules.store.entity.dos.Store;
import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
public class AfterSaleFallback implements AfterSaleClient {
    @Override
    public void editAfterSaleStoreInfo(Store store) {
        throw new ServiceException();
    }

    @Override
    public void editAfterSaleUserInfo(User user) {
        throw new ServiceException();
    }

    @Override
    public List<AfterSale> queryByOrderSn(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public Boolean isAfterSale(String orderSn) {
        throw new ServiceException();
    }

    @Override
    public Double getReturnSuccessRate(String storeId) {
        // 返回0.0而不是抛出异常，避免影响应用启动
        return 0.0;
    }
}
