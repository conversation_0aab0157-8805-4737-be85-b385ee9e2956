package cn.lili.controller.system;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.system.entity.dos.Logistics;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.vo.ServiceFeeVO;
import cn.lili.modules.system.service.LogisticsService;
import cn.lili.modules.system.service.ServiceFeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "管理后台,服务费")
@RequestMapping("/system/service/fee")
@RequiredArgsConstructor
public class ServiceFeeController {

    private final ServiceFeeService serviceFeeService;

    @Operation(summary = "获取服务费列表")
    @GetMapping(value = "/list")
    public ResultMessage<List<ServiceFeeVO>> getList() {
        return ResultUtil.data(serviceFeeService.getOpenList());
    }

    @Operation(summary = "添加服务费")
    @PostMapping
    public ResultMessage<ServiceFee> save(ServiceFee serviceFee) {
        serviceFee.validateParams();
        serviceFee.setFeeStatus(true);
        serviceFeeService.addOrUpdateFee(serviceFee);
        return ResultUtil.data(serviceFee);
    }

    @Operation(summary = "编辑服务费")
    @PutMapping(value = "/{id}")
    public ResultMessage<ServiceFee> update(@NotNull @PathVariable String id, ServiceFee serviceFee) {
        serviceFee.setId(id);
        serviceFee.validateParams();
        serviceFeeService.addOrUpdateFee(serviceFee);
        return ResultUtil.data(serviceFee);
    }

    @Operation(summary = "删除服务费")
    @DeleteMapping(value = "/{id}")
    public ResultMessage<Object> delAllByIds(@PathVariable String id) {
        ServiceFee serviceFee = serviceFeeService.getById(id);
        if(null!=serviceFee){
            serviceFee.setDeleteFlag(true);
            serviceFeeService.addOrUpdateFee(serviceFee);
            return ResultUtil.success();
        }
        return ResultUtil.error();
    }
}
