package cn.lili.modules.store.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商家标签搜索参数
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class StoreTagSearchParams extends PageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "标签名称")
    private String tagName;

    @Schema(title = "标签类型")
    private String tagType;

    @Schema(title = "是否启用")
    private Boolean enabled;

    @Schema(title = "关键词", description = "搜索标签名称或描述")
    private String keyword;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();

        if (CharSequenceUtil.isNotEmpty(tagName)) {
            queryWrapper.like("tag_name", tagName);
        }

        if (CharSequenceUtil.isNotEmpty(tagType)) {
            queryWrapper.eq("tag_type", tagType);
        }

        if (enabled != null) {
            queryWrapper.eq("enabled", enabled);
        }

        if (CharSequenceUtil.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like("tag_name", keyword)
                    .or()
                    .like("tag_description", keyword)
            );
        }

        // 默认按排序字段升序，然后按创建时间降序
        queryWrapper.orderByAsc("sort_order");
        queryWrapper.orderByDesc("create_time");

        return queryWrapper;
    }
}
