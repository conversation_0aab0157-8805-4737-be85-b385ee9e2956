package cn.lili.modules.goods.entity.dto;

import cn.lili.common.validation.EnumValue;
import cn.lili.modules.goods.entity.dos.Category;
import cn.lili.modules.goods.entity.enums.PurchaseRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品导入DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsImportDTO {

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品卖点")
    private String sellingPoint;

    @Schema(title = "商品分类")
    private Category category;

    @Schema(title = "运费模板")
    private String template;

    @Schema(title = "计量单位")
    private String goodsUnit;

    @Schema(title = "发布状态")
    private Boolean release;

    @Schema(title = "商品图片")
    private List<String> images;

    private List<String> goodsGalleryList;

    @Schema(title = "成本价")
    private Double cost;

    @Schema(title = "销售价")
    private Double price;

    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "重量")
    private Double weight;

    @Schema(title = "货号")
    private String sn;

    @Schema(title = "详情")
    private String intro;

    @Schema(title = "规格图片")
    private String specsImage;

    @Schema(title = "商品规格")
    private String specs;

    @Schema(title = "支持代发")
    private Boolean supportProxy = false;

    @Schema(title = "支持采购")
    private Boolean supportPurchase = false;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型")
    @EnumValue(strValues = {"PHYSICAL_GOODS", "VIRTUAL_GOODS", "E_COUPON"}, message = "商品类型参数值错误")
    private String goodsType;

    @Schema(title = "采购规则")
    private PurchaseRuleEnum purchaseRule;

    /**
     * 批发采购规则
     */
    @Schema(title = "批发采购规则")
    private List<Wholesale> wholesaleList;

    @Schema(title = "货号")
    private String itemCode;
}
