# 商家标签管理 API 文档

## 概述

本文档描述了商家标签管理相关的API接口，包括标签的增删改查、状态管理等功能。

## 接口列表

### 1. 获取标签分页列表

**接口地址：** `GET /user/store/tag`

**接口描述：** 分页查询商家标签列表

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| tagName | String | 否 | 标签名称 | "优质商家" |
| tagType | String | 否 | 标签类型 | "QUALITY" |
| enabled | Boolean | 否 | 是否启用 | true |
| keyword | String | 否 | 关键词搜索 | "优质" |
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "tag_quality_001",
        "tagName": "优质商家",
        "tagDescription": "优质商家标签",
        "tagColor": "#52c41a",
        "tagIcon": null,
        "sortOrder": 1.00,
        "enabled": true,
        "tagType": "QUALITY",
        "tagTypeName": "优质商家",
        "remark": "系统默认标签",
        "createTime": "2025-07-23 10:00:00",
        "updateTime": "2025-07-23 10:00:00",
        "storeCount": 5
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取标签详情

**接口地址：** `GET /user/store/tag/{id}`

**接口描述：** 根据ID获取标签详情

**路径参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 是 | 标签ID |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "id": "tag_quality_001",
    "tagName": "优质商家",
    "tagDescription": "优质商家标签",
    "tagColor": "#52c41a",
    "tagIcon": null,
    "sortOrder": 1.00,
    "enabled": true,
    "tagType": "QUALITY",
    "tagTypeName": "优质商家",
    "remark": "系统默认标签",
    "createTime": "2025-07-23 10:00:00",
    "updateTime": "2025-07-23 10:00:00",
    "storeCount": 5
  }
}
```

### 3. 添加标签

**接口地址：** `POST /user/store/tag`

**接口描述：** 添加新的商家标签

**请求参数：**

```json
{
  "tagName": "新标签",
  "tagDescription": "新标签描述",
  "tagColor": "#ff0000",
  "tagIcon": "icon-star",
  "sortOrder": 10.00,
  "enabled": true,
  "tagType": "QUALITY",
  "remark": "备注信息"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| tagName | String | 是 | 标签名称 |
| tagDescription | String | 否 | 标签描述 |
| tagColor | String | 否 | 标签颜色（十六进制） |
| tagIcon | String | 否 | 标签图标 |
| sortOrder | BigDecimal | 是 | 排序（数值越小排序越靠前） |
| enabled | Boolean | 否 | 是否启用（默认true） |
| tagType | String | 否 | 标签类型 |
| remark | String | 否 | 备注 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "id": "new_tag_id",
    "tagName": "新标签",
    "tagDescription": "新标签描述",
    "tagColor": "#ff0000",
    "sortOrder": 10.00,
    "enabled": true,
    "tagType": "QUALITY",
    "createTime": "2025-07-23 10:30:00"
  }
}
```

### 4. 编辑标签

**接口地址：** `PUT /user/store/tag`

**接口描述：** 编辑商家标签

**请求参数：**

```json
{
  "id": "tag_quality_001",
  "tagName": "编辑后标签",
  "tagDescription": "编辑后描述",
  "tagColor": "#00ff00",
  "sortOrder": 5.00,
  "enabled": true,
  "tagType": "RECOMMEND",
  "remark": "编辑后备注"
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "id": "tag_quality_001",
    "tagName": "编辑后标签",
    "tagDescription": "编辑后描述",
    "tagColor": "#00ff00",
    "sortOrder": 5.00,
    "enabled": true,
    "tagType": "RECOMMEND",
    "updateTime": "2025-07-23 11:00:00"
  }
}
```

### 5. 删除标签

**接口地址：** `DELETE /user/store/tag/{id}`

**接口描述：** 删除商家标签

**路径参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 是 | 标签ID |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": true
}
```

### 6. 批量删除标签

**接口地址：** `DELETE /user/store/tag/batch`

**接口描述：** 批量删除商家标签

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | List<String> | 是 | 标签ID列表 |

**请求示例：**

```
DELETE /user/store/tag/batch?ids=tag1,tag2,tag3
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": true
}
```

### 7. 启用/禁用标签

**接口地址：** `PUT /user/store/tag/{id}/status`

**接口描述：** 启用或禁用标签

**路径参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 是 | 标签ID |

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| enabled | Boolean | 是 | 启用状态 |

**请求示例：**

```
PUT /user/store/tag/tag_quality_001/status?enabled=false
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": true
}
```

### 8. 批量更新标签状态

**接口地址：** `PUT /user/store/tag/batch/status`

**接口描述：** 批量更新标签状态

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| ids | List<String> | 是 | 标签ID列表 |
| enabled | Boolean | 是 | 启用状态 |

**请求示例：**

```
PUT /user/store/tag/batch/status?ids=tag1,tag2&enabled=true
```

### 9. 获取启用的标签列表

**接口地址：** `GET /user/store/tag/enabled`

**接口描述：** 获取所有启用的标签列表

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": [
    {
      "id": "tag_quality_001",
      "tagName": "优质商家",
      "tagDescription": "优质商家标签",
      "tagColor": "#52c41a",
      "sortOrder": 1.00,
      "enabled": true,
      "tagType": "QUALITY"
    }
  ]
}
```

### 10. 根据标签类型获取标签列表

**接口地址：** `GET /user/store/tag/type/{tagType}`

**接口描述：** 根据标签类型获取标签列表

**路径参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| tagType | String | 是 | 标签类型 |

**标签类型说明：**

- `QUALITY` - 优质商家
- `RECOMMEND` - 推荐商家
- `HOT` - 热门商家
- `NEW` - 新商家
- `REAL_PHOTO` - 实拍商家
- `FAST_DELIVERY` - 发货快商家

### 11. 检查标签名称是否存在

**接口地址：** `GET /user/store/tag/check`

**接口描述：** 检查标签名称是否已存在

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| tagName | String | 是 | 标签名称 |
| excludeId | String | 否 | 排除的标签ID（编辑时使用） |

### 12. 获取标签类型枚举

**接口地址：** `GET /user/store/tag/types`

**接口描述：** 获取所有标签类型枚举

### 13. 初始化默认标签

**接口地址：** `POST /user/store/tag/init`

**接口描述：** 初始化系统默认标签

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 50015 | 商家标签不存在 | 检查标签ID是否正确 |
| 50016 | 标签名称已存在 | 使用不同的标签名称 |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查登录状态和权限 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 使用场景

### 1. 标签管理后台

管理员可以通过标签管理界面对商家标签进行增删改查操作：

```javascript
// 获取标签列表
fetch('/api/user/store/tag?page=1&size=10')

// 添加新标签
fetch('/api/user/store/tag', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tagName: '新标签',
    tagDescription: '新标签描述',
    sortOrder: 10,
    enabled: true
  })
})

// 编辑标签
fetch('/api/user/store/tag', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    id: 'tag_id',
    tagName: '编辑后标签',
    enabled: false
  })
})
```

### 2. 店铺标签设置

为店铺设置标签时，可以从标签库中选择：

```javascript
// 获取启用的标签列表供选择
fetch('/api/user/store/tag/enabled')
  .then(response => response.json())
  .then(data => {
    // 渲染标签选择器
    renderTagSelector(data.result);
  });
```

### 3. 标签分类展示

根据标签类型展示不同类别的标签：

```javascript
// 获取优质商家标签
fetch('/api/user/store/tag/type/QUALITY')

// 获取推荐商家标签
fetch('/api/user/store/tag/type/RECOMMEND')
```

## 注意事项

1. **标签名称唯一性**：同一个标签名称不能重复
2. **删除限制**：正在被店铺使用的标签无法删除
3. **排序规则**：数值越小排序越靠前
4. **颜色格式**：标签颜色使用十六进制格式，如 #ff0000
5. **权限控制**：标签管理需要相应的管理员权限
