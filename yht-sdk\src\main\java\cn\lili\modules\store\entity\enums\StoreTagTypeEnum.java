package cn.lili.modules.store.entity.enums;

/**
 * 商家标签类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public enum StoreTagTypeEnum {

    /**
     * 优质商家
     */
    QUALITY("QUALITY", "优质商家", "#52c41a"),

    /**
     * 推荐商家
     */
    RECOMMEND("RECOMMEND", "推荐商家", "#1890ff"),

    /**
     * 热门商家
     */
    HOT("HOT", "热门商家", "#ff4d4f"),

    /**
     * 新商家
     */
    NEW("NEW", "新商家", "#faad14"),

    /**
     * 实拍商家
     */
    REAL_PHOTO("REAL_PHOTO", "实拍商家", "#722ed1"),

    /**
     * 发货快商家
     */
    FAST_DELIVERY("FAST_DELIVERY", "发货快商家", "#13c2c2");

    private final String code;
    private final String name;
    private final String defaultColor;

    StoreTagTypeEnum(String code, String name, String defaultColor) {
        this.code = code;
        this.name = name;
        this.defaultColor = defaultColor;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDefaultColor() {
        return defaultColor;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 标签类型代码
     * @return 对应的枚举值
     */
    public static StoreTagTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (StoreTagTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的标签类型
     *
     * @param code 标签类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
