package cn.lili.modules.page.fallback;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.page.client.PageDataClient;
import cn.lili.modules.page.entity.dos.PageData;
import cn.lili.modules.page.entity.dto.PageDataListParams;
import cn.lili.modules.page.entity.dto.PageDataSaveDTO;
import cn.lili.modules.page.entity.dto.PageDataSearchParams;
import cn.lili.modules.page.entity.dto.PageDataShowParams;
import cn.lili.modules.page.entity.vos.PageDataVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 页面数据服务降级处理
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Component
public class PageDataFallback implements PageDataClient {

    @Override
    public PageData getById(String id) {
        log.error("PageDataClient.getById 服务调用失败，参数：id={}", id);
        return null;
    }

    @Override
    public PageDataVO getPageData(PageDataShowParams pageDataShowParams) {
        log.error("PageDataClient.getPageData 服务调用失败，参数：{}", pageDataShowParams);
        return new PageDataVO(null);
    }

    @Override
    public Page<PageData> getPageDataList(PageDataListParams params) {
        log.error("PageDataClient.getPageDataList 服务调用失败，参数：{}", params);
        Page<PageData> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        if (params != null && params.getPageVO() != null) {
            page.setCurrent(params.getPageVO().getPageNumber());
            page.setSize(params.getPageVO().getPageSize());
        } else {
            page.setCurrent(1);
            page.setSize(10);
        }
        return page;
    }

    @Override
    public PageData addPageData(PageDataSaveDTO pageDataSaveDTO) {
        log.error("PageDataClient.addPageData 服务调用失败，参数：{}", pageDataSaveDTO);
        return null;
    }

    @Override
    public PageData updatePageData(PageDataSaveDTO pageDataSaveDTO) {
        log.error("PageDataClient.updatePageData 服务调用失败，参数：{}", pageDataSaveDTO);
        return null;
    }

    @Override
    public PageData updateStatus(String id, Boolean enable) {
        log.error("PageDataClient.updateStatus 服务调用失败，参数：id={}, enable={}", id, enable);
        return null;
    }

    @Override
    public PageData releaseIndex(String id) {
        log.error("PageDataClient.releaseIndex 服务调用失败，参数：id={}", id);
        return null;
    }

    @Override
    public Boolean deletePageData(String id) {
        log.error("PageDataClient.deletePageData 服务调用失败，参数：id={}", id);
        return false;
    }

    @Override
    public PageDataVO getPageDataByType(String pageType, String extendId) {
        log.error("PageDataClient.getPageDataByType 服务调用失败，参数：pageType={}, extendId={}", pageType, extendId);
        return new PageDataVO(null);
    }

    @Override
    public Boolean checkPageDataExists(String pageType, String extendId) {
        log.error("PageDataClient.checkPageDataExists 服务调用失败，参数：pageType={}, extendId={}", pageType, extendId);
        return false;
    }

    @Override
    public List<PageData> getEnabledPageDataList(String pageType) {
        log.error("PageDataClient.getEnabledPageDataList 服务调用失败，参数：pageType={}", pageType);
        return Collections.emptyList();
    }
}
