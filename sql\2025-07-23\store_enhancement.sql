-- 店铺功能增强 - 添加标签、实拍和发货快字段
-- 执行时间: 2025-07-23
-- 描述: 为店铺表添加标签、实拍商品和发货快字段，支持1.推荐，2.上新，3.实拍，4.发货快，5.热销查询功能

-- 为 li_store 表添加新字段
ALTER TABLE `li_store`
ADD COLUMN `store_tag` VARCHAR(255) NULL COMMENT '店铺标签' AFTER `market_detail`,
ADD COLUMN `real_photo` TINYINT(1) DEFAULT 0 COMMENT '是否实拍商品，0-否，1-是' AFTER `store_tag`,
ADD COLUMN `fast_delivery` TINYINT(1) DEFAULT 0 COMMENT '是否发货快，0-否，1-是' AFTER `real_photo`;

-- 为新字段添加索引以提高查询性能
CREATE INDEX `idx_store_tag` ON `li_store` (`store_tag`);
CREATE INDEX `idx_real_photo` ON `li_store` (`real_photo`);
CREATE INDEX `idx_fast_delivery` ON `li_store` (`fast_delivery`);
CREATE INDEX `idx_create_time` ON `li_store` (`create_time`);
CREATE INDEX `idx_goods_num` ON `li_store` (`goods_num`);

-- 更新现有数据的默认值
UPDATE `li_store` SET `real_photo` = 0 WHERE `real_photo` IS NULL;
UPDATE `li_store` SET `fast_delivery` = 0 WHERE `fast_delivery` IS NULL;

-- 创建商家标签表
CREATE TABLE `li_store_tag` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `delete_flag` bit(1) DEFAULT b'0' COMMENT '删除标志 true/false 删除/未删除',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `tag_name` varchar(255) NOT NULL COMMENT '标签名称',
  `tag_description` varchar(500) DEFAULT NULL COMMENT '标签描述',
  `tag_color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `tag_icon` varchar(255) DEFAULT NULL COMMENT '标签图标',
  `sort_order` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '排序',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `tag_type` varchar(50) DEFAULT NULL COMMENT '标签类型',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_tag_type` (`tag_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商家标签表';

-- 插入默认标签数据
INSERT INTO `li_store_tag` (`id`, `create_by`, `create_time`, `delete_flag`, `update_by`, `update_time`, `tag_name`, `tag_description`, `tag_color`, `tag_type`, `sort_order`, `enabled`, `remark`) VALUES
('tag_quality_001', 'system', NOW(), 0, 'system', NOW(), '优质商家', '优质商家标签', '#52c41a', 'QUALITY', 1.00, 1, '系统默认标签'),
('tag_recommend_002', 'system', NOW(), 0, 'system', NOW(), '推荐商家', '推荐商家标签', '#1890ff', 'RECOMMEND', 2.00, 1, '系统默认标签'),
('tag_hot_003', 'system', NOW(), 0, 'system', NOW(), '热门商家', '热门商家标签', '#ff4d4f', 'HOT', 3.00, 1, '系统默认标签'),
('tag_new_004', 'system', NOW(), 0, 'system', NOW(), '新商家', '新商家标签', '#faad14', 'NEW', 4.00, 1, '系统默认标签'),
('tag_real_photo_005', 'system', NOW(), 0, 'system', NOW(), '实拍商家', '实拍商家标签', '#722ed1', 'REAL_PHOTO', 5.00, 1, '系统默认标签'),
('tag_fast_delivery_006', 'system', NOW(), 0, 'system', NOW(), '发货快商家', '发货快商家标签', '#13c2c2', 'FAST_DELIVERY', 6.00, 1, '系统默认标签');
