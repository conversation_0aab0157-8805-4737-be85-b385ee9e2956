<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.lili.sbc</groupId>
        <artifactId>service-parent</artifactId>
        <version>1.0.2</version>
        <relativePath>../pom.xml</relativePath>
    </parent>


    <artifactId>system-service</artifactId>
    <version>1.0.2</version>
    <name>system-service</name>
    <description>yht-系统服务</description>

    <dependencies>
        <dependency>
            <groupId>cn.lili.sbc</groupId>
            <artifactId>amqp</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>cn.lili.sbc</groupId>
            <artifactId>lili-logs</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea</artifactId>
            <version>1.2.9</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.aliyun/tea-util -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-util</artifactId>
            <version>0.2.21</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.aliyun/openapiutil -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>openapiutil</artifactId>
            <version>0.2.1</version>
        </dependency>
    </dependencies>
</project>
