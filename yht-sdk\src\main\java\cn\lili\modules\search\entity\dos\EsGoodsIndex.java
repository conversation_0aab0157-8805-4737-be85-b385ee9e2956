package cn.lili.modules.search.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.utils.GsonUtils;
import cn.lili.elasticsearch.EsSuffix;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.GoodsParamsDTO;
import cn.lili.modules.goods.entity.dto.Wholesale;
import cn.lili.modules.goods.entity.enums.GoodsTagsTypeEnum;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.promotion.entity.enums.CouponGetEnum;
import cn.lili.modules.promotion.tools.PromotionTools;
import cn.lili.modules.store.entity.enums.FreightTemplateEnum;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xkcoding.http.util.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * 商品索引
 *
 * <AUTHOR>
 **/
@Data
@Document(indexName = "#{@elasticsearchProperties.indexPrefix}_" + EsSuffix.GOODS_INDEX_NAME)
@Setting(settingPath = "elasticsearch/es-goods-setting.json")
@ToString
@NoArgsConstructor
@Accessors(chain = true)
public class EsGoodsIndex implements Serializable {

    @Serial
    private static final long serialVersionUID = -6856471777036048874L;

    @Id
    @Schema(title = "Id")
    private String id;

    /**
     * 商品id
     */
    @Schema(title = "商品Id")
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String goodsId;

    @Schema(title = "商品Id")
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String skuId;

    /**
     * 商品名称
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngramIndexAnalyzer"), otherFields = {
            @InnerField(suffix = "SPY", type = FieldType.Text, analyzer = "pinyiSimpleIndexAnalyzer"),
            @InnerField(suffix = "FPY", type = FieldType.Text, analyzer = "pinyiFullIndexAnalyzer"),
            @InnerField(suffix = "IKS", type = FieldType.Text, analyzer = "ikIndexAnalyzer"),
    })
    @Schema(title = "商品名称")
    private String goodsName;

    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngramIndexAnalyzer"), otherFields = {
            @InnerField(suffix = "SPY", type = FieldType.Text, analyzer = "pinyiSimpleIndexAnalyzer"),
            @InnerField(suffix = "FPY", type = FieldType.Text, analyzer = "pinyiFullIndexAnalyzer"),
            @InnerField(suffix = "IKS", type = FieldType.Text, analyzer = "ikIndexAnalyzer"),
    })
    @Schema(title = "spu名称")
    private String spuName;

    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngramIndexAnalyzer"), otherFields = {
            @InnerField(suffix = "SPY", type = FieldType.Text, analyzer = "pinyiSimpleIndexAnalyzer"),
            @InnerField(suffix = "FPY", type = FieldType.Text, analyzer = "pinyiFullIndexAnalyzer"),
            @InnerField(suffix = "IKS", type = FieldType.Text, analyzer = "ikIndexAnalyzer"),
    })
    @Schema(title = "sku名称")
    private String skuName;

    /**
     * 商品编号
     */
    @Field(type = FieldType.Keyword)
    @Schema(title = "商品编号")
    private String sn;

    /**
     * 卖家id
     */
    @Field(type = FieldType.Text)
    @Schema(title = "卖家id")
    private String storeId;

    /**
     * 店铺区域 市
     */
    @Field(type = FieldType.Text)
    @Schema(title = "店铺区域 市")
    private String storeRegionCity;
    /**
     * 店铺区域 区
     */
    @Field(type = FieldType.Text)
    @Schema(title = "店铺区域 区")
    private String storeRegionArea;
    /**
     * 店铺区域 市场
     */
    @Field(type = FieldType.Text)
    @Schema(title = "店铺区域 市场")
    private String storeRegionMarket;

    /**
     * 卖家名称
     */
    @Field(type = FieldType.Text, fielddata = true)
    @Schema(title = "卖家名称")
    private String storeName;

    @Field(type = FieldType.Text)
    @Schema(title = "店铺logo")
    private String storeLogo;


    /**
     * 销量
     */
    @Field(type = FieldType.Integer)
    @Schema(title = "销量")
    private Integer buyCount;

    /**
     * 小图
     */
    @Schema(title = "小图")
    private String small;

    /**
     * 缩略图
     */
    @Schema(title = "缩略图")
    private String thumbnail;

    /**
     * 品牌id
     */
    @Field(type = FieldType.Text, fielddata = true)
    @Schema(title = "品牌id")
    private String brandId;

    /**
     * 品牌名称
     */
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @Schema(title = "品牌名称")
    private String brandName;

    /**
     * 品牌图片地址
     */
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @Schema(title = "品牌图片地址")
    private String brandUrl;

    /**
     * 分类path
     */
    @Field(type = FieldType.Text, fielddata = true)
    @Schema(title = "分类path")
    private String categoryPath;

    /**
     * 分类名称path
     */
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @Schema(title = "分类名称path")
    private String categoryNamePath;

    /**
     * 店铺分类id
     */
    @Field(type = FieldType.Text, fielddata = true)
    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    /**
     * 店铺分类名称
     */
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @Schema(title = "店铺分类名称")
    private String storeCategoryNamePath;

    /**
     * 商品价格
     */
    @Field(type = FieldType.Double)
    @Schema(title = "商品价格")
    private Double price;

    /**
     * 促销价
     */
    @Field(type = FieldType.Double)
    @Schema(title = "促销价")
    private Double promotionPrice;

    /**
     * 如果是积分商品需要使用的积分
     */
    @Field(type = FieldType.Integer)
    @Schema(title = "积分商品需要使用的积分")
    private Integer points;

    /**
     * 评价数量
     */
    @Field(type = FieldType.Integer)
    @Schema(title = "评价数量")
    private Integer commentNum;

    /**
     * 好评数量
     */
    @Field(type = FieldType.Integer)
    @Schema(title = "好评数量")
    private Integer highPraiseNum;

    /**
     * 好评率
     */
    @Field(type = FieldType.Double)
    @Schema(title = "好评率")
    private Double grade;

    /**
     * 是否自营
     */
    @Field(type = FieldType.Boolean)
    @Schema(title = "是否自营")
    private Boolean selfOperated;

    /**
     * 是否为推荐商品
     */
    @Field(type = FieldType.Boolean)
    @Schema(title = "是否为推荐商品")
    private Boolean recommend;

    /**
     * 销售模式
     */
    @Field(type = FieldType.Text)
    @Schema(title = "销售模式")
    private String salesModel;

    /**
     * 卖点
     */
    @MultiField(mainField = @Field(type = FieldType.Text, fielddata = true), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @Schema(title = "卖点")
    private String sellingPoint;

    /**
     * 商品视频
     */
    @Field(type = FieldType.Text)
    @Schema(title = "商品视频")
    private String goodsVideo;

    @Schema(title = "商品发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date releaseTime;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @Field(type = FieldType.Text)
    private String goodsType;

    @Schema(title = "商品类型CODE", requiredMode = Schema.RequiredMode.REQUIRED)
    @Field(type = FieldType.Integer)
    private Integer goodsTypeCode;

    @Schema(title = "商品sku基础分数", requiredMode = Schema.RequiredMode.REQUIRED)
    @Field(type = FieldType.Integer)
    private Integer skuSource = 100;

    @Schema(title = "商品sku库存", requiredMode = Schema.RequiredMode.REQUIRED)
    @Field(type = FieldType.Integer)
    private Integer quantity;

    @Schema(title = "商品图片集合")
    @Field(type = FieldType.Keyword)
    private List<String> goodsGalleryList;

    @Schema(title = "规格信息json", hidden = true)
    private String specs;

    @Schema(title = "商品参数json", hidden = true)
    private String params;

    /**
     * 商品属性（参数和规格）
     */
    @Field(type = FieldType.Nested)
    private List<EsGoodsAttribute> attrList;

    /**
     * 商品促销活动集合
     * key 为 促销活动类型
     *
     * @see PromotionTypeEnum
     * value 为 促销活动实体信息
     */
    @Field(type = FieldType.Keyword)
    @Schema(title = "商品促销活动集合JSON，key 为 促销活动类型，value 为 促销活动实体信息 ")
    private String promotionMapJson;

    @Schema(title = "供应商商品ID")
    private String supplierGoodsId;

    @Schema(title = "供应商SKU ID")
    private String supplierSkuId;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "所属场景")
    private String scene;
    /**
     * 审核状态
     */
    @Field(type = FieldType.Keyword)
    @Schema(title = "审核状态")
    private String authFlag;

    /**
     * 上架状态
     */
    @Field(type = FieldType.Keyword)
    @Schema(title = "上架状态")
    private String marketEnable;

    @Schema(title = "重量")
    @Field(type = FieldType.Double)
    private Double weight;

    @Schema(title = "计量单位")
    private String goodsUnit;

    /**
     * @see cn.lili.modules.goods.entity.dto.Wholesale
     */
    @Schema(title = "批发商品消费规则列表 List<Wholesale>")
    @Field(type = FieldType.Text)
    private String wholesale;

    /**
     * 详情
     */
    @Field(type = FieldType.Text)
    @Schema(title = "详情")
    private String intro;

    /**
     * 商品移动端详情
     */
    @Field(type = FieldType.Text)
    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "分类名称")
    @Field(type = FieldType.Keyword)
    private List<String> categoryName;

    @Schema(title = "sku列表")
    @Field(type = FieldType.Object, enabled = false)
    private String skuList;

    @Schema(title = "运费模版信息")
    @Field(type = FieldType.Object)
    private FreightTemplateVO freightTemplate;

    @Schema(title = "商品标签")
    @Field(type = FieldType.Keyword)
    private List<String> tags;

    @Schema(title = "忽略的标签")
    @Field(type = FieldType.Keyword)
    private List<String> ignoreTags;



    @Schema(title = "省")
    private String province;

    @Schema(title = "市")
    private String city;

    @Schema(title = "区")
    private String district;

    @Schema(title = "市场")
    private String market;
    @TableField(exist = false)
    private String marketName;

    @Schema(title = "市场详细地址")
    private String marketDetail;

    @Schema(title = "图片文件资源包")
    private String imageZipFile;

    @Schema(title = "是否是实拍商品")
    private Boolean isRealShoot;

    @Schema(title = "是否现货商品")
    private Boolean isInStock;

    @Schema(title = "搜同款商品ID")
    private String productId;   

    @Field(type = FieldType.Boolean)
    @Schema(title = "是否支持退现")
    private Boolean isBackMoney;

    @Schema(title = "预售截止时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preSaleDeadline;

    public EsGoodsIndex(GoodsSku sku) {
        if (sku != null) {
            BeanUtils.copyProperties(sku, this);
            //设置商品ID 为 es 主键
//            this.setId(sku.getGoodsId());
            this.setSkuId(sku.getId());
            this.setBuyCount(sku.getBuyCount() != null ? sku.getBuyCount() : 0);
            this.setReleaseTime(sku.getUpdateTime() != null ? sku.getUpdateTime() : sku.getCreateTime());
            if (CharSequenceUtil.isNotEmpty(sku.getGoodsGallery())) {
                this.setGoodsGalleryList(Arrays.asList(sku.getGoodsGallery().split(",")));
            }
        }
    }

    public EsGoodsIndex(GoodsSku sku, Date createDate) {
        this(sku);
        this.setReleaseTime(createDate);
    }

    public void setAttrListByParams(List<GoodsParamsDTO> goodsParamDTOS) {
        //如果参数不为空
        if (goodsParamDTOS != null && !goodsParamDTOS.isEmpty()) {
            //接受不了参数索引
            List<EsGoodsAttribute> attributes = new ArrayList<>();
            //循环参数分组
            goodsParamDTOS.forEach(goodsParamGroup -> {
                //如果参数有配置，则增加索引
                if (goodsParamGroup.getGoodsParamsItemDTOList() != null && !goodsParamGroup.getGoodsParamsItemDTOList().isEmpty()) {
                    //循环分组的内容
                    goodsParamGroup.getGoodsParamsItemDTOList().forEach(goodsParam -> {
                                //如果字段需要索引，则增加索引字段
                                if (goodsParam.getIsIndex() != null && goodsParam.getIsIndex() == 1) {
                                    EsGoodsAttribute attribute = new EsGoodsAttribute();
                                    attribute.setType(goodsParam.getIsIndex());
                                    attribute.setName(goodsParam.getParamName());
                                    attribute.setValue(goodsParam.getParamValue());
                                    attribute.setSort(goodsParam.getSort());
                                    attributes.add(attribute);
                                }
                            }
                    );
                }

            });
            this.setAttrList(attributes);
        }
    }

    public List<Wholesale> getWholesaleList() {
        if (StringUtil.isNotEmpty(this.getWholesale())) {
            return JSONUtil.toList(JSONUtil.parseArray(this.getWholesale()), Wholesale.class);
        } else {
            return new ArrayList<>();
        }
    }

    public Double getPromotionPrice() {
        if (this.getPromotionMap().keySet().stream().anyMatch(j -> j.contains(PromotionTypeEnum.SECKILL.name()) || j.contains(PromotionTypeEnum.MINUS.name()) || j.contains(PromotionTypeEnum.PINTUAN.name()))) {
            return promotionPrice;
        }
        return 0d;
    }

    public List<String> getTags() {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }

        // 当前标签(除需要根据时间判断的标签)
        List<String> currentTags = new ArrayList<>(this.tags.stream().filter(i ->
                        (i.contains(GoodsTagsTypeEnum.CHOICE.getDescription())) ||
                        i.contains(GoodsTagsTypeEnum.NEW.getDescription()) ||
                        i.contains(GoodsTagsTypeEnum.HOT.getDescription()) ||
                        i.contains(GoodsTagsTypeEnum.SELL.getDescription()))
                .toList());
        Set<String> strings = this.getPromotionMap().keySet();
        // 促销类型
        List<PromotionTypeEnum> promotionTypes = Arrays.asList(
                PromotionTypeEnum.SECKILL, PromotionTypeEnum.MINUS, PromotionTypeEnum.FULL_DISCOUNT, PromotionTypeEnum.PINTUAN, PromotionTypeEnum.GIFT
        );
        // 标签类型(与促销类型一一对应)
        List<GoodsTagsTypeEnum> goodsTagsTypes = Arrays.asList(
                GoodsTagsTypeEnum.SECKILL, GoodsTagsTypeEnum.REDUCE, GoodsTagsTypeEnum.FULL_REDUCE, GoodsTagsTypeEnum.GROUP, GoodsTagsTypeEnum.GIFT
        );

        // 促销标签
        for (int i = 0; i < promotionTypes.size(); i++) {
            int finalI = i;
            if (strings.stream().anyMatch(j -> j.contains(promotionTypes.get(finalI).name()))) {
                this.tags.stream().filter(j -> j.contains(goodsTagsTypes.get(finalI).getDescription())).findFirst().ifPresent(currentTags::add);
            }
        }

        // 免邮
        if ((this.getFreightTemplate() == null || CharSequenceUtil.isEmpty(this.getFreightTemplate().getId()) || FreightTemplateEnum.FREE.name().equals(this.getFreightTemplate().getPricingMethod())) && !goodsType.equals(GoodsTypeEnum.VIRTUAL_GOODS.name())) {
            this.tags.stream().filter(i -> i.contains(GoodsTagsTypeEnum.FREE.getDescription())).findFirst().ifPresent(currentTags::add);
        }

        // 自营
        if (Boolean.TRUE.equals(this.getSelfOperated())) {
            this.tags.stream().filter(i -> i.contains(GoodsTagsTypeEnum.SELF.getDescription())).findFirst().ifPresent(currentTags::add);
        }

        // 忽略活动类型的优惠券活动
        this.getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.COUPON.name())).forEach(i -> {
            if (i.getValue() instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) i.getValue();
                if (map.get("getType") != null && CouponGetEnum.FREE.name().equals(map.get("getType").toString()) && currentTags.stream().noneMatch(j -> j.contains(GoodsTagsTypeEnum.COUPON.getDescription()))) {
                    this.tags.stream().filter(j -> j.contains(GoodsTagsTypeEnum.COUPON.getDescription())).findFirst().ifPresent(currentTags::add);
                }
            }
        });

        // 移除忽略标签
        if (this.ignoreTags != null && !this.ignoreTags.isEmpty()) {
            currentTags.removeIf(tag -> this.ignoreTags.contains(JSONUtil.parseObj(tag).getStr("name")));
        }


        // 根据sort排序
//        currentTags.sort(Comparator.comparingInt(i -> JSONUtil.parseObj(i).getInt("sort")));
        return currentTags;
    }

    public Map<String, Object> getOriginPromotionMap() {
        return GsonUtils.fromJson(this.getPromotionMapJson(), Map.class);
    }

    public Map<String, Object> getPromotionMap() {
        return PromotionTools.filterInvalidPromotionsMap(GsonUtils.fromJson(this.getPromotionMapJson(), Map.class));
    }
}
