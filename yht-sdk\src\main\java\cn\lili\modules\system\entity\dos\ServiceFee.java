package cn.lili.modules.system.entity.dos;

import cn.lili.common.utils.StringUtils;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 服务费
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_service_fee")
@Schema(title = "服务费管理")
@EqualsAndHashCode(callSuper = true)
public class ServiceFee extends BaseStandardEntity {

    private static final long serialVersionUID = -3936333221051322353L;

    @Schema(title = "服务费名称")
    private String name;

    @Schema(title = "价格")
    private Double price = 0.0;

    @Schema(title = "数量")
    private int nums = 1;

    @Schema(title = "是否默认")
    private Boolean defaultFlag = false;

    @Schema(title = "父级id")
    private String parentId = "0";

    @Schema(title = "是否启用")
    private Boolean feeStatus = true;

    public boolean validateParams() {
        if (StringUtils.isBlank(name)){
            ValidateParamsUtil.throwInvalidParamError("服务费名称不能为空");
        }
        return true;
    }
}