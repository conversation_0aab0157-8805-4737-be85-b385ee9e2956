package cn.lili.modules.system.entity.params;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分类查询参数
 *
 * <AUTHOR>
 * @since 2020/12/1
 **/
@Data
public class RegionSearchParams {

    @Schema(title = "分类名称")
    private String name;

    @Schema(title = "父id")
    private String parentId;

    @Schema(title = "父id")
    private String[] parentIds;

    @Schema(title = "层级")
    private Integer level;

    @Schema(title = "排序值")
    private BigDecimal sortOrder;

    @Schema(title = "父节点名称")
    private String parentTitle;



    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CharSequenceUtil.isNotBlank(name), "name", name);
        queryWrapper.eq(CharSequenceUtil.isNotBlank(parentId), "parent_id", parentId);
        queryWrapper.in(parentIds != null, "parent_id", parentIds);
        return queryWrapper;
    }
}
