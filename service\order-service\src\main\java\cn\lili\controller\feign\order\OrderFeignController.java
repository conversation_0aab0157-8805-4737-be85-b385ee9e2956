package cn.lili.controller.feign.order;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.aftersale.service.AfterSaleService;
import cn.lili.modules.order.cart.render.TradeBuilder;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderComplaint;
import cn.lili.modules.order.order.entity.dos.OrderFlow;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.order.order.integration.OrderIntegrationHandler;
import cn.lili.modules.order.order.integration.PintuanIntegrationHandler;
import cn.lili.modules.order.order.service.OrderComplaintService;
import cn.lili.modules.order.order.service.OrderFlowService;
import cn.lili.modules.order.order.service.OrderLogService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.TradeService;
import cn.lili.modules.order.order.task.OrderEveryDayTask;
import cn.lili.modules.order.trade.entity.dos.OrderLog;
import cn.lili.modules.payment.entity.dto.PaymentCallback;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单 feign client
 *
 * <AUTHOR>
 * @version v1.0 2021-12-07 18:34
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class OrderFeignController implements OrderClient {

    private final OrderService orderService;

    private final AfterSaleService afterSaleService;

    private final TradeBuilder tradeBuilder;

    private final OrderEveryDayTask orderEveryDayTask;

    private final OrderFlowService orderFlowService;

    private final OrderIntegrationHandler orderIntegrationHandler;

    private final PintuanIntegrationHandler pintuanIntegrationHandler;

    private final OrderLogService orderLogService;

    private final OrderComplaintService orderComplaintService;

    private final TradeService tradeService;

    @Override
    public Order getBySn(String sn) {
        return orderService.getBySn(sn);
    }

    @Override
    public void paymentCallback(PaymentCallback paymentCallback) {
        tradeService.paymentOrder(paymentCallback);
    }

    /**
     * 系统取消订单
     *
     * @param orderSn 订单编号
     * @param reason  错误原因
     */
    @Override
    public void systemCancel(String orderSn, String reason,Boolean refundMoney) {
        orderIntegrationHandler.systemCancel(orderSn, reason, refundMoney);
    }

    @Override
    public Double getPaymentTotal(String orderSn) {
        return tradeBuilder.getPaymentTotal(orderSn);
    }

    @Override
    public OrderDetailVO queryDetail(String sn) {
        return orderService.queryDetail(sn);
    }

    @Override
    public List<Order> getByTradeSn(String sn) {
        return orderService.getByTradeSn(sn);
    }

    /**
     * 订单信息
     *
     * @param orderSearchParams 查询参数
     * @return 订单信息
     */
    @Override
    public List<Order> queryListByParams(OrderSearchParams orderSearchParams) {
        return orderService.queryListByParams(orderSearchParams);
    }

    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    @Override
    public long queryCountByPromotion(
            String orderPromotionType, String payStatus, String parentOrderSn, String orderSn) {
        return orderService.queryCountByPromotion(orderPromotionType, payStatus, parentOrderSn, orderSn);
    }

    /**
     * 检查是否开始虚拟成团
     *
     * @param pintuanId   拼团活动id
     * @param requiredNum 成团人数
     * @param fictitious  是否开启成团
     */
    @Override
    public void checkFictitiousOrder(String pintuanId, Integer requiredNum, Boolean fictitious) {
        pintuanIntegrationHandler.checkFictitiousOrder(pintuanId, requiredNum, fictitious);
    }

    @Override
    public void save(Order order) {
        orderService.save(order);
    }

    @Override
    public void afterOrderConfirm(String orderSn) {
        Order order = orderService.getBySn(orderSn);
        //判断是否为拼团订单，进行特殊处理
        //判断订单类型进行不同的订单确认操作
        if (PromotionTypeEnum.PINTUAN.name().equals(order.getOrderPromotionType())) {
            String parentOrderSn = CharSequenceUtil.isEmpty(order.getParentOrderSn()) ? orderSn : order.getParentOrderSn();
            pintuanIntegrationHandler.checkPintuanOrder(order.getPromotionId(), parentOrderSn);
        } else {
            orderService.afterOrderConfirm(orderSn);
        }
    }


    /**
     * 自动成团订单处理
     *
     * @param pintuanId     拼团活动id
     * @param parentOrderSn 拼团订单sn
     */
    @Override
    public void agglomeratePintuanOrder(String pintuanId, String parentOrderSn) {
        pintuanIntegrationHandler.agglomeratePintuanOrder(pintuanId, parentOrderSn);
    }

    @Override
    public void updateByStoreInfo(Store store) {
        LambdaUpdateWrapper<Order> lambdaUpdateWrapper = new LambdaUpdateWrapper<Order>()
                .set(Order::getStoreName, store.getStoreName())
                .eq(Order::getStoreId, store.getId());
        orderService.update(lambdaUpdateWrapper);

        LambdaUpdateWrapper<AfterSale> afterSaleLambdaUpdateWrapper = new LambdaUpdateWrapper<AfterSale>()
                .set(AfterSale::getStoreName, store.getStoreName())
                .eq(AfterSale::getStoreId, store.getId());
        afterSaleService.update(afterSaleLambdaUpdateWrapper);

        LambdaUpdateWrapper<OrderFlow> orderFlowLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderFlow>()
                .set(OrderFlow::getStoreName, store.getStoreName())
                .eq(OrderFlow::getStoreId, store.getId());
        orderFlowService.update(orderFlowLambdaUpdateWrapper);

        LambdaUpdateWrapper<OrderLog> orderLogLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderLog>()
                .eq(OrderLog::getOperatorId,store.getExtendId())
                .set(OrderLog::getOperatorName,store.getStoreName());
        orderLogService.update(orderLogLambdaUpdateWrapper);


        LambdaUpdateWrapper<OrderComplaint> orderComplaintLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderComplaint>()
                .eq(OrderComplaint::getStoreId, store.getExtendId())
                .set(OrderComplaint::getStoreName, store.getStoreName());
        orderComplaintService.update(orderComplaintLambdaUpdateWrapper);

    }

    @Override
    public void storeRefundReconcilePayment(AfterSale afterSale) {
        orderIntegrationHandler.storeRefundReconcilePayment(afterSale);
    }

    /**
     * 查询订单列表分页
     *
     * @param orderSearchParams
     * @return
     */
    @Override
    public Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams) {
        return orderService.queryByParams(orderSearchParams);
    }

    /**
     * 订单发货
     *
     * @param orderSn
     * @param logisticsNo
     * @param logisticsId
     * @return
     */
    @Override
    public Order delivery(String orderSn, String logisticsNo, String logisticsId) {
        return orderService.delivery(orderSn, logisticsNo, logisticsId);
    }

    @Override
    public Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO) {
        return orderService.partDelivery(partDeliveryParamsDTO);
    }

    /**
     * 取消订单
     *
     * @param orderSn 订单编号
     * @param reason 取消原因
     * @return
     */
    @Override
    public void cancel(String orderSn, String reason) {
        orderIntegrationHandler.cancel(orderSn, reason);
    }

    @Override
    public void complete(String orderSn) {
        orderService.complete(orderSn);
    }

    /**
     * 下载待发货的订单列表
     *
     * @param logisticsName
     */
    @Override
    public void getBatchDeliverList( List<String> logisticsName) {
        orderService.getBatchDeliverList( logisticsName);
    }

    /**
     * 获取物流踪迹
     *
     * @param orderSn
     * @return
     */
    @Override
    public Traces getTraces(String orderSn) {
        return orderService.getTraces(orderSn);
    }

    /**
     * 供应商订单统计
     *
     * @param supplierId
     * @return
     */
    @Override
    public Map<String, Long> pendingPaymentOrderNum(String supplierId) {
        return orderService.pendingPaymentOrderNum(supplierId);
    }

    @Override
    public void everyDayTask() {
        orderEveryDayTask.execute();
    }


    @Override
    public List<OrderFlow> orderFlowList(String orderSn) {
        return orderFlowService.listByOrderSn(orderSn);
    }


    @Override
    public void updateByMemberInfo(User user) {

        LambdaUpdateWrapper<Order> lambdaUpdateWrapper = new LambdaUpdateWrapper<Order>()
                .set(Order::getNickname, user.getNickName())
                .eq(Order::getBuyerId, user.getId());
        orderService.update(lambdaUpdateWrapper);

        LambdaUpdateWrapper<AfterSale> afterSaleLambdaUpdateWrapper = new LambdaUpdateWrapper<AfterSale>()
                .set(AfterSale::getMemberName, user.getNickName())
                .eq(AfterSale::getMemberId, user.getId());
        afterSaleService.update(afterSaleLambdaUpdateWrapper);

        LambdaUpdateWrapper<OrderFlow> orderFlowLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderFlow>()
                .set(OrderFlow::getNickname, user.getNickName())
                .eq(OrderFlow::getMemberId, user.getId());
        orderFlowService.update(orderFlowLambdaUpdateWrapper);

        LambdaUpdateWrapper<OrderLog> orderLogLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderLog>()
                .eq(OrderLog::getOperatorId,user.getId())
                .set(OrderLog::getOperatorName,user.getNickName());
        orderLogService.update(orderLogLambdaUpdateWrapper);

        LambdaUpdateWrapper<OrderComplaint> orderComplaintLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderComplaint>()
                .eq(OrderComplaint::getMemberId, user.getId())
                .set(OrderComplaint::getMemberName, user.getNickName());

        orderComplaintService.update(orderComplaintLambdaUpdateWrapper);
    }

    @Override
    public void paidToSupplier() {
        orderIntegrationHandler.paidToSupplier();
    }
    @Override
    public List<Order> listByTradeSn(String tradeSn) {
        return orderService.getByTradeSn(tradeSn);
    }

    @Override
    public Page<StoreRankStatisticsVO> getSaleAmountByStore (StoreRankStatisticsParams params) {
        return orderService.getSaleAmountByStore(params);
    }
}
