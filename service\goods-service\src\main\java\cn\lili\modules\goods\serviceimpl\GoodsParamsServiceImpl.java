package cn.lili.modules.goods.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.goods.entity.dos.*;
import cn.lili.modules.goods.entity.dto.BrandPageDTO;
import cn.lili.modules.goods.entity.dto.GoodsParamsSearchParams;
import cn.lili.modules.goods.entity.vos.BrandVO;
import cn.lili.modules.goods.entity.vos.GoodsPageVO;
import cn.lili.modules.goods.mapper.GoodsParamsMapper;
import cn.lili.modules.goods.service.*;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.mybatis.util.SceneHelp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 商品品牌业务层实现
 *
 * <AUTHOR>
 * @since 2020-02-18 16:18:56
 */
@Service
@RequiredArgsConstructor
public class GoodsParamsServiceImpl extends ServiceImpl<GoodsParamsMapper, GoodsParams>
        implements GoodsParamsService {


    @Autowired
    private CategoryService categoryService;

    /**
     * 校验绑定关系
     *
     * @param brandIds 品牌ID集合
     */

    /**
     * 校验是否存在
     *
     * @param id
     * @return 实体对象
     */
    private GoodsParams checkExist(String id) {
        GoodsParams bean = getById(id);
        if (bean == null) {
            log.error("ID为" + id + "的商品参数不存在");
            throw new ServiceException(ResultCode.GOODS_PARAMS_NOT_EXIST);
        }
        return bean;
    }


    @Override
    public void deleteParams(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public boolean addParams(GoodsParams goodsParams) {
        if (getOne(new LambdaQueryWrapper<GoodsParams>().eq(GoodsParams::getParamsName, goodsParams.getParamsName())
                .eq(GoodsParams::getCategoryId, goodsParams.getCategoryId())) != null) {
            throw new ServiceException(ResultCode.GOODS_PARAMS_EXIST_ERROR);
        }
        return this.save(goodsParams);
    }

    @Override
    public boolean updateParams(GoodsParams goodsParams) {
        this.checkExist(goodsParams.getId());
        if (getOne(new LambdaQueryWrapper<GoodsParams>().eq(GoodsParams::getParamsName, goodsParams.getParamsName())
                .eq(GoodsParams::getCategoryId,goodsParams.getCategoryId())
                .ne(GoodsParams::getId, goodsParams.getId())) != null) {
            throw new ServiceException(ResultCode.GOODS_PARAMS_EXIST_ERROR);
        }
        return this.updateById(goodsParams);
    }

    @Override
    public List<GoodsParams> getList(GoodsParamsSearchParams searchParams) {
        LambdaQueryWrapper<GoodsParams> queryWrapper = new LambdaQueryWrapper<>();
        if(!StrUtil.isEmpty(searchParams.getParamsName())){
            queryWrapper.like(GoodsParams::getParamsName,"%"+searchParams.getParamsName()+"%");
        }
        if(!StrUtil.isEmpty(searchParams.getParamsType())){
            queryWrapper.eq(GoodsParams::getParamsType,searchParams.getParamsType());
        }
//        List<GoodsParams> plist = this.list(queryWrapper);
//        if(!plist.isEmpty()){
//            plist.forEach(item->{
//                Category categrory = categoryService.getCategoryById(item.getCategoryId());
//                if(null!=categrory){
//                    item.set
//                }
//            });
//        }
        return this.list(queryWrapper);
    }
}