package cn.lili.modules.system.entity.params;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 市场查询参数
 *
 * <AUTHOR>
 * @since 2020/12/1
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreMarketSearchParams extends PageVO {

    @Schema(title = "市场名称")
    private String marketName;

    @Schema(title = "开始时间")
    private String startDate;

    @Schema(title = "结束时间")
    private String endDate;



    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(CharSequenceUtil.isNotBlank(marketName), "market_name", "%"+marketName+"%");
        queryWrapper.ge(CharSequenceUtil.isNotBlank(startDate), "create_time", startDate);
        queryWrapper.le(CharSequenceUtil.isNotBlank(endDate), "create_time", endDate);
        return queryWrapper;
    }
}
