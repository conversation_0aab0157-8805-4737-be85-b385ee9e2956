package cn.lili.modules.order.order.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.member.entity.dto.MemberAddressDTO;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dto.OrderBatchDeliverDTO;
import cn.lili.modules.order.order.entity.dto.OrderBatchDeliverLogDTO;
import cn.lili.modules.order.order.entity.dto.OrderMessage;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.payment.entity.dos.PaymentLog;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/**
 * 子订单业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:36 下午
 */
public interface OrderService extends IService<Order> {


    /**
     * 系统取消订单
     *
     * @param orderSn 订单编号
     * @param reason  错误原因
     */
    Order systemCancel(String orderSn, String reason, Boolean refundMoney);

    /**
     * 根据sn查询
     *
     * @param orderSn 订单编号
     * @return 订单信息
     */
    Order getBySn(String orderSn);

    /**
     * 查询订单，判定是否可以发货
     *
     * @param orderSn 订单编号
     * @return 订单信息
     */
    Order checkDeliver(String orderSn);

    /**
     * 根据sn查询
     *
     * @param orderSn 订单编号
     * @return 订单信息
     */
    Order getBySnNoAuth(String orderSn);

    /**
     * 订单查询
     *
     * @param orderSearchParams 查询参数
     * @return 简短订单分页
     */
    Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams);

    /**
     * 订单信息
     *
     * @param orderSearchParams 查询参数
     * @return 订单信息
     */
    List<Order> queryListByParams(OrderSearchParams orderSearchParams);

    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    long queryCountByPromotion(String orderPromotionType, String payStatus, String parentOrderSn, String orderSn);

    /**
     * 父级拼团订单分组
     *
     * @param pintuanId 拼团id
     * @return 拼团订单信息
     */
    List<Order> queryListByPromotion(String pintuanId);


    /**
     * 查询导出订单列表
     *
     * @param orderSearchParams 查询参数
     * @return 导出订单列表
     */
    void queryExportOrder(OrderSearchParams orderSearchParams) ;


    /**
     * 订单详细
     *
     * @param orderSn 订单SN
     * @return 订单详细
     */
    OrderDetailVO queryDetail(String orderSn);

    /**
     * 创建订单
     * 1.检查交易信息
     * 2.循环交易购物车列表，创建订单以及相关信息
     *
     * @param tradeDTO 交易DTO
     */
    void intoDB(TradeDTO tradeDTO);

    /**
     * 订单付款
     * 修改订单付款信息
     * 记录订单流水
     *
     * @param paymentLog 支付日志
     */
    Order payOrder(PaymentLog paymentLog);

    /**
     * 订单付款 0元支付场景
     *
     * @param tradeSn 交易编号
     */
    List<Order> payOrderZero(String tradeSn);

    /**
     * 订单付款 0元支付场景
     *
     * @param oderSn 订单编号
     */
    Order payOrderZeroByOrderSn(String oderSn);

    /**
     * 订单确认成功
     *
     * @param orderSn
     */
    void afterOrderConfirm(String orderSn);

    /**
     * 取消订单
     *
     * @param orderSn 订单SN
     * @param reason  取消理由
     * @return 订单
     */
    Order cancel(String orderSn, String reason);


    /**
     * 发货信息修改
     * 日志功能内部实现
     *
     * @param orderSn          订单编号
     * @param memberAddressDTO 收货地址信息
     * @return 订单
     */
    Order updateConsignee(String orderSn, MemberAddressDTO memberAddressDTO);

    /**
     * 买家订单备注
     *
     * @param orderSn 订单编号
     * @param remark  备注
     * @return 订单
     */
    Order updateRemark(String orderSn, String remark);

    /**
     * 卖家订单备注
     *
     * @param orderSn 订单编号
     * @param sellerRemark  卖家订单备注
     * @return 订单
     */
    Order updateSellerRemark(String orderSn, String sellerRemark);

    /**
     * 订单发货
     *
     * @param orderSn       订单编号
     * @param invoiceNumber 发货单号
     * @param logisticsId   物流公司
     * @return 订单
     */
    Order delivery(String orderSn, String invoiceNumber, String logisticsId);

    /**
     * 获取物流踪迹
     *
     * @param orderSn 订单编号
     * @return 物流踪迹
     */
    Traces getTraces(String orderSn);

    /**
     * 订单核验
     *
     * @param verificationCode 验证码
     * @param orderSn          订单编号
     * @param storeId          店铺ID
     */
    void take(String verificationCode,String orderSn,String storeId);

    /**
     * 订单完成
     *
     * @param orderSn 订单编号
     */
    void complete(String orderSn);

    /**
     * 通过trade 获取订单列表
     *
     * @param tradeSn 交易编号
     * @return 订单列表
     */
    List<Order> getByTradeSn(String tradeSn);

    /**
     * 发送更新订单状态的信息
     *
     * @param orderMessage 订单传输信息
     */
    void sendUpdateStatusMessage(OrderMessage orderMessage);

    /**
     * 根据订单sn逻辑删除订单
     *
     * @param sn 订单sn
     */
    void deleteOrder(String sn);

    /**
     * 开具发票
     *
     * @param sn 订单sn
     * @return
     */
    Boolean invoice(String sn);

    /**
     * 获取待发货订单编号列表
     *
     * @param logisticsName 店铺已选择物流公司列表
     */
    void getBatchDeliverList( List<String> logisticsName);

    /**
     * 订单批量发货
     *
     * @param files 文件
     */
    void batchDeliver(MultipartFile files);


    /**
     * 查询订单支付记录
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 订单支付记录分页
     */
    Page<PaymentLog> queryPaymentLogs(Page<PaymentLog> page, Wrapper<PaymentLog> queryWrapper);

    /**
     * 供应商订单统计
     *
     * @param supplierId
     * @return
     */
    Map<String, Long> pendingPaymentOrderNum(String supplierId);

    /**
     * 订单部分发货
     *
     * @param partDeliveryParamsDTO 参数
     * @return 订单
     */
    Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO);

    /**
     * 订单批量发货
     *
     * @param files 文件
     */
    OrderBatchDeliverLogDTO checkBatchDeliver(MultipartFile files);

    /**
     * 选择列表进行批量发货
     * @param id
     * @param orderBatchDeliverDTOList
     */
    void batchDeliverByList(String id, List<OrderBatchDeliverDTO> orderBatchDeliverDTOList);

    /**
     * 获取待发货订单编号列表
     */
    void getBatchDeliverList(String id, String status);

    /**
     * 发送订单状态消息
     * @param order
     */
    void orderStatusMessage(Order order);

    /**
     * 店铺补差成功
     * @param sn 订单编号
     */
    void storePaidToSupplier(String sn);

    /**
     * 标记全额退款
     * @param orderSn 订单编号
     */
    void fullRefund(String orderSn);
    /**
     * 撤销全额退款
     * @param orderSn 订单编号
     */
    void revokeFullRefund(String orderSn);

    /**
     * 标记售后状态
     *
     * @param orderSn             订单编号
     * @param isAfterSaleApplying 是否售后申请中
     */
    void setAfterSaleApplying(String orderSn, Boolean isAfterSaleApplying);

    /**
     * 获取店铺销售金额统计
     *
     * @param params 参数
     * @return 订单统计
     */
    Page<StoreRankStatisticsVO> getSaleAmountByStore (StoreRankStatisticsParams params);

    /**
     * 根据店铺获取支付订单数量
     *
     * @param storeId 店铺ID
     * @return 订单统计
     */
    Long getPaymentOrderCountByStore(String storeId);
}