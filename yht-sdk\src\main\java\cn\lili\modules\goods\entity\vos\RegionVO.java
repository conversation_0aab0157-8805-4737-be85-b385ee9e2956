package cn.lili.modules.goods.entity.vos;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.goods.entity.dos.Brand;
import cn.lili.modules.goods.entity.dos.Category;
import cn.lili.modules.system.entity.dos.Region;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 分类VO
 *
 * <AUTHOR>
 * @since 2020/12/1
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegionVO extends Region {

    @Serial
    private static final long serialVersionUID = 3775766246075838410L;

    @Schema(title = "父节点名称")
    private String parentTitle;

    @Schema(title = "子分类列表")
    private List<RegionVO> children;

    public RegionVO(Region region) {
        BeanUtil.copyProperties(region, this);
    }


    public List<RegionVO> getChildren() {

        if (children != null) {
            children.sort(Comparator.comparing(Region::getOrderNum));
            return children;
        }
        return Collections.emptyList();
    }
}
