package cn.lili.modules.order.cart.entity.dto;

import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.order.cart.entity.enums.CartSceneEnum;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.enums.SuperpositionPromotionEnum;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.cart.entity.vo.PriceDetailVO;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.entity.vo.OrderVO;
import cn.lili.modules.order.order.entity.vo.ReceiptVO;
import cn.lili.modules.promotion.entity.dos.MemberCoupon;
import cn.lili.modules.promotion.entity.vos.MemberCouponVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 购物车视图
 *
 * <AUTHOR>
 * @since 2020-03-25 2:30 下午
 */
@Data
@NoArgsConstructor
public class TradeDTO implements Serializable {

    private static final long serialVersionUID = -3137165707807057810L;

    @Schema(title = "sn")
    private String sn;

    @Schema(title = "是否为其他订单下的订单，如果是则为依赖订单的sn，否则为空")
    private String parentOrderSn;

    @Schema(title = "购物车列表")
    private List<CartVO> cartList;

    @Schema(title = "整笔交易中所有的规格商品")
    private List<CartSkuVO> skuList;

    @Schema(title = "购物车车计算后的总价")
    private PriceDetailVO priceDetailVO;

    @Schema(title = "购物车车计算后的总价")
    private PriceDetailDTO priceDetailDTO;

    @Schema(title = "发票信息")
    private ReceiptVO receiptVO;

    @Schema(title = "是否需要发票")
    private Boolean needReceipt;


    @Schema(title = "不支持配送方式")
    private List<CartSkuVO> notSupportFreight;

    /**
     * 购物车类型
     */
    private CartSceneEnum cartSceneEnum;

    /**
     * 配送方式
     */
    private DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.LOGISTICS;
    /**
     * 店铺备注
     */
    private List<StoreRemarkDTO> storeRemark;

    /**
     * sku促销连线 包含满优惠
     * <p>
     * KEY值为 sku_id+"_"+SuperpositionPromotionEnum
     * VALUE值为 对应的活动ID
     *
     * @see SuperpositionPromotionEnum
     */
//    private Map<String, String> skuPromotionDetail;

    /**
     * 使用平台优惠券，一笔订单只能使用一个平台优惠券
     */
    private MemberCouponDTO platformCoupon;

    /**
     * key 为商家id
     * value 为商家优惠券
     * 店铺优惠券
     */
    private List<TradeCouponDTO> storeCoupons;

    /**
     * 可用优惠券列表
     */
    private List<MemberCoupon> canUseCoupons;

    /**
     * 无法使用优惠券无法使用的原因
     */
    private List<MemberCouponVO> cantUseCoupons;

    /**
     * 收货地址
     */
    private UserAddress userAddress;

    /**
     * 店铺自提地址
     */
    private UserAddress  storeAddress;
    /**
     * 客户端类型
     */
    private String clientType;

    /**
     * 买家名称
     */
    private String memberName;

    /**
     * 买家id
     */
    private String memberId;

    /**
     * 分销商id
     */
    private String distributionId;

    /**
     * 优惠信息
     */
    private String discountValue;

    /**
     * 分销商名称
     */
    private String distributionName;
    /**
     * 订单vo
     */
    private List<OrderVO> orderVO;

    /**
     * 是否支持服务费
     */
    private Boolean needServiceFee;

    /**
     * 场景
     */
    private String scene;

    /**
     * @see PromotionTypeEnum
     */
    @Schema(title = "参与促销枚举 如不设置，且存在拼团、积分、砍价以外的活动，则会自动参与一项（顺序以map中促销的优先级为准）")
    private PromotionTypeEnum promotionType;

    private List<ServiceFeeDTO> serviceFeeDTOList;

    public TradeDTO(CartSceneEnum cartSceneEnum) {
        this.cartSceneEnum = cartSceneEnum;

        this.skuList = new ArrayList<>();
        this.cartList = new ArrayList<>();
        this.storeCoupons = new ArrayList<>();
        this.priceDetailDTO = new PriceDetailDTO();
        this.cantUseCoupons = new ArrayList<>();
        this.canUseCoupons = new ArrayList<>();
        this.needReceipt = false;
    }

    public TradeDTO(CartSceneEnum cartSceneEnum, AuthUser currentUser) {
        if(currentUser.getScene().equals(SceneEnums.MEMBER)){
            this.memberId = currentUser.getId();
        }else if(currentUser.getScene().equals(SceneEnums.STORE)){
            this.memberId = currentUser.getExtendId();
        }
        this.memberName = currentUser.getNickName();

        this.cartSceneEnum = cartSceneEnum;
        this.skuList = new ArrayList<>();
        this.cartList = new ArrayList<>();
        this.storeCoupons = new ArrayList<>();
        this.priceDetailDTO = new PriceDetailDTO();
        this.cantUseCoupons = new ArrayList<>();
        this.canUseCoupons = new ArrayList<>();
        this.needReceipt = false;
    }

    /**
     * 过滤购物车中已选择的sku
     *
     * @return
     */
    public List<CartSkuVO> getCheckedSkuList() {
        if (skuList != null && !skuList.isEmpty()) {
            return skuList.stream().filter(CartSkuVO::getChecked).toList();
        }
        return skuList;
    }

    public void removeCoupon() {
        this.canUseCoupons = new ArrayList<>();
        this.cantUseCoupons = new ArrayList<>();
    }

    public void removeUsingCoupon() {
        this.platformCoupon = null;
        this.storeCoupons = new ArrayList<>();
    }

}
