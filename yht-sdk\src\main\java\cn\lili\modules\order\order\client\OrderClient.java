package cn.lili.modules.order.order.client;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.order.aftersale.entity.dos.AfterSale;
import cn.lili.modules.order.fallback.OrderFallback;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderFlow;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.payment.entity.dto.PaymentCallback;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 订单服务client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.ORDER_SERVICE, contextId = "order", fallback = OrderFallback.class)
public interface OrderClient {

    /**
     * 订单编号查询
     *
     * @param sn 订单编号
     * @return 订单模型
     */
    @GetMapping("/feign/order/query/{sn}")
    Order getBySn(@PathVariable String sn);

    /**
     * 订单支付回调
     *
     * @param paymentCallback 支付回调参数
     */
    @PostMapping("/feign/order/paymentCallback")
    void paymentCallback(@RequestBody PaymentCallback paymentCallback);
    /**
     * 系统取消订单
     *
     * @param orderSn 订单编号
     * @param reason  错误原因
     */
    @GetMapping("/feign/order/system/cancel")
    void systemCancel(@RequestParam String orderSn, @RequestParam String reason,@RequestParam Boolean refundMoney);

    /**
     * 获取实际支付金额
     *
     * @param orderSn 订单编号
     * @return 支付金额
     */
    @GetMapping("/feign/order/query/{orderSn}/payment-total")
    Double getPaymentTotal(@PathVariable String orderSn);

    /**
     * 获取订单详情
     *
     * @param sn 订单编号
     * @return 订单详情
     */
    @GetMapping("/feign/order/query/{sn}/detail-vo")
    OrderDetailVO queryDetail(@PathVariable String sn);

    /**
     * 根据交易sn，获取订单列表
     *
     * @param sn 交易sn
     * @return 订单列表
     */
    @GetMapping("/feign/order/query/{sn}/by-trade-sn")
    List<Order> getByTradeSn(@PathVariable String sn);

    /**
     * 订单信息
     *
     * @param orderSearchParams 查询参数
     * @return 订单信息
     */
    @PostMapping("/feign/order/query/list")
    List<Order> queryListByParams(@RequestBody OrderSearchParams orderSearchParams);


    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    @GetMapping("/feign/order/query/promotions/count")
    long queryCountByPromotion(@RequestParam String orderPromotionType, @RequestParam String payStatus, @RequestParam String parentOrderSn,
                               @RequestParam String orderSn);

    /**
     * 检查是否开始虚拟成团
     *
     * @param pintuanId   拼团活动id
     * @param requiredNum 成团人数
     * @param fictitious  是否开启成团
     * @return 是否成功
     */
    @GetMapping("/feign/order/fictitious/pintuan/{pintuanId}")
    void checkFictitiousOrder(@PathVariable String pintuanId, @RequestParam Integer requiredNum, @RequestParam Boolean fictitious);

    /**
     * 保存订单
     *
     * @param order 订单信息
     */
    @PostMapping("/feign/order/fictitious/save")
    void save(@RequestBody Order order);

    /**
     * 订单确认后处理
     *
     * @param orderSn 订单编号
     */
    @GetMapping("/feign/order/{orderSn}/fictitious/afterOrderConfirm")
    void afterOrderConfirm(@PathVariable String orderSn);

    /**
     * 自动成团订单处理
     *
     * @param pintuanId     拼团活动id
     * @param parentOrderSn 拼团订单sn
     */
    @GetMapping("/feign/order/agglomerate/pintuan/{pintuanId}")
    void agglomeratePintuanOrder(@PathVariable String pintuanId, @RequestParam String parentOrderSn);

    /**
     * 根据店铺信息更新订单信息
     *
     * @param store 店铺信息
     */
    @PutMapping("/feign/order/store/updateByStoreInfo")
    void updateByStoreInfo(@RequestBody Store store);

    /**
     * 查询订单列表分页
     *
     * @param orderSearchParams 查询参数
     * @return 订单列表
     */
    @PostMapping("/feign/order/fictitious/queryByParams")
    Page<OrderSimpleVO> queryByParams(@RequestBody OrderSearchParams orderSearchParams);

    /**
     * 订单发货
     *
     * @param orderSn 订单编号
     * @param logisticsNo 物流单号
     * @param logisticsId 物流公司id
     * @return 订单信息
     */
    @PostMapping("/feign/order/fictitious/delivery")
    Order delivery(@RequestParam String orderSn, @RequestParam String logisticsNo, @RequestParam String logisticsId);

    /**
     * 订单部分发货
     *
     * @param partDeliveryParamsDTO 部分发货参数
     * @return 订单信息
     */
    @PostMapping("/feign/order/fictitious/partDelivery")
    Order partDelivery(@RequestBody PartDeliveryParamsDTO partDeliveryParamsDTO);

    /**
     * 取消订单
     *
     * @param orderSn 订单编号
     * @param reason 取消原因
     */
    @PostMapping("/feign/order/cancel")
    void cancel(@RequestParam String orderSn, @RequestParam String reason);


    /**
     * 订单完成
     *
     * @param orderSn 订单编号
     */
    @PostMapping("/feign/order/complete")
    void complete(@RequestParam String orderSn);
    /**
     * 下载待发货的订单列表
     *
     * @param logisticsName 物流公司名称
     */
    @PostMapping("/feign/order/getBatchDeliverList")
    void getBatchDeliverList(@RequestParam List<String> logisticsName);

    /**
     * 获取物流踪迹
     *
     * @param orderSn 订单编号
     * @return 物流踪迹
     */
    @PostMapping("/feign/order/getTraces")
    Traces getTraces(@RequestParam String orderSn);

    /**
     * 供应商订单统计
     *
     * @param supplierId 供应商id
     * @return 订单统计
     */
    @PostMapping("/feign/order/pendingPaymentOrderNum")
    Map<String, Long> pendingPaymentOrderNum(@RequestParam String supplierId);

    /**
     * 订单每日任务
     */
    @PostMapping("/feign/order/everyDayTask")
    void everyDayTask();

    /**
     * 获取订单流水列表
     * @param orderSn 订单编号
     * @return 订单流水列表
     */
    @PostMapping("/feign/order/orderFlowList")
    List<OrderFlow> orderFlowList(@RequestParam String orderSn);

    /**
     * 根据用户信息更新订单信息
     * @param user 用户信息
     */
    @PutMapping("/feign/order/store/updateByUserInfo")
    void updateByMemberInfo(@RequestBody User user);

    /**
     * 供应商订单补差
     */
    @GetMapping("/feign/order/paidToSupplier")
    void paidToSupplier();

    /**
     * 供应商订单补差退款
     */
    @PostMapping("/feign/order/refundToSupplier")
    void storeRefundReconcilePayment(@RequestBody AfterSale afterSale);


    /**
     * 根据交易号码获取订单列表
     * @param tradeSn 交易号码
     * @return 订单列表
     */
    @GetMapping("/feign/order/listByTradeSn")
    List<Order> listByTradeSn(@RequestParam String tradeSn);

    /**
     * 获取店铺销售金额
     *
     * @param params 查询参数
     * @return 商品图片下载量排名
     */
    @PostMapping("/feign/order/getSaleAmountByStore")
    Page<StoreRankStatisticsVO> getSaleAmountByStore (@RequestBody StoreRankStatisticsParams params);

    /**
     * 获取店铺付款订单数
     *
     * @param storeId 店铺ID
     * @return 订单数
     */
    @GetMapping("/feign/order/getPaymentOrderCountByStore/{storeId}")
    Long getPaymentOrderCountByStore(@PathVariable String storeId);

    /**
     * 获取店铺24小时内快速发货订单数量
     *
     * @param storeId 店铺ID
     * @return 24小时内快速发货订单数量
     */
    @GetMapping("/feign/order/getQuickDeliveryOrderCount/{storeId}")
    Long getQuickDeliveryOrderCount(@PathVariable String storeId);
}
