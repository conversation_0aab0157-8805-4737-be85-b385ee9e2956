package cn.lili.controller.system;

import cn.lili.common.aop.annotation.DemoSite;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dto.CategorySearchParams;
import cn.lili.modules.goods.entity.vos.CategoryVO;
import cn.lili.modules.system.entity.dos.Region;
import cn.lili.modules.system.entity.params.RegionSearchParams;
import cn.lili.modules.system.entity.vo.RegionVO;
import cn.lili.modules.system.service.RegionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 地址信息接口
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "地址信息接口")
@RequestMapping("/system/region")
@RequiredArgsConstructor
public class RegionController {

    private final RegionService regionService;

    @Operation(summary = "点地图获取地址信息")
    @GetMapping
    public ResultMessage<Object> getRegion(@RequestParam String cityCode, @RequestParam String townName) {
        return ResultUtil.data(regionService.getRegion(cityCode, townName));
    }

    @GetMapping(value = "/name")
    @Operation(summary = "根据名字获取地区地址id")
    public ResultMessage<String> getItemByLastName(String lastName) {
        return ResultUtil.data(regionService.getItemByLastName(lastName));
    }

    @GetMapping(value = "/item/{id}")
    
    @Operation(summary = "通过id获取子地区")
    public ResultMessage<List<Region>> getItem(@PathVariable String id) {
        return ResultUtil.data(regionService.getItem(id));
    }

    @GetMapping(value = "/allCity")
    @Operation(summary = "获取所有的省-市")
    public ResultMessage<List<RegionVO>> getAllCity() {
        return ResultUtil.data(regionService.getAllCity());
    }

    @DemoSite
    @PostMapping(value = "/sync")
    @Operation(summary = "同步高德行政地区数据")
    public void synchronizationData(String url) {
        regionService.synchronizationData(url);
    }

    @GetMapping(value = "/{id}")
    
    @Operation(summary = "通过id获取地区详情")
    public ResultMessage<Region> get(@PathVariable String id) {
        return ResultUtil.data(regionService.getById(id));
    }

    @PutMapping(value = "/{id}")
    
    @Operation(summary = "更新地区")
    public ResultMessage<Region> update(@PathVariable String id, Region region) {
        region.setId(id);
        region.validateParams();
        regionService.updateById(region);
        return ResultUtil.data(region);
    }


    @PostMapping
    @Operation(summary = "增加地区")
    public ResultMessage<Region> save(Region region) {
        region.validateParams();
        regionService.save(region);
        return ResultUtil.data(region);
    }

    @DeleteMapping(value = "{ids}")
    
    @Operation(summary = "批量通过id删除")
    public ResultMessage<Object> delAllByIds(@PathVariable List<String> ids) {
        regionService.removeByIds(ids);
        return ResultUtil.success();
    }


    @Operation(summary = "查询全部区域列表")
    @GetMapping(value = "/allChildren")
    public ResultMessage<List<RegionVO>> list(RegionSearchParams searchParams) {
        return ResultUtil.data(this.regionService.listAllChildren(searchParams));
    }

}
