package cn.lili.modules.goods.mapper;

import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.vos.GoodsStatusCountVO;
import cn.lili.modules.goods.entity.vos.GoodsVO;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 规格项数据处理层
 *
 * <AUTHOR>
 * @since 2020-02-18 15:18:56
 */
public interface GoodsMapper extends BaseMapper<Goods> {

    /**
     * 根据店铺ID获取商品ID列表
     *
     * @param storeId 店铺ID
     * @return 商品ID列表
     */
    @Select("SELECT id FROM li_goods WHERE store_id = #{storeId}")
    List<String> getGoodsIdByStoreId(String storeId);

    /**
     * 更新商品销售数量
     */
    @Update("UPDATE li_goods SET buy_count = #{buyCount} WHERE id = #{goodsId}")
    void updateBuyCount(@Param("goodsId") String goodsId, @Param("buyCount") Integer buyCount);
    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @Update("UPDATE li_goods SET comment_num = comment_num + #{commentNum} WHERE id = #{goodsId}")
    void addGoodsCommentNum(Integer commentNum, String goodsId);

    /**
     * 查询商品VO分页
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 商品VO分页
     */
    @Select("select g.* from li_goods as g ")
    Page<GoodsVO> queryByParams(Page<GoodsVO> page, @Param(Constants.WRAPPER) Wrapper<GoodsVO> queryWrapper);

    /**
     * 根据商品id获取全部skuId的集合
     *
     * @param queryWrapper 查询参数
     * @return 全部skuId的集合
     */
    @Select("SELECT id FROM li_goods ${ew.customSqlSegment}")
    List<String> getGoodsSkuIdByGoodsId(@Param(Constants.WRAPPER) Wrapper<Goods> queryWrapper);

    /**
     * 根据商品id获取全部skuId的集合
     *
     * @param queryWrapper 查询参数
     * @return 全部skuId的集合
     */
    @Select("SELECT store_id FROM li_goods ${ew.customSqlSegment}")
    List<String> getGoodsSkuStoreIdByGoodsId(@Param(Constants.WRAPPER) Wrapper<Goods> queryWrapper);

    /**
     * 获取商品各个状态的数量
     *
     */
    @Select("SELECT " +
            "count(case when (g.market_enable = 'UPPER' and g.auth_flag = 'PASS') then 1 end) as status_upper," +
            "count(case when (g.market_enable = 'DOWN') then 1 end) as status_down ," +
            "count(case when (g.auth_flag = 'TOBEAUDITED') then 1 end) as status_unaudited ," +
            "count(case when (g.auth_flag = 'REFUSE') then 1 end) as status_refuse" +
            " from li_goods g ${ew.customSqlSegment}")
    GoodsStatusCountVO getGoodsStatusCount(@Param(Constants.WRAPPER) Wrapper<Goods> queryWrapper);


    @Select("select IFNULL(sum(image_download_count),0) c from li_goods where store_id = ${storeId}")
    long getImageDownloadCount(@Param("storeId") String storeId);

    /**
     * 统计店铺的商品图片下载量
     * @param page
     * @param queryWrapper
     * @return
     */
    @Select("SELECT " +
            "lg.store_id as storeId, " +
            "SUM(lg.image_download_count) as imageDownloadNum " +
            "FROM li_goods lg " +
            "${ew.customSqlSegment}")
    Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore(Page<Goods> page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    /**
     * 获取店铺商品实拍率
     *
     * @param storeId 店铺ID
     * @return 商品实拍率（百分比）
     */
    @Select("SELECT " +
            "CASE " +
            "    WHEN COUNT(*) = 0 THEN 0.0 " +
            "    ELSE ROUND((COUNT(CASE WHEN is_real_shoot = 1 THEN 1 END) * 100.0 / COUNT(*)), 2) " +
            "END " +
            "FROM li_goods " +
            "WHERE store_id = #{storeId} " +
            "AND delete_flag = 0 " +
            "AND auth_flag = 'PASS' " +
            "AND market_enable = 'UPPER'")
    Double getRealShootRate(@Param("storeId") String storeId);

    /**
     * 获取店铺商品质量合格率
     * 计算方式：审核通过数/(审核通过数+审核拒绝数)
     *
     * @param storeId 店铺ID
     * @return 商品质量合格率（百分比）
     */
    @Select("SELECT " +
            "CASE " +
            "    WHEN (COUNT(CASE WHEN auth_flag = 'PASS' THEN 1 END) + " +
            "          COUNT(CASE WHEN auth_flag = 'REFUSE' THEN 1 END)) = 0 THEN 0.0 " +
            "    ELSE ROUND((COUNT(CASE WHEN auth_flag = 'PASS' THEN 1 END) * 100.0 / " +
            "               (COUNT(CASE WHEN auth_flag = 'PASS' THEN 1 END) + " +
            "                COUNT(CASE WHEN auth_flag = 'REFUSE' THEN 1 END))), 2) " +
            "END " +
            "FROM li_goods " +
            "WHERE store_id = #{storeId} " +
            "AND delete_flag = 0")
    Double getQualityPassRate(@Param("storeId") String storeId);

}