package cn.lili.modules.store.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreVerify;
import cn.lili.modules.store.entity.dto.StoreSearchPageParams;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.params.StoreParams;
import cn.lili.modules.store.entity.vos.StoreVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * StoreServiceFallback
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-17 14:46
 */
public class StoreFallback implements StoreClient {


    @Override
    public StoreVO getStoreDetailVO(String id) {
        throw new ServiceException();
    }


    @Override
    public StoreVO getStore(String storeId) {
        throw new ServiceException();
    }

    @Override
    public Store getStoreDO(String storeId) {
        throw new ServiceException();
    }

    /**
     * 列表查询
     *
     * @param searchParams 查询参数
     * @return 店铺列表
     */
    @Override
    public List<Store> list(StoreSearchParams searchParams) {
        throw new ServiceException();
    }


    @Override
    public void updateScore(StoreParams storeParams) {
        throw new ServiceException();
    }

    @Override
    public void updateStoreGoodsNum(String storeId, Long num) {
        throw new ServiceException();
    }

    @Override
    public Store customAdd(Store store) {
        throw new ServiceException();
    }

    @Override
    public Store customEdit(Store store) {
        throw new ServiceException();
    }

    @Override
    public Page<StoreVO> page(StoreSearchPageParams storeSearchPageParams) {
        throw new ServiceException();
    }

    @Override
    public Store getStoreDetailBySceneAndUserId(String extendId, SceneEnums scene) {
        throw new ServiceException();
    }

    @Override
    public Store getStoreByManagerId(String managerId, SceneEnums scene) {
        throw new ServiceException();
    }

    @Override
    public StoreVerify getStoreVerify() {
        throw new ServiceException();
    }

    @Override
    public Store registerByUser(User user) {
        throw new ServiceException();
    }

    @Override
    public String getBusinessCategory(String storeId) {
        throw new ServiceException();
    }

    @Override
    public void updateStoreStatistics(String storeId, Double num, String field) {
        throw new ServiceException();
    }
}
