package cn.lili.modules.system.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.system.entity.dos.Region;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.entity.params.StoreMarketSearchParams;
import cn.lili.modules.system.entity.vo.StoreMarketVO;
import cn.lili.modules.system.mapper.StoreMarketMapper;
import cn.lili.modules.system.service.RegionService;
import cn.lili.modules.system.service.StoreMarketService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务费业务层实现
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreMarketServiceImpl extends ServiceImpl<StoreMarketMapper, StoreMarket>
        implements StoreMarketService {


    @Autowired
    public RegionService regionService;
    @Override
    public Page<StoreMarketVO> getOpenList(StoreMarketSearchParams storeMarketSearchParams) {
        QueryWrapper<StoreMarket> queryWrapper = storeMarketSearchParams.queryWrapper();
        queryWrapper.eq("delete_flag", false);
        Page<StoreMarket> list = this.page(PageUtil.initPage(storeMarketSearchParams), queryWrapper);
        return fillData(list);
    }

    @Override
    public List<StoreMarket> listAll(String city) {
        LambdaQueryWrapper<StoreMarket> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreMarket::getDeleteFlag, false);
        if(StrUtil.isNotBlank(city)){
            queryWrapper.like(StoreMarket::getRegionNamePath, "%"+city+"%");
        }
        return this.list(queryWrapper);
    }

    private Page<StoreMarketVO> fillData(Page<StoreMarket> dataList) {
        List<StoreMarketVO> vos = new ArrayList<>();
        Page<StoreMarketVO> page = new Page<>();
        dataList.getRecords().forEach(item->{
            StoreMarketVO storeMarketVO = new StoreMarketVO(item);
//            Region region = regionService.getById(item.getRegionId());
//            if(null!=region){
//                storeMarketVO.setStreet(region.getName());
//            }
            vos.add(storeMarketVO);
        });
        page.setRecords(vos);
        page.setTotal(dataList.getTotal());
        page.setPages(dataList.getPages());
        page.setCurrent(dataList.getCurrent());
        page.setSize(dataList.getSize());
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdate(StoreMarket market) {
        LambdaQueryWrapper<StoreMarket> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreMarket::getMarketName, market.getMarketName());
        queryWrapper.ne(CharSequenceUtil.isNotEmpty(market.getId()), StoreMarket::getId, market.getId());
        if (this.getOne(queryWrapper) != null) {
            throw new ServiceException(ResultCode.STORE_MARKET_EXIST_ERROR);
        }
//        Region region = regionService.getById(market.getRegionId());
//        if(null!=region){
//            String[] path = region.getPath().split(",");
//            List<String> parentIds = Arrays.stream(path).filter(item->CharSequenceUtil.isNotEmpty(item) && !item.equals("0") && !item.equals(market.getRegionId())).collect(Collectors.toList());
//            if(parentIds.size()==3){
//                Region provice = regionService.getById(parentIds.get(0));
//                Region city = regionService.getById(parentIds.get(1));
//                Region district = regionService.getById(parentIds.get(2));
//                if(null!=provice){
//                    market.setProvince(provice.getName());
//                }
//                if(null!=city){
//                    market.setCity(city.getName());
//                }
//                if(null!=district){
//                    market.setDistrict(district.getName());
//                }
//            }
//        }

        return this.saveOrUpdate(market);
    }
}
