package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.context.ThreadContextHolder;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.common.utils.SnowFlake;
import cn.lili.common.vo.PageVO;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.lililogs.logs.annotation.SystemLogPoint;
import cn.lili.modules.file.client.UploadClient;
import cn.lili.modules.file.entity.dto.FileUploadDTO;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.member.entity.dto.MemberAddressDTO;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.order.aop.OrderLogPoint;
import cn.lili.modules.order.order.entity.dos.*;
import cn.lili.modules.order.order.entity.dto.*;
import cn.lili.modules.order.order.entity.enums.*;
import cn.lili.modules.order.order.entity.vo.AllowOperation;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.order.order.entity.vo.OrderVO;
import cn.lili.modules.order.order.mapper.OrderMapper;
import cn.lili.modules.order.order.service.*;
import cn.lili.modules.order.trade.entity.dos.OrderLog;
import cn.lili.modules.payment.entity.dos.PaymentLog;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.promotion.entity.enums.GiftEnum;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.system.client.ExportLogClient;
import cn.lili.modules.system.client.LogisticsClient;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dos.ExportLog;
import cn.lili.modules.system.entity.dos.Logistics;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.ExportDTO;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.enums.ExportStatusEnum;
import cn.lili.modules.system.entity.enums.ExportTypeEnum;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.entity.vo.Traces;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.mybatis.util.SceneHelp;
import cn.lili.routing.ExportRoutingKey;
import cn.lili.routing.GoodsRoutingKey;
import cn.lili.routing.OrderRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 子订单业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private static final String ORDER_SN_COLUMN = "order_sn";
    /**
     * 订单货物数据层
     */
    private final OrderItemService orderItemService;
    /**
     * 发票
     */
    private final ReceiptService receiptService;
    /**
     * 物流公司
     */
    private final LogisticsClient logisticsClient;
    /**
     * 订单日志
     */
    private final OrderLogService orderLogService;

    private final AmqpSender amqpSender;

    private final Cache cache;

    private final UploadClient uploadClient;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final OrderPackageService orderPackageService;

    private final OrderPackageItemService orderPackageItemService;

    private final DeliverLogService deliverLogService;

    private final ExportLogClient exportLogClient;

    private final SettingClient settingClient;

    private final VerificationCodeService verificationCodeService;

    private final GoodsClient goodsClient;

    @Override
    @Transactional
    public void intoDB(TradeDTO tradeDTO) {
        //存放购物车，即业务中的订单
        List<Order> orders = new ArrayList<>(tradeDTO.getCartList().size());
        //存放自订单/订单日志
        List<OrderItem> orderItems = new ArrayList<>();
        List<OrderLog> orderLogs = new ArrayList<>();

        //订单集合
        List<OrderVO> orderVOS = new ArrayList<>();
        //循环购物车
        for (CartVO item : tradeDTO.getCartList()) {
            if (item.getSkuList().stream().noneMatch(CartSkuVO::getChecked)) {
                continue;
            }
            Order order = new Order(item, tradeDTO);
            //构建orderVO对象
            OrderVO orderVO = new OrderVO();
            BeanUtil.copyProperties(order, orderVO);

            // 赠品订单标识
            if (item.getSkuList() != null && CharSequenceUtil.isNotEmpty(item.getSkuList().getFirst().getLockedTag())
                    && item.getSkuList().getFirst().getLockedTag().equals(GiftEnum.SKU.getDescription())) {
                order.setOrderPromotionType(PromotionTypeEnum.GIFT.name());
                order.setPromotionId(item.getSkuList().getFirst().getPromotionId());
            }
            //持久化DO
            orders.add(order);
            String message = "订单[" + item.getSn() + "]创建";
            //记录日志
            orderLogs.add(new OrderLog(item.getSn(), UserContext.getCurrentExistUser().getExtendId(),
                    UserContext.getCurrentExistUser().getScene().value(),
                    UserContext.getCurrentExistUser().getNickName(), message));
            item.getCheckedSkuList().forEach(sku -> {
                Double cost = goodsClient.getSkuCostPrice(sku.getGoodsSku().getId());
                log.info("订单商品成本：{},id:{}", cost, sku.getGoodsSku().getId());
                orderItems.add(new OrderItem(sku, item, tradeDTO, cost));
            });
            //写入子订单信息
            orderVO.setOrderItems(orderItems);
            //orderVO 记录
            orderVOS.add(orderVO);
        }
        tradeDTO.setOrderVO(orderVOS);
        //批量保存订单
        super.saveBatch(orders);
        //批量保存 子订单
        orderItemService.saveBatch(orderItems);
        //批量记录订单操作日志
        orderLogService.saveBatch(orderLogs);
    }

    @Override
    @Transactional
    public Order payOrder(PaymentLog paymentLog) {
        //获取订单
        Order order = this.getBySn(paymentLog.getOrderSn());


        //修改订单状态
        order.setPaymentTime(new Date());
        order.setPaymentMethod(paymentLog.getPaymentMethod());
        order.setPayStatus(PayStatusEnum.PAID.name());
        order.setOrderStatus(OrderStatusEnum.PAID.name());
        order.setOutTradeNo(paymentLog.getOutTradeNo());
        order.setTransactionId(paymentLog.getTransactionId());
        order.setCanReturn(PaymentMethodEnum.canReturnOnline(paymentLog.getPaymentMethod()));
        this.updateById(order);

        //发送订单已付款消息
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setPaymentMethod(order.getPaymentMethod());
        orderMessage.setNewStatus(OrderStatusEnum.PAID);
        this.sendUpdateStatusMessage(orderMessage);

        String message = "订单付款，付款方式[" + PaymentMethodEnum.valueOf(order.getPaymentMethod())
                .paymentName() + "]";

//        payGiftOrder(order, paymentLog);

        String orderLogName = order.getNickname();
        OrderLog orderLog = new OrderLog(order.getSn(), order.getBuyerId(), SceneEnums.MEMBER.name(), orderLogName, message);
        orderLogService.save(orderLog);
        return order;
    }

    @Override
    @Transactional
    public List<Order> payOrderZero(String sn) {

        //订单列表
        List<Order> orders = this.getByTradeSn(sn);

        for (Order order : orders) {
            //修改订单状态
            order.setPaymentTime(new Date());
            order.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
            order.setPayStatus(PayStatusEnum.PAID.name());
            order.setOrderStatus(OrderStatusEnum.PAID.name());
            order.setOutTradeNo("-1");
            order.setTransactionId("-1");
            order.setCanReturn(false);
            this.updateById(order);

            //发送订单已付款消息
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderSn(order.getSn());
            orderMessage.setPaymentMethod(order.getPaymentMethod());
            orderMessage.setNewStatus(OrderStatusEnum.PAID);
            this.sendUpdateStatusMessage(orderMessage);

            String message = "订单金额为0元，自动修改订单状态";

            OrderLog orderLog = new OrderLog(order.getSn(), "-1", SceneEnums.SYSTEM.name(), "系统操作", message);
            orderLogService.save(orderLog);
        }
        return orders;
    }

    @Override
    public Order payOrderZeroByOrderSn(String oderSn) {
        Order order = this.getBySn(oderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //修改订单状态
        order.setPaymentTime(new Date());
        order.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
        order.setPayStatus(PayStatusEnum.PAID.name());
        order.setOrderStatus(OrderStatusEnum.PAID.name());
        order.setOutTradeNo("-1");
        order.setTransactionId("-1");
        order.setCanReturn(false);
        this.updateById(order);

        //发送订单已付款消息
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setPaymentMethod(order.getPaymentMethod());
        orderMessage.setNewStatus(OrderStatusEnum.PAID);
        this.sendUpdateStatusMessage(orderMessage);

        String message = "订单金额为0元，自动修改订单状态";

        OrderLog orderLog = new OrderLog(order.getSn(), "-1", SceneEnums.SYSTEM.name(), "系统操作", message);
        orderLogService.save(orderLog);
        return order;
    }

    @Override
    public Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams) {
        QueryWrapper queryWrapper = orderSearchParams.queryWrapper();
        queryWrapper.groupBy("o.id");
        queryWrapper.orderByDesc("o.id");
        Page<OrderSimpleVO> page = this.baseMapper.queryByParams(PageUtil.initPage(orderSearchParams), queryWrapper);
        return page;
    }

    /**
     * 订单信息
     *
     * @param orderSearchParams 查询参数
     * @return 订单信息
     */
    @Override
    public List<Order> queryListByParams(OrderSearchParams orderSearchParams) {
        return this.baseMapper.queryListByParams(orderSearchParams.queryWrapper());
    }

    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    @Override
    public long queryCountByPromotion(String orderPromotionType, String payStatus, String parentOrderSn, String orderSn) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        //查找团长订单和已和当前拼团订单拼团的订单
        queryWrapper.eq(Order::getOrderPromotionType, orderPromotionType).eq(Order::getPayStatus, payStatus).and(i -> i.eq(Order::getParentOrderSn,
                parentOrderSn).or(j -> j.eq(Order::getSn, orderSn)));
        return this.count(queryWrapper);
    }

    /**
     * 获取未成团拼团订单
     *
     * @param pintuanId 拼团id
     * @return 拼团订单信息
     */
    @Override
    public List<Order> queryListByPromotion(String pintuanId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderPromotionType, PromotionTypeEnum.PINTUAN.name());
        queryWrapper.eq(Order::getPromotionId, pintuanId);
        queryWrapper.in(Order::getOrderStatus, OrderStatusEnum.UNPAID.name(), OrderStatusEnum.PAID.name());
        return this.list(queryWrapper);
    }

    @Override
    public void queryExportOrder(OrderSearchParams orderSearchParams) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }

        QueryWrapper<OrderItem> wrapper = orderSearchParams.queryWrapper();
        wrapper.orderByDesc("o.create_time");
        List<OrderItemExportDTO> list = this.baseMapper.queryExportOrderItem(wrapper);
        if (list.isEmpty()) {
            throw new ServiceException(ResultCode.EXPORT_DATA_ERROR);
        }
        List<Object> exportList = list.stream()
                .map(Object.class::cast) // 进行类型转换
                .toList();

        // 保存导出记录
        ExportLog exportLog = new ExportLog();
        String fileName = "订单列表-" + System.currentTimeMillis();
        exportLog.setFileName(fileName);
        if (currentUser.getScene().name().equals(SceneEnums.STORE.name()) || currentUser.getScene().name().equals(SceneEnums.SUPPLIER.name())) {
            exportLog.setStoreId(currentUser.getExtendId());
            exportLog.setStoreName(currentUser.getExtendName());
        } else {
            exportLog.setStoreId("-1");
            exportLog.setStoreName("平台");
        }
        exportLog.setOperator(currentUser.getNickName());
        exportLog.setExportNum(exportList.size());
        exportLog.setType(ExportTypeEnum.ORDER.name());
        exportLog.setStatus(ExportStatusEnum.EXPORTING.name());
        ExportLog saveExport = exportLogClient.saveExport(exportLog);

        /**
         * 发送MQ消息，异步处理导出数据
         */
        ExportDTO exportDTO = new ExportDTO();
        exportDTO.setExportList(exportList);
        exportDTO.setExportId(saveExport.getId());
        exportDTO.setFileName(fileName);
        exportDTO.setExportTypeEnum(ExportTypeEnum.ORDER);
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getExport()).routingKey(ExportRoutingKey.EXPORT_DATA).message(JSONUtil.toJsonStr(exportDTO)).build());

    }

    @Override
    public OrderDetailVO queryDetail(String orderSn) {

        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);


        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            switch (currentUser.getScene()) {
                case MEMBER -> queryWrapper.eq("buyer_id", currentUser.getExtendId());
                case STORE -> queryWrapper.eq("store_id", currentUser.getExtendId()).or().eq("buyer_id",
                        currentUser.getExtendId());
                case SUPPLIER -> queryWrapper.eq("store_id", currentUser.getExtendId()).or().eq("supplier_id",
                        currentUser.getExtendId());
                default -> {
                }
            }
        }


        Order order = this.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        QueryWrapper<OrderItem> orderItemWrapper = new QueryWrapper<>();
        orderItemWrapper.eq(ORDER_SN_COLUMN, orderSn);
        //查询订单项信息
        List<OrderItem> orderItems = orderItemService.list(orderItemWrapper);
        //查询订单日志信息
        List<OrderLog> orderLogs = orderLogService.getOrderLog(orderSn);
        //查询发票信息
        Receipt receipt = receiptService.getByOrderSn(orderSn);
        //查询订单和自订单，然后写入vo返回
        OrderDetailVO orderDetailVO = new OrderDetailVO(order, orderItems, orderLogs, receipt);

        if (orderDetailVO.getOrder()
                .getOrderStatus()
                .equals(OrderStatusEnum.UNPAID.name())) {
            Setting setting = settingClient.get(SettingEnum.ORDER_SETTING.name());
            OrderSetting orderSetting = JSONUtil.toBean(setting.getSettingValue(), OrderSetting.class);
            //订单自动取消时间 = 当前时间 - 自动取消时间分钟数
            DateTime cancelTime = DateUtil.offsetMinute(order.getCreateTime(), orderSetting.getAutoCancel());
            orderDetailVO.setCancelOrderTime(cancelTime);
        }
        //核销码列表
        orderDetailVO.setVerificationCodeList(verificationCodeService.getVerificationCodeList(orderSn));
        orderDetailVO.setCanVerificationCount(verificationCodeService.getVerificationCodeUseNum(orderSn));

        return orderDetailVO;
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']取消，原因为：'+#reason", orderSn = "#orderSn")
    @Transactional
    public Order cancel(String orderSn, String reason) {
        Order order = this.getBySn(orderSn);
        SceneHelp.objectAuthentication(order);
        cancelOrder(order, reason);
        return order;
    }


    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']系统取消，原因为：'+#reason", orderSn = "#orderSn")
    @Transactional
    public Order systemCancel(String orderSn, String reason, Boolean refundMoney) {
        Order order = this.getBySn(orderSn);
        order.setOrderStatus(OrderStatusEnum.CANCELLED.name());
        order.setCancelReason(reason);
        this.cancelOrder(order, reason);
        return order;
    }

    /**
     * 获取订单
     *
     * @param orderSn 订单编号
     * @return 订单详情
     */
    @Override
    public Order getBySn(String orderSn) {

        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);
        if (UserContext.getCurrentUser() != null) {
            switch (Objects.requireNonNull(UserContext.getCurrentUser()).getScene()) {
                case MEMBER -> queryWrapper.eq("buyer_id", UserContext.getCurrentUser().getId());
                case SUPPLIER ->
                        queryWrapper.nested(n -> n.eq("supplier_id", UserContext.getCurrentUser().getExtendId()).or().eq("store_id",
                                UserContext.getCurrentUser().getExtendId()));
                case STORE ->
                        queryWrapper.nested(n -> n.eq("store_id", UserContext.getCurrentUser().getExtendId()).or().eq("buyer_id",
                                UserContext.getCurrentUser().getExtendId()));
                default -> {
                }
            }
        }

        Order order = this.getOne(queryWrapper, false);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        return order;
    }

    @Override
    public Order checkDeliver(String orderSn) {
        Order order = this.getBySn(orderSn);
        //判断订单状促销状态不为空，且是否为赠品订单
        if (CharSequenceUtil.isNotEmpty(order.getOrderPromotionType()) && order.getOrderPromotionType().equals(PromotionTypeEnum.GIFT.name())) {

            List<Order> orders = this.getByTradeSn(order.getTradeSn());
            orders.forEach(o -> {
                //判定当前店铺订单
                if (o.getStoreId().equals(order.getStoreId())) {
                    //判断订单状态是否为待发货&&订单促销类型不为空&&订单促销类型不为赠品
                    if (o.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) &&
                            CharSequenceUtil.isNotEmpty(order.getOrderPromotionType()) &&
                            !o.getOrderPromotionType().equals(PromotionTypeEnum.GIFT.name())) {
                        throw new ServiceException(ResultCode.ORDER_GIFT_DELIVER_ERROR);
                    }
                }
            });

        }
        return order;
    }

    @Override
    public Order getBySnNoAuth(String orderSn) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);
        Order order = this.getOne(queryWrapper);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        return order;
    }

    @Override
    @OrderLogPoint(description = "'库存确认'", orderSn = "#orderSn")
    @Transactional
    public void afterOrderConfirm(String orderSn) {
        Order order = this.getBySn(orderSn);
        //判断订单类型/配送方式
        if ((order.getOrderType().equals(OrderTypeEnum.NORMAL.name()) && order.getDeliveryMethod().equals(DeliveryMethodEnum.LOGISTICS.name())) || order.getOrderType()
                .equals(OrderTypeEnum.PURCHASE.name())) {
            normalOrderConfirm(orderSn);
        } else {
            virtualOrderConfirm(orderSn);
        }
    }


    @Override
    @SystemLogPoint(description = "修改订单", customerLog = "'订单[' + #orderSn + ']收货信息修改，修改为'+#memberAddressDTO.fullAddress+'")
    @Transactional
    public Order updateConsignee(String orderSn, MemberAddressDTO memberAddressDTO) {
        Order order = this.getBySn(orderSn);
        // 判断是否可以编辑收货人信息
        if (!AllowOperation.editConsignee(order)) {
            throw new ServiceException(ResultCode.ORDER_OPERATION_ERROR);
        }

        //要记录之前的收货地址，所以需要以代码方式进行调用 不采用注解
        String message = "订单[" + orderSn + "]收货信息修改，由[" + order.getFullAddress() + "]修改为[" + memberAddressDTO.getFullAddress() + "]";
        //记录订单操作日志
        BeanUtil.copyProperties(memberAddressDTO, order);
        this.updateById(order);

        OrderLog orderLog = new OrderLog(orderSn, UserContext.getCurrentUser().getId(), UserContext.getCurrentUser().getScene().value(),
                UserContext.getCurrentUser().getScene().equals(SceneEnums.MEMBER) ? UserContext.getCurrentUser().getNickName() : UserContext.getCurrentUser().getExtendName(), message);
        orderLogService.save(orderLog);

        return order;
    }

    @Override
    public Order updateRemark(String orderSn, String remark) {
        Order order = this.getBySn(orderSn);
        UpdateWrapper<Order> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("sn", orderSn);
        updateWrapper.set("remark", remark);
        this.update(updateWrapper);
        return order;
    }

    @Override
    public Order updateSellerRemark(String orderSn, String sellerRemark) {
        Order order = this.getBySn(orderSn);
        order.setSellerRemark(sellerRemark);
        this.updateById(order);
        return order;
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']发货，发货单号['+#logisticsNo+']'", orderSn = "#orderSn")
    @Transactional
    public Order delivery(String orderSn, String logisticsNo, String logisticsId) {
        Order order = this.checkDeliver(orderSn);

        //如果订单未发货，并且订单状态值等于待发货
        if (order.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) && (order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) || order.getOrderStatus().equals(OrderStatusEnum.PARTS_DELIVERED.name()))) {
            //获取对应物流
            Logistics logistics = logisticsClient.getById(logisticsId);
            if (logistics == null) {
                throw new ServiceException(ResultCode.ORDER_LOGISTICS_ERROR);
            }
            //写入物流信息
            order.setLogisticsCode(logistics.getId());
            order.setLogisticsName(logistics.getName());
            order.setLogisticsNo(logisticsNo);
            order.setLogisticsTime(new Date());
            order.setDeliverStatus(DeliverStatusEnum.DELIVERED.name());
            this.updateById(order);
            //修改订单状态为已发送
            this.updateStatus(orderSn, OrderStatusEnum.DELIVERED);
            //发送订单状态改变消息
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setNewStatus(OrderStatusEnum.DELIVERED);
            orderMessage.setOrderSn(order.getSn());
            this.sendUpdateStatusMessage(orderMessage);
        } else {
            throw new ServiceException(ResultCode.ORDER_DELIVER_ERROR);
        }
        return order;
    }

    @Override
    public Traces getTraces(String orderSn) {
        //获取订单信息
        Order order = this.getBySn(orderSn);
        //获取踪迹信息
        String str = order.getConsigneeMobile();
        return logisticsClient.getLogistic(order.getLogisticsCode(), order.getLogisticsNo(), str.substring(str.length() - 4));
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']核销，核销码['+#verificationCode+']'", orderSn = "#orderSn")
    @Transactional
    public void take(String verificationCode, String orderSn, String storeId) {
        //核销，如果全部核销则修改订单状态为已完成
        if (Boolean.TRUE.equals(verificationCodeService.verification(storeId, orderSn, verificationCode))) {
            //订单完成
            this.complete(orderSn);
        }
    }

    /**
     * 完成订单方法封装
     *
     * @param orderSn 订单编号
     */

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']完成'", orderSn = "#orderSn")
    @Transactional
    public void complete(String orderSn) {
        //是否可以查询到订单
        Order order = this.getBySn(orderSn);

        //修改订单货物可以进行评价
        orderItemService.update(new UpdateWrapper<OrderItem>().eq(ORDER_SN_COLUMN, orderSn).set("comment_status", CommentStatusEnum.UNFINISHED));
        order.setOrderStatus(OrderStatusEnum.COMPLETED.name());
        order.setCompleteTime(new Date());
        this.updateById(order);
        //发送订单状态改变消息
        orderStatusMessage(order);
        goodsHandler(orderSn);
    }

    private void goodsHandler(String orderSn) {
        //发送当前商品购买完成的信息（用于更新商品数据）
        List<OrderItem> orderItems = orderItemService.getByOrderSn(orderSn);
        List<GoodsCompleteMessage> goodsCompleteMessageList = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            GoodsCompleteMessage goodsCompleteMessage = new GoodsCompleteMessage();
            goodsCompleteMessage.setGoodsId(orderItem.getGoodsId());
            goodsCompleteMessage.setSkuId(orderItem.getSkuId());
            goodsCompleteMessage.setBuyNum(orderItem.getNum());
            goodsCompleteMessageList.add(goodsCompleteMessage);
        }
        //发送商品购买消息
        if (!goodsCompleteMessageList.isEmpty()) {
            //发送订单变更mq消息
            amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.ORDER_COMPLETE).message(goodsCompleteMessageList).build());
        }
    }


    @Override
    public List<Order> getByTradeSn(String tradeSn) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        return this.list(queryWrapper.eq(Order::getTradeSn, tradeSn));
    }

    @Override
    public void sendUpdateStatusMessage(OrderMessage orderMessage) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder()
                        .source("订单状态变更")
                        .exchange(amqpExchangeProperties.getOrder())
                        .routingKey(OrderRoutingKey.STATUS_CHANGE)
                        .message(orderMessage)
                        .build());
    }

    @Override
    @Transactional
    public void deleteOrder(String sn) {
        Order order = this.getBySn(sn);
        if (order == null) {
            log.error("订单号为" + sn + "的订单不存在！");
            throw new ServiceException();
        }
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, sn).set(Order::getDeleteFlag, true);
        this.update(updateWrapper);
        LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderItemLambdaUpdateWrapper.eq(OrderItem::getOrderSn, sn).set(OrderItem::getDeleteFlag, true);
        this.orderItemService.update(orderItemLambdaUpdateWrapper);
    }

    @Override
    public Boolean invoice(String sn) {
        //根据订单号查询发票信息
        Receipt receipt = receiptService.getByOrderSn(sn);
        //校验发票信息是否存在
        if (receipt != null) {
            receipt.setReceiptStatus(1);
            return receiptService.updateById(receipt);
        }
        throw new ServiceException(ResultCode.USER_RECEIPT_NOT_EXIST);
    }


    @Override
    public void getBatchDeliverList(List<String> logisticsName) {

        HttpServletResponse response = ThreadContextHolder.getHttpResponse();
        ExcelWriter writer = ExcelUtil.getWriter();
        //Excel 头部
        ArrayList<String> rows = new ArrayList<>();
        rows.add("订单编号");
        rows.add("物流公司");
        rows.add("物流编号");
        writer.writeHeadRow(rows);

        //存放下拉列表  ----店铺已选择物流公司列表
        String[] logiList = logisticsName.toArray(new String[]{});
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 200, 1, 1);
        writer.addSelect(cellRangeAddressList, logiList);

        ServletOutputStream out = null;
        try {
            //设置公共属性，列表名称
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量发货导入模板", StandardCharsets.UTF_8) + ".xls");
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("获取待发货订单编号列表错误", e);
        } finally {
            writer.close();
            IoUtil.close(out);
        }

    }

    @Override
    @Transactional
    public void batchDeliver(MultipartFile files) {

        InputStream inputStream;
        List<OrderBatchDeliverDTO> orderBatchDeliverDTOList = new ArrayList<>();
        try {
            inputStream = files.getInputStream();
            //2.应用HUtool ExcelUtil获取ExcelReader指定输入流和sheet
            ExcelReader excelReader = ExcelUtil.getReader(inputStream);
            //可以加上表头验证
            //3.读取第二行到最后一行数据
            List<List<Object>> read = excelReader.read(1, excelReader.getRowCount());
            for (List<Object> objects : read) {
                OrderBatchDeliverDTO orderBatchDeliverDTO = new OrderBatchDeliverDTO();
                orderBatchDeliverDTO.setOrderSn(objects.get(0).toString());
                orderBatchDeliverDTO.setLogisticsName(objects.get(1).toString());
                orderBatchDeliverDTO.setLogisticsNo(objects.get(2).toString());
                orderBatchDeliverDTOList.add(orderBatchDeliverDTO);
            }
        } catch (Exception e) {
            throw new ServiceException(ResultCode.ORDER_LOGISTICS_NOT_COMPLETE);
        }
        //循环检查是否符合规范
        checkBatchDeliver(orderBatchDeliverDTOList);
        //订单批量发货
        for (OrderBatchDeliverDTO orderBatchDeliverDTO : orderBatchDeliverDTOList) {
            OrderDetailVO orderDetailVO = this.queryDetail(orderBatchDeliverDTO.getOrderSn());
            PartDeliveryParamsDTO partDeliveryParamsDTO = new PartDeliveryParamsDTO(orderDetailVO);
            partDeliveryParamsDTO.setLogisticsNo(orderBatchDeliverDTO.getLogisticsNo());
            partDeliveryParamsDTO.setLogisticsId(orderBatchDeliverDTO.getLogisticsId());
            this.partDelivery(partDeliveryParamsDTO);
        }
    }


    @Override
    public Page<PaymentLog> queryPaymentLogs(Page<PaymentLog> page, Wrapper<PaymentLog> queryWrapper) {
        return baseMapper.queryPaymentLogs(page, queryWrapper);
    }

    //取消订单
    @Transactional
    public void cancelOrder(Order order, String cancelReason) {

        String orderSn = order.getSn();
        // 修改订单状态
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, orderSn);
        updateWrapper.set(Order::getOrderStatus, OrderStatusEnum.CANCELLED.name());
        updateWrapper.set(Order::getCancelReason, cancelReason);
        order.setOrderStatus(OrderStatusEnum.CANCELLED.name());

        // 更新订单项售后状态
        LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderItemLambdaUpdateWrapper.eq(OrderItem::getOrderSn, orderSn);
        orderItemLambdaUpdateWrapper.set(OrderItem::getAfterSaleStatus, OrderItemAfterSaleStatusEnum.EXPIRED.name());
        orderItemLambdaUpdateWrapper.set(OrderItem::getComplainStatus, OrderComplaintStatusEnum.EXPIRED.name());
        orderItemService.update(orderItemLambdaUpdateWrapper);
        //修改订单
        this.update(updateWrapper);

        orderStatusMessage(order);

        cancelGiftOrder(order);
    }

    @Override
    public Map<String, Long> pendingPaymentOrderNum(String supplierId) {
        Map<String, Long> map = new HashMap<>();
        //待付款订单
        Long pendingPaymentOrderNum = this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).eq(Order::getOrderStatus,
                OrderStatusEnum.UNPAID.name()));
        map.put("pendingPaymentOrderNum", pendingPaymentOrderNum);
        //待发货,待收货订单
        Long deliveryAndReceiptOrderNum =
                this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).and(i -> i.eq(Order::getOrderStatus,
                        OrderStatusEnum.UNDELIVERED.name()).or().eq(Order::getOrderStatus, OrderStatusEnum.DELIVERED.name())));
        map.put("deliveryAndReceiptOrderNum", deliveryAndReceiptOrderNum);
        //退款 退货订单
        Long refundAndReturnOrderNum = this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).eq(Order::getOrderStatus,
                OrderStatusEnum.CANCELLED.name()));
        map.put("refundAndReturnOrderNum", refundAndReturnOrderNum);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO) {
        String logisticsId = partDeliveryParamsDTO.getLogisticsId();
        String orderSn = partDeliveryParamsDTO.getOrderSn();
        String invoiceNumber = partDeliveryParamsDTO.getLogisticsNo();
        Order order = this.checkDeliver(orderSn);
        //获取对应物流
        Logistics logistics = logisticsClient.getById(logisticsId);
        if (logistics == null) {
            throw new ServiceException(ResultCode.ORDER_LOGISTICS_ERROR);
        }
        if (order.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) && (order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) || order.getOrderStatus().equals(OrderStatusEnum.PARTS_DELIVERED.name()))) {
            // 补充包裹信息
            OrderPackage orderPackage = new OrderPackage();
            orderPackage.setPackageNo(SnowFlake.createStr("OP"));
            orderPackage.setOrderSn(orderSn);
            orderPackage.setLogisticsNo(invoiceNumber);
            orderPackage.setLogisticsCode(logistics.getId());
            orderPackage.setLogisticsName(logistics.getName());
            orderPackage.setStatus("1");
            orderPackage.setConsigneeMobile(order.getConsigneeMobile());
            orderPackageService.save(orderPackage);

            //完善子订单信息
            List<OrderItem> orderItemList = orderItemService.getByOrderSn(orderSn);

            for (PartDeliveryDTO partDeliveryDTO : partDeliveryParamsDTO.getPartDeliveryDTOList()) {
                for (OrderItem orderItem : orderItemList) {
                    //寻找订单货物进行判断
                    if (partDeliveryDTO.getOrderItemId().equals(orderItem.getId())) {
                        if ((partDeliveryDTO.getDeliveryNum() + orderItem.getDeliverNumber()) > orderItem.getNum()) {
                            throw new ServiceException("发货数量不正确!");
                        }
                        orderItem.setDeliverNumber((partDeliveryDTO.getDeliveryNum() + orderItem.getDeliverNumber()));

                        // 记录分包裹中每个item子单的具体发货信息
                        OrderPackageItem orderPackageItem = new OrderPackageItem();
                        orderPackageItem.setOrderSn(orderSn);
                        orderPackageItem.setPackageNo(orderPackage.getPackageNo());
                        orderPackageItem.setOrderItemSn(orderItem.getSn());
                        orderPackageItem.setDeliverNumber(partDeliveryDTO.getDeliveryNum());
                        orderPackageItem.setLogisticsTime(new Date());
                        orderPackageItem.setGoodsName(orderItem.getGoodsName());
                        orderPackageItem.setThumbnail(orderItem.getImage());
                        orderPackageItemService.save(orderPackageItem);
                    }
                }
            }
            //修改订单货物
            orderItemService.updateBatchById(orderItemList);
            //日志对象拼接
            String id = "";
            String userName = "";
            String role = "";
            if(UserContext.getCurrentUser() != null){
                userName = UserContext.getCurrentUser().getUsername();
                id = UserContext.getCurrentUser().getId();
                role = UserContext.getCurrentUser().getScene().value();
            } else {
                //代发订单，发货人为供应商
                if(order.getIsProxy()){
                    id = order.getSupplierId();
                    userName = order.getSupplierName();
                    role = SceneEnums.SUPPLIER.name();
                }else{
                    id = order.getStoreId();
                    userName = order.getStoreName();
                    role = SceneEnums.STORE.name();
                }
            }
            OrderLog orderLog = new OrderLog(orderSn, id, role, userName, "订单[" + orderSn + "]分包裹发货，发货单号[" + invoiceNumber + "]");
            orderLogService.save(orderLog);
            //判断订单货物是否全部发货完毕
            boolean delivery = true;
            for (OrderItem orderItem : orderItemList) {
                if (orderItem.getDeliverNumber() < orderItem.getNum() - orderItem.getReturnNum()) {
                    delivery = false;
                    break;
                }
            }
            //是否全部发货
            if (delivery) {
                return delivery(orderSn, invoiceNumber, logisticsId);
            } else {
                order.setOrderStatus(OrderStatusEnum.PARTS_DELIVERED.name());
                this.updateById(order);
            }
        }

        return order;
    }

    @Override
    public OrderBatchDeliverLogDTO checkBatchDeliver(MultipartFile files) {
        InputStream inputStream;
        List<OrderBatchDeliverDTO> orderBatchDeliverDTOList = new ArrayList<>();
        String uploadUrl = "";
        files.getContentType();
        try {
            inputStream = files.getInputStream();
            //2.应用HUtool ExcelUtil获取ExcelReader指定输入流和sheet
            ExcelReader excelReader = ExcelUtil.getReader(inputStream);
            //可以加上表头验证
            //3.读取第二行到最后一行数据
            List<List<Object>> read = excelReader.read(1, excelReader.getRowCount());
            for (List<Object> objects : read) {
                OrderBatchDeliverDTO orderBatchDeliverDTO = new OrderBatchDeliverDTO();
                if (!objects.isEmpty() && objects.get(0) != null) {
                    orderBatchDeliverDTO.setOrderSn(objects.get(0).toString());
                }
                if (objects.size() > 1 && objects.get(1) != null) {
                    orderBatchDeliverDTO.setLogisticsCode(objects.get(1).toString());
                }
                if (objects.size() > 2 && objects.get(2) != null) {
                    orderBatchDeliverDTO.setLogisticsNo(objects.get(2).toString());
                }
                orderBatchDeliverDTOList.add(orderBatchDeliverDTO);
            }
            //读取完成后将源文件上传阿里云以供后续下载
            FileUploadDTO fileUploadDTO = new FileUploadDTO();
            fileUploadDTO.setBucketName("lili-delivery");
            fileUploadDTO.setKey(files.getOriginalFilename() + new DateTime().toString("yyyy-MM-dd") + ".xlsx");
            byte[] bytes = files.getBytes();
            fileUploadDTO.setContextBytes(bytes);
            fileUploadDTO.setContentType("application/vnd.ms-excel");
            uploadUrl = uploadClient.uploadFile(fileUploadDTO);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.ORDER_BATCH_DELIVER_ERROR);
        }
        //循环检查是否符合规范
        OrderBatchDeliverLogDTO deliverLogDTO = checkBatchDeliver(orderBatchDeliverDTOList);
        //保存发货记录
        DeliverLog deliverLog = new DeliverLog();
        deliverLog.setNum(orderBatchDeliverDTOList.size());
        deliverLog.setSuccessNum(0);
        deliverLog.setFileName(files.getOriginalFilename());
        deliverLog.setErrorNum(deliverLogDTO.getErrorNum());
        deliverLog.setFileUrl(uploadUrl);
        deliverLog.setCreateBy(UserContext.getCurrentUser().getUsername());
        deliverLog.setExtentId(UserContext.getCurrentUser().getExtendId());
        deliverLog = deliverLogService.saveWithUploadOss(deliverLog);

        deliverLogDTO.setId(deliverLog.getId());
        //缓存存储，方便后续查询操作
        cache.put(CachePrefix.BATCH_DELIVER + deliverLog.getId(), deliverLogDTO);

        return deliverLogDTO;
    }

    @Override
    public void batchDeliverByList(String id, List<OrderBatchDeliverDTO> orderBatchDeliverDTOList) {
        OrderBatchDeliveryMessage deliveryMessage = new OrderBatchDeliveryMessage(id, orderBatchDeliverDTOList);
        //发送mq消息
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getOrder()).routingKey(OrderRoutingKey.BATCH_DELIVER).message(
                JSONUtil.toJsonStr(deliveryMessage)).build());

    }

    @Override
    public void getBatchDeliverList(String id, String status) {

        HttpServletResponse response = ThreadContextHolder.getHttpResponse();
        ExcelWriter writer = ExcelUtil.getWriter();

        writer.addHeaderAlias("orderSn", "订单编号");
        writer.addHeaderAlias("logisticsCode", "物流公司（请填写物流公司代码）");
        writer.addHeaderAlias("logisticsNo", "物流编号");
        writer.addHeaderAlias("processMessage", "处理信息");

        //获取导出字段
        OrderBatchDeliverLogDTO deliverLogDTO = (OrderBatchDeliverLogDTO) cache.get(CachePrefix.BATCH_DELIVER + id);
        List<OrderBatchDeliverDTO> exportList = null;
        if ("unDeliver".equals(status)) {
            exportList = deliverLogDTO.getBatchDeliverDTOList();
        } else if ("deliverError".equals(status)) {
            exportList = deliverLogDTO.getBatchDeliverErrorDTOList();
        } else if ("deliverSuccess".equals(status)) {
            exportList = deliverLogDTO.getBatchDeliverSuccessDTOList();
        }
        writer.setOnlyAlias(true);
        writer.write(exportList, true);
        ServletOutputStream out = null;
        try {
            //设置公共属性，列表名称
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量发货导入列表", StandardCharsets.UTF_8) + ".xls");
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("获取待发货订单编号列表错误", e);
        } finally {
            writer.close();
            IoUtil.close(out);
        }

    }


    /**
     * 订单状态变更消息
     *
     * @param order 订单信息
     */
    @Override
    @Transactional
    public void orderStatusMessage(Order order) {
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setNewStatus(OrderStatusEnum.valueOf(order.getOrderStatus()));
        this.sendUpdateStatusMessage(orderMessage);
    }

    @Override
    public void storePaidToSupplier(String sn) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, sn);
        updateWrapper.set(Order::getIsPaidToSupplier, true);
        this.update(updateWrapper);
    }

    @Override
    public void fullRefund(String orderSn) {
        this.getBySn(orderSn);
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, orderSn);
        updateWrapper.set(Order::getFullRefund, true);
        updateWrapper.set(Order::getAfterSaleApplying, true);
        this.update(updateWrapper);
    }

    @Override
    public void revokeFullRefund(String orderSn) {
        this.getBySn(orderSn);
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, orderSn);
        updateWrapper.set(Order::getFullRefund, false);
        updateWrapper.set(Order::getAfterSaleApplying, false);
        this.update(updateWrapper);

    }

    @Override
    public void setAfterSaleApplying(String orderSn, Boolean isAfterSaleApplying) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, orderSn);
        updateWrapper.set(Order::getAfterSaleApplying, isAfterSaleApplying);
        this.update(updateWrapper);
    }

    @Override
    public Page<StoreRankStatisticsVO> getSaleAmountByStore(StoreRankStatisticsParams params) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        // 如果指定了店铺ID，则只查询该店铺
        if (CollectionUtils.isNotEmpty(params.getStoreIds())) {
            queryWrapper.in("lo.store_id", params.getStoreIds());
        }
        // 分组查询
        queryWrapper.groupBy("lo.store_id");

        queryWrapper.orderByDesc("SUM(lo.flow_price)");

        return this.baseMapper.getSaleAmountByStore(PageUtil.initPage(params), queryWrapper);
    }

    /**
     * 此方法只提供内部调用，调用前应该做好权限处理
     * 修改订单状态
     *
     * @param orderSn     订单编号
     * @param orderStatus 订单状态
     */
    private void updateStatus(String orderSn, OrderStatusEnum orderStatus) {
        this.baseMapper.updateStatus(orderStatus.name(), orderSn);
    }


    /**
     * 循环检查批量发货订单列表
     *
     * @param deliverDTOList 待发货订单列表
     */
    private OrderBatchDeliverLogDTO checkBatchDeliver(List<OrderBatchDeliverDTO> deliverDTOList) {
        OrderBatchDeliverLogDTO orderBatchDeliverLogDTO = new OrderBatchDeliverLogDTO();
        List<OrderBatchDeliverDTO> orderBatchDeliverErrorDTOList = new ArrayList<>();
        List<OrderBatchDeliverDTO> orderBatchDeliverDTOList = new ArrayList<>();
        List<Logistics> logistics = logisticsClient.list();
        for (OrderBatchDeliverDTO orderBatchDeliverDTO : deliverDTOList) {
            //数据非空校验
            boolean errorFlag = false;
            if (orderBatchDeliverDTO.getOrderSn() == null) {
                orderBatchDeliverDTO.setProcessMessage("未填写订单号");
                orderBatchDeliverDTO.setStatus("deliverError");
                orderBatchDeliverErrorDTOList.add(orderBatchDeliverDTO);
                continue;
            }
            //非空校验通过后进行其他校验
            //查看订单号是否存在-是否是当前店铺的订单
            OrderDetailVO orderDetailVO = this.queryDetail(orderBatchDeliverDTO.getOrderSn());
            //            logisticsClient
            if (orderDetailVO == null || orderDetailVO.getOrder() == null) {
                orderBatchDeliverDTO.setProcessMessage("订单编号：'" + orderBatchDeliverDTO.getOrderSn() + " '不存在");
                errorFlag = true;
            } else if (!orderDetailVO.getOrder().getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name())) {
                orderBatchDeliverDTO.setProcessMessage("订单编号：'" + orderBatchDeliverDTO.getOrderSn() + " '的订单状态不能发货");
                errorFlag = true;
            }
            //获取物流公司
            logistics.forEach(item -> {
                if (item.getName().equals(orderBatchDeliverDTO.getLogisticsCode())) {
                    orderBatchDeliverDTO.setLogisticsId(item.getId());
                    orderBatchDeliverDTO.setLogisticsName(item.getName());
                }
            });

            if (CharSequenceUtil.isEmpty(orderBatchDeliverDTO.getLogisticsCode())) {
                orderBatchDeliverDTO.setProcessMessage("未填写物流公司编码");
                errorFlag = true;
            } else if (CharSequenceUtil.isEmpty(orderBatchDeliverDTO.getLogisticsCode())) {
                orderBatchDeliverDTO.setProcessMessage("物流编码" + orderBatchDeliverDTO.getLogisticsCode() + "不存在");
                errorFlag = true;
            }
            if (CharSequenceUtil.isEmpty(orderBatchDeliverDTO.getLogisticsNo())) {
                orderBatchDeliverDTO.setProcessMessage("未填写运单号");
                errorFlag = true;
            }
            for (OrderItem orderItem : orderDetailVO.getOrderItems()) {
                if (orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.PART_AFTER_SALE.name())
                        || orderItem.getAfterSaleStatus().equals(OrderItemAfterSaleStatusEnum.ALREADY_APPLIED.name())) {
                    orderBatchDeliverDTO.setProcessMessage("订单已申请售后，请确认后发货");
                }
            }
            if (errorFlag) {
                orderBatchDeliverDTO.setStatus("deliverError");
                orderBatchDeliverErrorDTOList.add(orderBatchDeliverDTO);
            } else {
                orderBatchDeliverDTO.setStatus("unDeliver");
                orderBatchDeliverDTO.setId(orderDetailVO.getOrder().getId());
                orderBatchDeliverDTOList.add(orderBatchDeliverDTO);
            }
        }

        orderBatchDeliverLogDTO.setBatchDeliverErrorDTOList(orderBatchDeliverErrorDTOList);
        orderBatchDeliverLogDTO.setErrorNum(orderBatchDeliverErrorDTOList.size());
        orderBatchDeliverLogDTO.setBatchDeliverDTOList(orderBatchDeliverDTOList);
        orderBatchDeliverLogDTO.setUnDeliverNum(orderBatchDeliverDTOList.size());
        return orderBatchDeliverLogDTO;
    }


    /**
     * 普通商品订单确认
     * 修改订单状态为待发货
     * 发送订单状态变更消息
     *
     * @param orderSn 订单编号
     */
    @Transactional
    public void normalOrderConfirm(String orderSn) {
        //修改订单
        this.update(new LambdaUpdateWrapper<Order>().eq(Order::getSn, orderSn).set(Order::getOrderStatus, OrderStatusEnum.UNDELIVERED.name()));
        //修改订单
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setNewStatus(OrderStatusEnum.UNDELIVERED);
        orderMessage.setOrderSn(orderSn);
        sendUpdateStatusMessage(orderMessage);
    }

    /**
     * 虚拟商品订单确认
     * 修改订单状态为待核验
     * 发送订单状态变更消息
     *
     * @param orderSn 订单编号
     */
    @Transactional
    public void virtualOrderConfirm(String orderSn) {
        //修改订单
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setNewStatus(OrderStatusEnum.TAKE);
        Order order = this.getBySn(orderSn);
        if (order.getDeliveryMethod() != null && order.getDeliveryMethod().equals(DeliveryMethodEnum.SELF_PICK_UP.name())) {
            this.update(new LambdaUpdateWrapper<Order>().eq(Order::getSn, orderSn).set(Order::getOrderStatus, OrderStatusEnum.STAY_PICKED_UP.name()));
            orderMessage.setNewStatus(OrderStatusEnum.STAY_PICKED_UP);
        } else {
            this.update(new LambdaUpdateWrapper<Order>().eq(Order::getSn, orderSn).set(Order::getOrderStatus, OrderStatusEnum.TAKE.name()));
            orderMessage.setNewStatus(OrderStatusEnum.TAKE);
        }
        // 设置订单验证码
        verificationCodeService.addVerificationCode(this.getBySn(orderSn));

        orderMessage.setOrderSn(orderSn);
        this.sendUpdateStatusMessage(orderMessage);
    }

    /**
     * 检测虚拟订单信息
     *
     * @param order            订单
     * @param verificationCode 验证码
     */
    private void checkVerificationOrder(Order order, String verificationCode) {
        //判断查询是否可以查询到订单
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //判断是否为虚拟订单
        if (!order.getOrderType().equals(OrderTypeEnum.VIRTUAL.name()) && !order.getDeliveryMethod().equals(DeliveryMethodEnum.SELF_PICK_UP.name())) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
        //判断虚拟订单状态
        if (!order.getOrderStatus().equals(OrderStatusEnum.TAKE.name()) && !order.getOrderStatus().equals(OrderStatusEnum.STAY_PICKED_UP.name())) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
    }


    /**
     * 初始化填充订单导出数据
     *
     * @param orderExportDTOList 导出的订单数据
     * @return 订单导出列表
     */
    private XSSFWorkbook initOrderExportData(List<OrderExportDTO> orderExportDTOList) {
        List<OrderExportDetailDTO> orderExportDetailDTOList = new ArrayList<>();
        for (OrderExportDTO orderExportDTO : orderExportDTOList) {
            OrderExportDetailDTO orderExportDetailDTO = new OrderExportDetailDTO();
            BeanUtil.copyProperties(orderExportDTO, orderExportDetailDTO);
            //金额
            PriceDetailDTO priceDetailDTO = GsonUtils.fromJson(orderExportDTO.getPriceDetail(), PriceDetailDTO.class);
            orderExportDetailDTO.setFreightPrice(priceDetailDTO.getFreightPrice());
            orderExportDetailDTO.setDiscountPrice(CurrencyUtil.add(priceDetailDTO.getDiscountPrice(), priceDetailDTO.getCouponPrice()));
            orderExportDetailDTO.setUpdatePrice(priceDetailDTO.getUpdatePrice());
            orderExportDetailDTO.setSiteMarketingCost(priceDetailDTO.getStoreMarketingCost());
            orderExportDetailDTO.setStoreMarketingCost(CurrencyUtil.sub(orderExportDetailDTO.getDiscountPrice(), orderExportDetailDTO.getSiteMarketingCost()));
            //地址
            if (StrUtil.isNotBlank(orderExportDTO.getConsigneeAddressPath())) {
                String[] receiveAddress = orderExportDTO.getConsigneeAddressPath().split(",");
                orderExportDetailDTO.setProvince(receiveAddress[0]);
                orderExportDetailDTO.setCity(receiveAddress[1]);
                orderExportDetailDTO.setDistrict(receiveAddress.length > 2 ? receiveAddress[2] : "");
                orderExportDetailDTO.setStreet(receiveAddress.length > 3 ? receiveAddress[3] : "");
            }

            //状态
            orderExportDetailDTO.setOrderStatus(OrderStatusEnum.valueOf(orderExportDTO.getOrderStatus()).description());
            orderExportDetailDTO.setPaymentMethod(CharSequenceUtil.isNotBlank(orderExportDTO.getPaymentMethod()) ? PaymentMethodEnum.valueOf(orderExportDTO.getPaymentMethod()).paymentName() : "");
            orderExportDetailDTO.setClientType(ClientTypeEnum.valueOf(orderExportDTO.getClientType()).value());
            orderExportDetailDTO.setOrderType(OrderTypeEnum.valueOf(orderExportDTO.getOrderType()).description());
            orderExportDetailDTO.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.valueOf(orderExportDTO.getAfterSaleStatus()).description());

            //时间
            orderExportDetailDTO.setCreateTime(DateUtil.formatDateTime(orderExportDTO.getCreateTime()));
            orderExportDetailDTO.setPaymentTime(DateUtil.formatDateTime(orderExportDTO.getPaymentTime()));
            orderExportDetailDTO.setLogisticsTime(DateUtil.formatDateTime(orderExportDTO.getLogisticsTime()));
            orderExportDetailDTO.setCompleteTime(DateUtil.formatDateTime(orderExportDTO.getCompleteTime()));
            orderExportDetailDTOList.add(orderExportDetailDTO);
        }

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("订单列表");

        // 创建表头
        Row header = sheet.createRow(0);
        String[] headers = {"主订单编号", "子订单编号", "选购商品", "商品数量", "商品ID", "商品单价", "订单应付金额",
                "运费", "优惠总金额", "平台优惠", "商家优惠", "商家改价", "支付方式", "收件人", "收件人手机号",
                "省", "市", "区", "街道", "详细地址", "买家留言", "订单提交时间", "支付完成时间", "来源",
                "订单状态", "订单类型", "售后状态", "取消原因", "发货时间", "完成时间", "店铺"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
        }
        // 填充数据
        for (int i = 0; i < orderExportDetailDTOList.size(); i++) {
            OrderExportDetailDTO dto = orderExportDetailDTOList.get(i);
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(dto.getOrderSn());
            row.createCell(1).setCellValue(dto.getOrderItemSn());
            row.createCell(2).setCellValue(dto.getGoodsName());
            row.createCell(3).setCellValue(dto.getNum());
            row.createCell(4).setCellValue(dto.getGoodsId());
            row.createCell(5).setCellValue(dto.getUnitPrice() == null ? 0 : dto.getUnitPrice());
            row.createCell(6).setCellValue(dto.getFlowPrice());
            row.createCell(7).setCellValue(dto.getFreightPrice());
            row.createCell(8).setCellValue(dto.getDiscountPrice());
            row.createCell(9).setCellValue(dto.getSiteMarketingCost());
            row.createCell(10).setCellValue(dto.getStoreMarketingCost());
            row.createCell(11).setCellValue(dto.getUpdatePrice());
            row.createCell(12).setCellValue(dto.getPaymentMethod());
            row.createCell(13).setCellValue(dto.getConsigneeName());
            row.createCell(14).setCellValue(dto.getConsigneeMobile());
            row.createCell(15).setCellValue(dto.getProvince());
            row.createCell(16).setCellValue(dto.getCity());
            row.createCell(17).setCellValue(dto.getDistrict());
            row.createCell(18).setCellValue(dto.getStreet());
            row.createCell(19).setCellValue(dto.getConsigneeDetail());
            row.createCell(20).setCellValue(dto.getRemark());
            row.createCell(21).setCellValue(dto.getCreateTime());
            row.createCell(22).setCellValue(dto.getPaymentTime());
            row.createCell(23).setCellValue(dto.getClientType());
            row.createCell(24).setCellValue(dto.getOrderStatus());
            row.createCell(25).setCellValue(dto.getOrderType());
            row.createCell(26).setCellValue(dto.getAfterSaleStatus());
            row.createCell(27).setCellValue(dto.getCancelReason());
            row.createCell(28).setCellValue(dto.getLogisticsTime());
            row.createCell(29).setCellValue(dto.getCompleteTime());
            row.createCell(30).setCellValue(dto.getStoreName());
        }
        return workbook;
    }

    private void cancelGiftOrder(Order order) {
        List<Order> orderList = this.list(new LambdaQueryWrapper<Order>().eq(Order::getTradeSn, order.getTradeSn()).eq(Order::getOrderPromotionType, PromotionTypeEnum.GIFT.name()));
        for (Order giftOrder : orderList) {
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            giftOrder.setOrderStatus(OrderStatusEnum.CANCELLED.name());
            giftOrder.setCancelReason("主订单取消");

            // 更新订单项售后状态
            LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            orderItemLambdaUpdateWrapper.eq(OrderItem::getOrderSn, giftOrder.getSn());
            orderItemLambdaUpdateWrapper.set(OrderItem::getAfterSaleStatus, OrderItemAfterSaleStatusEnum.EXPIRED.name());
            orderItemLambdaUpdateWrapper.set(OrderItem::getComplainStatus, OrderComplaintStatusEnum.EXPIRED.name());
            orderItemService.update(orderItemLambdaUpdateWrapper);
            //修改订单
            this.updateById(giftOrder);
            orderStatusMessage(giftOrder);
        }
    }

    private void payGiftOrder(Order order, PaymentLog paymentLog) {
        //查看订单是否存在赠品订单。如果存在赠品订单，同步修改赠品订单
        List<Order> list = this.list(new LambdaQueryWrapper<Order>().eq(Order::getTradeSn, order.getTradeSn()).eq(Order::getOrderPromotionType, PromotionTypeEnum.GIFT.name()));
        if (list != null && !list.isEmpty()) {
            for (Order giftOrder : list) {
                //修改订单状态
                giftOrder.setPaymentTime(new Date());
                giftOrder.setPaymentMethod(paymentLog.getPaymentMethod());
                giftOrder.setPayStatus(PayStatusEnum.PAID.name());
                giftOrder.setOrderStatus(OrderStatusEnum.PAID.name());
                giftOrder.setOutTradeNo(paymentLog.getOutTradeNo());
                giftOrder.setTransactionId(paymentLog.getTransactionId());
                giftOrder.setCanReturn(PaymentMethodEnum.canReturnOnline(paymentLog.getPaymentMethod()));
                this.updateById(giftOrder);

                //发送订单已付款消息
                OrderMessage giftOrderMessage = new OrderMessage();
                giftOrderMessage.setOrderSn(giftOrder.getSn());
                giftOrderMessage.setPaymentMethod(giftOrder.getPaymentMethod());
                giftOrderMessage.setNewStatus(OrderStatusEnum.PAID);
                this.sendUpdateStatusMessage(giftOrderMessage);
            }
        }

    }
}