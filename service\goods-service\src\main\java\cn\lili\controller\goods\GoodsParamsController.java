package cn.lili.controller.goods;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dos.Brand;
import cn.lili.modules.goods.entity.dos.GoodsParams;
import cn.lili.modules.goods.entity.dto.BrandPageDTO;
import cn.lili.modules.goods.entity.dto.GoodsParamsSearchParams;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.vos.BrandVO;
import cn.lili.modules.goods.entity.vos.GoodsPageVO;
import cn.lili.modules.goods.service.BrandService;
import cn.lili.modules.goods.service.GoodsParamsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 商品参数接口
 * <AUTHOR>
 */
@RestController
@Tag(name = "商品参数接口")
@RequestMapping("/goods/params")
@RequiredArgsConstructor
public class GoodsParamsController {

    private final GoodsParamsService goodsParamsService;

    @Operation(summary = "分页获取")
    @GetMapping
    public ResultMessage<List<GoodsParams>> getByPage(GoodsParamsSearchParams searchParams) {
        return ResultUtil.data(goodsParamsService.getList(searchParams));
    }

    @GetMapping(value = "/all")
    @Operation(summary = "获取所有商品参数")
    public ResultMessage<List<GoodsParams>> getAll() {
        return ResultUtil.data(goodsParamsService.list(new QueryWrapper<GoodsParams>().eq("delete_flag", 0)));
    }

    @Operation(summary = "新增商品参数")
    @PostMapping
    public ResultMessage<GoodsParams> save(GoodsParams data) {
        data.validateParams();
        if (goodsParamsService.addParams(data)) {
            return ResultUtil.data(data);
        }
        throw new ServiceException(ResultCode.GOODS_PARAMS_SAVE_ERROR);
    }

    @Operation(summary = "更新数据")
    @PutMapping("/{id}")
    public ResultMessage<GoodsParams> update(@PathVariable String id, GoodsParams data) {
        data.setId(id);
        data.validateParams();
        if (goodsParamsService.updateParams(data)) {
            return ResultUtil.data(data);
        }
        throw new ServiceException(ResultCode.GOODS_PARAMS_UPDATE_ERROR);
    }

    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/delByIds/{ids}")
    public ResultMessage<Object> delAllByIds(@PathVariable List<String> ids) {
        goodsParamsService.deleteParams(ids);
        return ResultUtil.success();
    }

}
