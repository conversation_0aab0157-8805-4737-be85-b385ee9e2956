package cn.lili.modules.search.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.common.utils.StringUtils;
import cn.lili.elasticsearch.ElasticsearchIndexAbstractService;
import cn.lili.modules.goods.client.GoodsSkuClient;
import cn.lili.modules.goods.entity.dos.*;
import cn.lili.modules.goods.entity.dto.GoodsParamsDTO;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.GoodsSkuDTO;
import cn.lili.modules.goods.entity.enums.GoodsTagsTypeEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.goods.entity.vos.GoodsSkuVO;
import cn.lili.modules.goods.service.*;
import cn.lili.modules.member.client.MemberEvaluationClient;
import cn.lili.modules.member.entity.dto.EvaluationQueryParams;
import cn.lili.modules.member.entity.enums.EvaluationGradeEnum;
import cn.lili.modules.order.order.client.OrderFlowStatisticsClient;
import cn.lili.modules.promotion.client.PromotionsClient;
import cn.lili.modules.promotion.entity.dos.BaseStandardPromotions;
import cn.lili.modules.promotion.entity.dos.PromotionGoods;
import cn.lili.modules.promotion.entity.dos.Seckill;
import cn.lili.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import cn.lili.modules.promotion.entity.enums.PromotionsScopeTypeEnum;
import cn.lili.modules.promotion.entity.enums.PromotionsStatusEnum;
import cn.lili.modules.promotion.tools.PromotionTools;
import cn.lili.modules.search.client.CustomWordsClient;
import cn.lili.modules.search.entity.dos.CustomWords;
import cn.lili.modules.search.entity.dos.EsGoodsAttribute;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.search.entity.dos.EsSupplierGoodsIndex;
import cn.lili.modules.search.entity.dto.EsDeleteDTO;
import cn.lili.modules.search.service.EsGoodsIndexService;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.query.ScriptType;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 商品索引业务层实现
 *
 * <AUTHOR>
 * @since 2020/10/14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EsGoodsIndexServiceImpl extends ElasticsearchIndexAbstractService implements EsGoodsIndexService {

    public static final String PROMOTION_PRICE = "promotionPrice";
    private static final String KEY_SUCCESS = "success";
    private static final String KEY_FAIL = "fail";
    private static final String KEY_PROCESSED = "processed";
    private static final String CURRENT_INFO = "currentInfo";
    private final MemberEvaluationClient memberEvaluationClient;
    private final OrderFlowStatisticsClient orderFlowStatisticsClient;
    private final PromotionsClient promotionsClient;
    private final GoodsSkuService goodsSkuService;
    private final BrandService brandService;
    private final CategoryService categoryService;
    private final StoreGoodsLabelService storeGoodsLabelService;
    private final CustomWordsClient customWordsClient;
    private final Cache<Object> cache;
    private final GoodsService goodsService;
    private final GoodsTagsService goodsTagsService;
    private final FreightTemplateClient freightTemplateClient;
    private final StoreClient storeClient;
    private final ConcurrentHashMap<String, Future> runningFutures = new ConcurrentHashMap<>();

    @Value("${product.baseUrl}")
    private String baseUrl;

    @Override
    public void init(Boolean force) {
        //获取索引任务标识
        Boolean flag = (Boolean) cache.get(CachePrefix.INIT_INDEX_FLAG.getPrefix());
        //为空则默认写入没有任务
        if (flag == null) {
            cache.put(CachePrefix.INIT_INDEX_FLAG.getPrefix(), false);
            cache.put(CachePrefix.INIT_SUPPLIER_INDEX_FLAG.getPrefix(), false);
        }
        //有正在初始化的任务，则提示异常，或者执行强制关闭。
        if (Boolean.TRUE.equals(flag)) {
            if (Boolean.TRUE.equals(force)) {
                //强制关闭
                stopRunningFutures();
            } else {
                throw new ServiceException(ResultCode.INDEX_BUILDING);
            }
        }
        //初始化标识
        cache.put(CachePrefix.INIT_INDEX_PROCESS.getPrefix(), null, 10L, TimeUnit.MINUTES);
        cache.put(CachePrefix.INIT_INDEX_FLAG.getPrefix(), true, 10L, TimeUnit.MINUTES);
        cache.put(CachePrefix.INIT_SUPPLIER_INDEX_PROCESS.getPrefix(), null, 10L, TimeUnit.MINUTES);
        cache.put(CachePrefix.INIT_SUPPLIER_INDEX_FLAG.getPrefix(), true, 10L, TimeUnit.MINUTES);

        startNewFutures();

    }


    private void startNewFutures() {
        Future<?> storeFuture = ThreadUtil.execAsync(() -> generatorEsGoodsIndex(SceneEnums.STORE.name()));
        Future<?> supplierFuture = ThreadUtil.execAsync(() -> generatorEsGoodsIndex(SceneEnums.SUPPLIER.name()));
        runningFutures.put(SceneEnums.STORE.name(), storeFuture);
        runningFutures.put(SceneEnums.SUPPLIER.name(), supplierFuture);
    }

    private void stopRunningFutures() {
        runningFutures.forEach((name, future) -> {
            if (future != null && !future.isDone()) {
                future.cancel(true); // 尝试取消任务
            }
        });
        runningFutures.clear();
    }


    @Override
    public Map<String, Object> getProgress(String key) {
        Map<String, Object> map;
        Boolean flag;
        if (SceneEnums.STORE.name().equals(key)) {
            map = (Map<String, Object>) cache.get(CachePrefix.INIT_INDEX_PROCESS.getPrefix());
            flag = (Boolean) cache.get(CachePrefix.INIT_INDEX_FLAG.getPrefix());
        } else if (SceneEnums.SUPPLIER.name().equals(key)) {
            map = (Map<String, Object>) cache.get(CachePrefix.INIT_SUPPLIER_INDEX_PROCESS.getPrefix());
            flag = (Boolean) cache.get(CachePrefix.INIT_SUPPLIER_INDEX_FLAG.getPrefix());
        } else {
            return Collections.emptyMap();
        }


        if (map == null) {
            return Collections.emptyMap();
        }
        map.put("flag", Boolean.TRUE.equals(flag) ? 1L : 0L);
        return map;

    }

    @Override
    public EsGoodsIndex addIndex(EsGoodsIndex goodsIndex) {
        try {

            //分词器分词
//            this.analyzeAndSaveWords(goods);
            // 清除缓存
            cache.remove(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsIndex.getGoodsId() + goodsIndex.getId()));
            cache.remove(CachePrefix.GOODS_SKU.getPrefix(goodsIndex.getId()));

            String storeId = goodsIndex.getStoreId();
            if (StringUtils.isNotBlank(storeId)) {
                StoreVO store = storeClient.getStore(storeId);
                if (store != null) {
                    goodsIndex.setProvince(store.getProvince());
                    goodsIndex.setCity(store.getCity());
                    goodsIndex.setDistrict(store.getDistrict());
                    goodsIndex.setMarket(store.getMarket());
                    goodsIndex.setIsBackMoney(store.getIsBackMoney());
                }
            }
            // 生成索引
            return this.saveIndex(goodsIndex);
        } catch (Exception e) {
            log.error("为商品[{}]生成索引异常", goodsIndex.getGoodsName(), e);
            return null;
        }
    }

    /**
     * 添加商品索引
     *
     * @param goods 商品索引信息
     */
    @Override
    public void addIndex(List<EsGoodsIndex> goods) {
        for (EsGoodsIndex esGoodsIndex : goods) {
            this.addIndex(esGoodsIndex);
        }
    }

    @Override
    public void updateIndex(EsGoodsIndex goods) {
        this.addIndex(goods);
    }


    /**
     * 商品分词
     *
     * @param goods 商品信息
     */
    private void analyzeAndSaveWords(EsGoodsIndex goods) {
        try {
            //分词器分词
            List<CustomWords> customWordsList = new ArrayList<>();
            List<String> keywordsList = new ArrayList<>();
            if (goods.getAttrList() != null && !goods.getAttrList().isEmpty()) {
                //保存分词
                for (EsGoodsAttribute esGoodsAttribute : goods.getAttrList()) {
                    if (keywordsList.stream().noneMatch(i -> i != null && i.toLowerCase(Locale.ROOT).equals(esGoodsAttribute.getValue().toLowerCase(Locale.ROOT)))) {
                        keywordsList.add(esGoodsAttribute.getValue());
                        customWordsList.add(new CustomWords(esGoodsAttribute.getValue(), 1));
                    }
                }
            }
            if (CollUtil.isNotEmpty(customWordsList)) {
                customWordsClient.addCustomWordsList(customWordsList);
            }
        } catch (Exception e) {
            log.debug("{}分词错误", goods, e);
        }
    }

    /**
     * 更新商品索引的的部分属性（只填写更新的字段，不需要更新的字段不要填写）
     *
     * @param queryFields  查询字段
     * @param updateFields 更新字段
     */
    @Override
    public void updateIndex(Map<String, Object> queryFields, Map<String, Object> updateFields, Class clazz) {
        // 如果queryFields存在id，则删除缓存
        if (queryFields != null && queryFields.containsKey("id")) {
            String id = queryFields.get("id").toString();
            if (JSONUtil.isTypeJSONArray(id)) {
                JSONUtil.parseArray(id).forEach(item -> cache.remove(CachePrefix.GOODS_SKU.getPrefix(item.toString())));
            }
        }
        updateIndexFields(queryFields, updateFields, clazz);
    }

    @Override
    public void updateGoodsIndexByGoodsList(List<Goods> goodsList) {
        for (Goods goods : goodsList) {
            try {
                deleteProduct(goods);
            }catch (Exception exception) {
                log.error("deleteProduct error = {}", com.alibaba.fastjson.JSONObject.toJSONString(goods), exception);
            }
            log.debug("updateGoodsIndexByGoodsList goods = {}", com.alibaba.fastjson.JSONObject.toJSONString(goods));
            // 如果商品库存不为0
            GoodsSearchParams searchParams = new GoodsSearchParams();
            searchParams.setGoodsId(goods.getId());
            List<GoodsSku> goodsSkuList = this.goodsSkuService.getGoodsSkuByList(searchParams);
            List<GoodsSku> goodsSkus = new ArrayList<>();
            // 如果goodsSkuList大于5条，则只取前5条数据
            if (goodsSkuList.size() > 5) {
                goodsSkus = goodsSkuList.subList(0, 5);
            }else {
                goodsSkus = goodsSkuList;
            }
            AtomicInteger skuSource = new AtomicInteger(100);
            if (Boolean.FALSE.equals(goods.getDeleteFlag())) {
                GoodsSku goodsSku = null;
                goodsSkuList.forEach(sku -> {
                    if (skuSource.get() < 1) {
                        skuSource.set(1);
                    }
                    skuSource.getAndDecrement();
                });
                if (CollUtil.isNotEmpty(goodsSkuList)) {
                    try {
                        createProduct(goods, goodsSkus, goodsSkuList);
                    }catch (Exception exception) {
                        log.error("createProduct error = {}", com.alibaba.fastjson.JSONObject.toJSONString(goods), exception);
                    }
                    goodsSku = goodsSkuList.stream().sorted(Comparator.comparing(GoodsSku::getPrice)).collect(Collectors.toList()).getFirst();
                    if (SceneEnums.SUPPLIER.name().equals(goods.getScene())) {
                        EsSupplierGoodsIndex esSupplierGoodsIndex = new EsSupplierGoodsIndex(this.settingUpGoodsIndexData(goods, goodsSku), goodsSku);
                        esSupplierGoodsIndex.setSkuSource(skuSource.get());
                        esSupplierGoodsIndex.setReleaseTime(goodsSku.getUpdateTime() != null ? goodsSku.getUpdateTime() : goodsSku.getCreateTime());
                        log.debug("updateGoodsIndexByGoodsList esSupplierGoodsIndex = {}", com.alibaba.fastjson.JSONObject.toJSONString(esSupplierGoodsIndex));
                        this.addIndex(esSupplierGoodsIndex);
                    } else {
                        EsGoodsIndex goodsIndex = this.settingUpGoodsIndexData(goods, goodsSku);
                        goodsIndex.setSkuSource(skuSource.get());
                        goodsIndex.setReleaseTime(goodsSku.getUpdateTime() != null ? goodsSku.getUpdateTime() : goodsSku.getCreateTime());
                        log.debug("updateGoodsIndexByGoodsList goodsIndex = {}", com.alibaba.fastjson.JSONObject.toJSONString(goodsIndex));
                        this.addIndex(goodsIndex);
                    }
                }
            }
            //如果商品状态值不支持es搜索，那么将商品信息做下架处理
            else {
                for (GoodsSku goodsSku : goodsSkuList) {
                    this.deleteIndexById(goodsSku.getId());
                }
            }
        }
    }

    @Override
    public void updateGoodsIndexByStore(String storeId) {
        //如果商品通过审核&&并且已上架
        GoodsSearchParams searchParams = new GoodsSearchParams();
        searchParams.setStoreId(storeId);
        for (Goods goods : this.goodsService.queryListByParams(searchParams)) {
            this.updateGoodsIndex(goods);
        }

    }

    /**
     * 更新商品索引
     *
     * @param goods 商品消息
     */
    private void updateGoodsIndex(Goods goods) {
        //如果商品通过审核&&并且已上架
        GoodsSearchParams searchParams = new GoodsSearchParams();
        searchParams.setGoodsId(goods.getId());
        List<GoodsSku> goodsSkuList = this.goodsSkuService.getGoodsSkuByList(searchParams);
        log.debug("goods：{}", goods);
        log.debug("goodsSkuList：{}", goodsSkuList);
        if (Boolean.FALSE.equals(goods.getDeleteFlag())) {
            this.generatorGoodsIndex(goods, goodsSkuList);
        }
        //如果商品状态值不支持es搜索，那么将商品信息做下架处理
        else {
            for (GoodsSku goodsSku : goodsSkuList) {
                this.deleteIndexById(goodsSku.getId());
            }
        }
    }

    /**
     * 生成商品索引
     *
     * @param goods        商品信息
     * @param goodsSkuList 商品sku信息
     */
    private void generatorGoodsIndex(Goods goods, List<GoodsSku> goodsSkuList) {
        int skuSource = 100;
        List<EsGoodsIndex> esGoodsIndices = new ArrayList<>();
//        for (GoodsSku goodsSku : goodsSkuList) {
//            EsGoodsIndex goodsIndex = this.settingUpGoodsIndexData(goods, goodsSku);
//            if (skuSource < 1) {
//                skuSource = 1;
//            }
//            goodsIndex.setSkuSource(skuSource);
//            log.debug("goodsSku：{}", goodsSku);
//            //如果商品库存不为0，并且es中有数据
//            log.debug("生成商品索引 {}", goodsIndex);
//            if (SceneEnums.SUPPLIER.name().equals(goods.getScene())) {
//                EsSupplierGoodsIndex esSupplierGoodsIndex = new EsSupplierGoodsIndex(goodsIndex, goodsSku);
//                esGoodsIndices.add(esSupplierGoodsIndex);
//            } else {
//                esGoodsIndices.add(goodsIndex);
//            }
//            skuSource--;
//        }
        if (CollectionUtils.isNotEmpty(goodsSkuList)) {
            GoodsSku goodsSku = goodsSkuList.stream().sorted(Comparator.comparing(GoodsSku::getPrice)).collect(Collectors.toList()).getFirst();
            EsGoodsIndex goodsIndex = this.settingUpGoodsIndexData(goods, goodsSku);
            goodsIndex.setSkuSource(skuSource);
            log.debug("goodsSku：{}", goodsSku);
            //如果商品库存不为0，并且es中有数据
            log.debug("生成商品索引 {}", goodsIndex);
            if (SceneEnums.SUPPLIER.name().equals(goods.getScene())) {
                EsSupplierGoodsIndex esSupplierGoodsIndex = new EsSupplierGoodsIndex(goodsIndex, goodsSku);
                esGoodsIndices.add(esSupplierGoodsIndex);
            } else {
                esGoodsIndices.add(goodsIndex);
            }
        }
        this.deleteIndex(
                MapUtil.builder(new HashMap<String, Object>())
                        .put("goodsId", goods.getId()).build(),
                esGoodsIndices.getFirst().getClass());
        this.addIndex(esGoodsIndices);
    }

    private EsGoodsIndex settingUpGoodsIndexData(Goods goods, GoodsSku goodsSku) {
        EsGoodsIndex goodsIndex = new EsGoodsIndex(goodsSku);
        if (goods.getParams() != null && !goods.getParams().isEmpty()) {
            List<GoodsParamsDTO> goodsParamDTOS = JSONUtil.toList(goods.getParams(), GoodsParamsDTO.class);
            goodsIndex.setAttrListByParams(goodsParamDTOS);
            goodsIndex.setParams(goods.getParams());
        }
        List<GoodsSkuVO> goodsListByGoodsId = goodsSkuService.getGoodsListByGoodsId(GoodsSearchParams.builder().goodsId(goodsSku.getGoodsId()).build());
        if (goodsListByGoodsId != null && !goodsListByGoodsId.isEmpty()) {
            goodsIndex.setSkuList(JSONUtil.toJsonStr(goodsListByGoodsId));
        }

        FreightTemplateVO freightTemplate = freightTemplateClient.getFreightTemplate(goodsSku.getFreightTemplateId());
        if (freightTemplate != null) {
            goodsIndex.setFreightTemplate(freightTemplate);
        }
        this.settingUpGoodsIndexOtherParam(goodsIndex);

        this.setGoodsTags(goodsIndex);
        goodsIndex.setImageZipFile(goods.getImageZipFile());
        return goodsIndex;
    }

    /**
     * 设置商品索引的其他参数（非商品自带）
     *
     * @param goodsIndex 商品索引信息
     */
    private void settingUpGoodsIndexOtherParam(EsGoodsIndex goodsIndex) {
        if (CharSequenceUtil.isNotEmpty(goodsIndex.getCategoryPath())) {
            List<Category> categories = categoryService.listByIdsOrderByLevel(Arrays.asList(goodsIndex.getCategoryPath().split(",")));
            if (!categories.isEmpty()) {
                goodsIndex.setCategoryNamePath(ArrayUtil.join(categories.stream().map(Category::getName).toArray(), ","));
            }
        }
        Brand brand = brandService.getById(goodsIndex.getBrandId());
        if (brand != null) {
            goodsIndex.setBrandName(brand.getName());
            goodsIndex.setBrandUrl(brand.getLogo());
        }
        if (goodsIndex.getStoreCategoryPath() != null && CharSequenceUtil.isNotEmpty(goodsIndex.getStoreCategoryPath())) {
            List<StoreGoodsLabel> storeGoodsLabels = storeGoodsLabelService.listByStoreIds(Arrays.asList(goodsIndex.getStoreCategoryPath().split(","
            )));
            if (!storeGoodsLabels.isEmpty()) {
                goodsIndex.setStoreCategoryNamePath(ArrayUtil.join(storeGoodsLabels.stream().map(StoreGoodsLabel::getLabelName).toArray(), ","));
            }
        }

        if (goodsIndex.getOriginPromotionMap() == null || goodsIndex.getOriginPromotionMap().isEmpty()) {
            Map<String, Object> goodsCurrentPromotionMap = promotionsClient.getGoodsSkuPromotionMap(goodsIndex.getStoreId(), goodsIndex.getId());
            if (goodsCurrentPromotionMap.containsKey(PROMOTION_PRICE)) {
                goodsIndex.setPromotionPrice((Double) goodsCurrentPromotionMap.get(PROMOTION_PRICE));
                goodsCurrentPromotionMap.remove(PROMOTION_PRICE);
            }
            goodsIndex.setPromotionMapJson(GsonUtils.toJson(goodsCurrentPromotionMap));
        }
    }

    /**
     * 删除索引
     *
     * @param id 商品索引信息
     */
    @Override
    public void deleteIndexById(String id) {
        deleteIndexById(id, EsGoodsIndex.class);
    }

    /**
     * 删除索引
     *
     * @param ids 商品索引id集合
     */
    @Override
    public void deleteIndexByIds(List<String> ids) {
        deleteIndexByIds(ids, EsGoodsIndex.class);
    }

    @Override
    public void deleteIndex(EsDeleteDTO esDeleteDTO) {
        if (esDeleteDTO.getIds() != null && !esDeleteDTO.getIds().isEmpty()) {
            this.deleteIndexByIds(esDeleteDTO.getIds(), esDeleteDTO.getClazz());
        }
        if (esDeleteDTO.getQueryFields() != null && !esDeleteDTO.getQueryFields().isEmpty()) {
            this.deleteIndex(esDeleteDTO.getQueryFields(), esDeleteDTO.getClazz());
        }
    }

    @Override
    public void initIndex(List<EsGoodsIndex> goodsIndexList, boolean regeneratorIndex, String scene) {
        String flagCacheKey = CachePrefix.INIT_INDEX_FLAG.getPrefix();
        String processKey = CachePrefix.INIT_INDEX_PROCESS.getPrefix();
        if (SceneEnums.SUPPLIER.name().equals(scene)) {
            flagCacheKey = CachePrefix.INIT_SUPPLIER_INDEX_FLAG.getPrefix();
            processKey = CachePrefix.INIT_SUPPLIER_INDEX_PROCESS.getPrefix();
        }

        if (goodsIndexList == null || goodsIndexList.isEmpty()) {
            //初始化标识
            cache.put(processKey, null);
            cache.put(flagCacheKey, false);
            return;
        }
        //预校验
        recheck(scene, regeneratorIndex);

        Map<String, Object> resultMap = (Map<String, Object>) cache.get(processKey);
        if (!goodsIndexList.isEmpty()) {
            for (EsGoodsIndex goodsIndex : goodsIndexList) {
                try {
                    resultMap.put(CURRENT_INFO, goodsIndex.getGoodsName());
                    log.debug("生成商品索引：{}", goodsIndex);
                    this.addIndex(goodsIndex);
                    resultMap.put(KEY_SUCCESS, Long.parseLong(resultMap.get(KEY_SUCCESS).toString()) + 1);
                } catch (Exception e) {
                    log.error("商品{}生成索引错误！", goodsIndex);
                    resultMap.put(KEY_FAIL, Long.parseLong(resultMap.get(KEY_FAIL).toString()) + 1);
                }
                resultMap.put(KEY_PROCESSED, Long.parseLong(resultMap.get(KEY_PROCESSED).toString()) + 1);
                cache.put(processKey, resultMap);
            }
        }
        cache.put(processKey, resultMap);
    }


    /**
     * 预校验，不存在则删除
     */
    private void recheck(String scene, boolean regeneratorIndex) {

        if (!regeneratorIndex) {
            return;
        }

        if (SceneEnums.STORE.name().equals(scene)) {
            IndexOperations indexOps = this.client.indexOps(EsGoodsIndex.class);
            recreateIndex(indexOps);
        } else if (SceneEnums.SUPPLIER.name().equals(scene)) {
            IndexOperations indexOps = this.client.indexOps(EsSupplierGoodsIndex.class);
            recreateIndex(indexOps);
        }

    }

    private void recreateIndex(IndexOperations indexOps) {
        if (!indexOps.exists()) {
            indexOps.createWithMapping();
        } else {
            indexOps.delete();
            indexOps.createWithMapping();
        }
    }

    /**
     * 更新商品索引的促销信息
     *
     * @param ids       skuId集合
     * @param promotion 促销信息
     * @param key       促销信息的key
     */
    @Override
    public void updateEsGoodsIndexPromotions(List<String> ids, BaseStandardPromotions promotion, String key) {
        List<UpdateQuery> updateQueries = new ArrayList<>();
        log.debug("更新商品索引的促销信息----------");
        log.debug("商品ids: {}", ids);
        log.debug("活动: {}", promotion);
        log.debug("key: {}", key);
        Class clazz = null;
        for (String id : ids) {
            EsGoodsIndex goodsIndex = this.findIndexById(id, EsGoodsIndex.class);
            PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
            searchParams.setPromotionId(promotion.getId());
            searchParams.setSkuId(id);
            PromotionGoods promotionsGoods = promotionsClient.getPromotionsGoods(searchParams);
            clazz = addUpdateQuery(promotion, key, updateQueries, clazz, goodsIndex);
            // 如果促销活动为秒杀或者拼团设置促销价格
            if (promotionsGoods != null && (PromotionTypeEnum.SECKILL.name().equals(promotionsGoods.getPromotionType()) || PromotionTypeEnum.MINUS.name().equals(promotionsGoods.getPromotionType()) || PromotionTypeEnum.PINTUAN.name().equals(promotionsGoods.getPromotionType()))) {
                updateQueries.add(UpdateQuery.builder(id).withDocument(Document.from(MapUtil.builder(PROMOTION_PRICE, promotionsGoods.getPrice()).build())).build());
            }

        }
        if (updateQueries.isEmpty()) {
            return;
        }
        this.client.bulkUpdate(updateQueries, clazz);
    }


    @Override
    public void updateEsGoodsIndexByList(List<PromotionGoods> promotionGoodsList, BaseStandardPromotions promotion, String key) {
        List<UpdateQuery> updateQueries = new ArrayList<>();
        log.debug("修改商品活动索引");
        log.debug("促销商品信息: {}", promotionGoodsList);
        log.debug("活动关键字: {}", key);
        log.debug("活动: {}", promotion);
        Class clazz = null;
        if (promotionGoodsList != null) {
            //循环更新 促销商品索引
            for (PromotionGoods promotionGoods : promotionGoodsList) {
                if (promotion.getStartTime() == null || promotion instanceof Seckill) {
                    promotion.setStartTime(promotionGoods.getStartTime());
                }
                if (promotion.getEndTime() == null || promotion instanceof Seckill) {
                    promotion.setEndTime(promotionGoods.getEndTime());
                }
                EsGoodsIndex goodsIndex = findEsGoodsIndexById(promotionGoods.getSkuId());
                clazz = addUpdateQuery(promotion, key, updateQueries, clazz, goodsIndex);

                // 如果促销活动为秒杀或者拼团设置促销价格
                if (PromotionTypeEnum.SECKILL.name().equals(promotionGoods.getPromotionType()) || PromotionTypeEnum.MINUS.name().equals(promotionGoods.getPromotionType()) || PromotionTypeEnum.PINTUAN.name().equals(promotionGoods.getPromotionType())) {
                    updateQueries.add(UpdateQuery.builder(goodsIndex.getId()).withDocument(Document.from(MapUtil.builder(PROMOTION_PRICE, promotionGoods.getPrice()).build())).build());
                }

            }
        }

        if (updateQueries.isEmpty()) {
            return;
        }
        this.client.bulkUpdate(updateQueries, clazz);
    }

    /**
     * 更新商品索引的促销信息
     *
     * @param promotion     促销信息
     * @param key           促销信息的key
     * @param updateQueries 更新请求
     * @param clazz         类
     * @param goodsIndex    商品索引
     * @return 商品索引类
     */
    private Class addUpdateQuery(BaseStandardPromotions promotion, String key, List<UpdateQuery> updateQueries, Class clazz, EsGoodsIndex goodsIndex) {
        if (goodsIndex != null) {
            //更新索引
            updateQueries.add(this.updateGoodsIndexPromotion(goodsIndex, key, promotion));
            clazz = goodsIndex.getClass();
            cache.remove(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsIndex.getGoodsId() + goodsIndex.getId()));
            cache.remove(CachePrefix.GOODS_SKU.getPrefix(goodsIndex.getId()));
        }
        return clazz;
    }


    /**
     * 更新全部商品索引的促销信息
     *
     * @param promotion 促销信息
     * @param key       促销信息的key
     */
    @Override
    public void updateEsGoodsIndexAllByList(BaseStandardPromotions promotion, String key) {
        this.executeUpdateAllEsGoodsIndexPromotions(promotion, key);
    }

    /**
     * Executes update for all goods indexes with promotions.
     *
     * @param promotion the promotion information
     * @param key       the key for the promotion information
     */
    private void executeUpdateAllEsGoodsIndexPromotions(BaseStandardPromotions promotion, String key) {

        GoodsSearchParams searchParams = new GoodsSearchParams();
        if (PromotionTools.isPromotionsTypeNeedsToChecked(key)) {
            searchParams.setSalesModel(SalesModeEnum.RETAIL.name());
        }
        //如果storeId不为空，则表示是店铺活动
        if (promotion.getStoreId() != null && !promotion.getStoreId().equals(PromotionTools.PLATFORM_ID)) {
            searchParams.setStoreId(promotion.getStoreId());
        }

        //查询出店铺商品
        List<String> skuIds = goodsSkuService.getGoodsSkuIdListByGoodsId(searchParams);
        if (skuIds.isEmpty()) {
            return;
        }
        int i = 0;
        int j = Math.min(skuIds.size(), 1000);
        do {
            List<String> skuIdList = skuIds.subList(i, j);
            this.deleteEsGoodsPromotionByPromotionKey(skuIdList, key);
            this.updateEsGoodsIndexPromotions(skuIdList, promotion, key);
            i = j;
            j += 1000;
        } while (j < skuIds.size());
    }

    @Override
    public void deleteEsGoodsPromotionByPromotionKey(List<String> skuIds, String promotionsKey) {
        List<UpdateQuery> updateQueries = new ArrayList<>();
        log.debug("删除商品活动索引");
        log.debug("商品skuIds: {}", skuIds);
        log.debug("活动Key: {}", promotionsKey);
        if (skuIds == null || skuIds.isEmpty()) {
            return;
        }
        Class clazz = null;
        for (String skuId : skuIds) {
            EsGoodsIndex goodsIndex = this.findIndexById(skuId, EsGoodsIndex.class);
            //商品索引不为空
            if (goodsIndex != null) {
                UpdateQuery updateRequest = this.removePromotionByPromotionKey(goodsIndex, promotionsKey);
                if (updateRequest != null) {
                    updateQueries.add(updateRequest);
                    clazz = goodsIndex.getClass();
                    cache.remove(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsIndex.getGoodsId() + goodsIndex.getId()));
                    cache.remove(CachePrefix.GOODS_SKU.getPrefix(goodsIndex.getId()));
                }
            } else {
                log.error("部分商品促销信息失败！skuId 为 【{}】的索引不存在！", skuId);
            }
        }
        if (updateQueries.isEmpty()) {
            return;
        }
        this.client.bulkUpdate(updateQueries, clazz);
    }

    /**
     * 删除索引中指定的促销活动id的促销活动
     *
     * @param promotionsKey 促销活动Key
     */
    @Override
    public void deleteEsGoodsPromotionByPromotionKey(String promotionsKey) {
        for (int i = 0; ; i++) {
            List<UpdateQuery> updateQueries = new ArrayList<>();

            NativeQueryBuilder builder = NativeQuery.builder();
            // 查询包含促销活动key的商品
            builder.withQuery(q -> q.wildcard(w -> w.field("promotionMapJson").wildcard("*" + promotionsKey + "*")));
            builder.withPageable(PageRequest.of(i, 1000));
            try {
                // todo 只考虑了店铺商品，后续根据需求考虑供应商商品
                SearchHits<EsGoodsIndex> esGoodsIndices = this.client.search(builder.build(), EsGoodsIndex.class);
                if (esGoodsIndices.isEmpty() || esGoodsIndices.getSearchHits().isEmpty()) {
                    break;
                }
                for (SearchHit<EsGoodsIndex> searchHit : esGoodsIndices.getSearchHits()) {
                    EsGoodsIndex goodsIndex = searchHit.getContent();
                    UpdateQuery updateRequest = this.removePromotionByPromotionKey(goodsIndex, promotionsKey);
                    if (updateRequest != null) {
                        updateQueries.add(updateRequest);
                        cache.remove(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsIndex.getGoodsId() + goodsIndex.getId()));
                        cache.remove(CachePrefix.GOODS_SKU.getPrefix(goodsIndex.getId()));
                    }
                }
                this.executeBulkUpdateRequest(updateQueries);
            } catch (Exception e) {
                log.error("删除索引中指定的促销活动id的促销活动失败！key: {}", promotionsKey, e);
                return;
            }
        }
    }

    /**
     * 从索引中删除指定促销活动id的促销活动
     *
     * @param goodsIndex    索引
     * @param promotionsKey 促销活动key
     */
    private UpdateQuery removePromotionByPromotionKey(EsGoodsIndex goodsIndex, String promotionsKey) {
        Map<String, Object> promotionMap = goodsIndex.getOriginPromotionMap();
        if (promotionMap != null && !promotionMap.isEmpty()) {
            //如果存在同促销ID的活动删除
            Map<String, Object> filterPromotionMap =
                    promotionMap.entrySet().stream().filter(i -> !i.getKey().equals(promotionsKey)).collect(Collectors.toMap(Map.Entry::getKey,
                            Map.Entry::getValue));
            return this.getGoodsIndexPromotionUpdateRequest(goodsIndex, filterPromotionMap);
        }
        return null;
    }

    /**
     * 清除所有商品索引的无效促销活动
     */
    @Override
    public void cleanInvalidPromotion() {
        for (int i = 0; ; i++) {
            List<UpdateQuery> updateQueries = new ArrayList<>();

            NativeQueryBuilder builder = NativeQuery.builder();
            builder.withQuery(q -> q.matchAll(a -> a));
            builder.withPageable(PageRequest.of(i, 1000));

            List<EsGoodsIndex> esGoodsIndices = this.findByQuery(builder.build(), EsGoodsIndex.class);
            List<EsSupplierGoodsIndex> esSupplierGoodsIndices = this.findByQuery(builder.build(), EsSupplierGoodsIndex.class);
            if (esGoodsIndices.isEmpty()) {
                break;
            }
            for (EsGoodsIndex goodsIndex : esGoodsIndices) {
                this.updateIndexPromotions(goodsIndex, updateQueries);
                this.updateIndexGoodsTags(goodsIndex, updateQueries);
            }

            for (EsSupplierGoodsIndex esSupplierGoodsIndex : esSupplierGoodsIndices) {
                this.updateIndexPromotions(esSupplierGoodsIndex, updateQueries);
                this.updateIndexGoodsTags(esSupplierGoodsIndex, updateQueries);
            }

            this.executeBulkUpdateRequest(updateQueries);
        }

    }

    private void updateIndexPromotions(EsGoodsIndex esGoodsIndex, List<UpdateQuery> updateQueries) {
        Map<String, Object> promotionMap = esGoodsIndex.getOriginPromotionMap();
        if (promotionMap != null && !promotionMap.isEmpty()) {
            //如果存在同促销ID的活动删除
            promotionMap.entrySet().removeIf(j -> {
                JSONObject promotionJson = JSONUtil.parseObj(j.getValue());
                BaseStandardPromotions promotion = promotionJson.toBean(BaseStandardPromotions.class);
                // 删除促销活动里的PromotionGoodsList
                promotionJson.remove("promotionGoodsList");
                return promotion.getOriginEndTime() != null && promotion.getOriginEndTime().getTime() < DateUtil.date().getTime();
            });
            updateQueries.add(this.getGoodsIndexPromotionUpdateRequest(esGoodsIndex, promotionMap));
        }
        cache.remove(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, esGoodsIndex.getGoodsId() + esGoodsIndex.getId()));
        cache.remove(CachePrefix.GOODS_SKU.getPrefix(esGoodsIndex.getId()));
    }

    private void updateIndexGoodsTags(EsGoodsIndex esGoodsIndex, List<UpdateQuery> updateQueries) {
        if (CollUtil.isNotEmpty(esGoodsIndex.getTags())) {
            this.setGoodsTags(esGoodsIndex);
            Map<String, Object> params = new HashMap<>();
            params.put("tags", esGoodsIndex.getTags());
            params.put("ignoreTags", esGoodsIndex.getIgnoreTags());

            updateQueries.add(UpdateQuery.builder(esGoodsIndex.getId())
                    .withParams(params)
                    .withScriptType(ScriptType.INLINE)
                    .withScript("ctx._source.tags=params.tags;ctx._source.ignoreTags=params.ignoreTags;")
                    .withRetryOnConflict(3).build());
        }
    }

    @Override
    public <T> T findIndexById(String id, Class<T> clazz) {
        T index = this.findById(id, clazz);
        if (index == null) {
            log.error("Id为{}的索引不存在！", id);
            return null;
        }
        return index;
    }

    @Override
    public EsGoodsIndex findEsGoodsIndexById(String id) {
        GoodsSku goodsSku = goodsSkuService.getById(id);
        if (goodsSku == null) {
            log.error("商品skuId为{}不存在！", id);
            return null;
        }

        EsGoodsIndex goodsIndex = this.findIndexById(id, goodsSku.getEsIndexClass());

        if (goodsIndex == null) {
            goodsIndex = this.getResetEsGoodsIndex(goodsSku);
        }
        return goodsIndex;
    }

    @Override
    public EsGoodsIndex findEsGoodsIndexByGoodsId(String goodsId) {
        Goods byId = goodsService.getById(goodsId);
        if (byId == null) {
            log.error("商品id为{}不存在！", goodsId);
            return null;
        }

        if (byId.getEsIndexClass() == EsSupplierGoodsIndex.class) {

            List<EsSupplierGoodsIndex> goodsIndices = this.findByQuery(NativeQuery.builder().withQuery(q -> q.match(m -> m.query(goodsId).field("goodsId.keyword"))).build(), EsSupplierGoodsIndex.class);

            if (CollUtil.isEmpty(goodsIndices)) {
                return null;
            }
            return goodsIndices.getFirst();
        } else {

            List<EsGoodsIndex> goodsIndices = this.findByQuery(NativeQuery.builder().withQuery(q -> q.match(m -> m.query(goodsId).field("goodsId.keyword"))).build(), EsGoodsIndex.class);

            if (CollUtil.isEmpty(goodsIndices)) {
                return null;
            }
            return goodsIndices.getFirst();
        }


    }

    /**
     * 根据id获取商品索引信息的促销信息
     *
     * @param id skuId
     * @return 促销信息map
     */
    @Override
    public Map<String, Object> getPromotionMap(String id) {
        EsGoodsIndex goodsIndex = this.findEsGoodsIndexById(id);

        //如果商品索引不为空，返回促销信息，否则返回空
        if (goodsIndex != null) {
            Map<String, Object> promotionMap = goodsIndex.getOriginPromotionMap();
            if (promotionMap == null || promotionMap.isEmpty()) {
                return HashMap.newHashMap(16);
            }
            return promotionMap;
        }
        return new HashMap<>();
    }

    /**
     * 根据id获取商品索引信息的指定促销活动的id
     *
     * @param id                skuId
     * @param promotionTypeEnum 促销活动类型
     * @return 当前商品参与的促销活动id集合
     */
    @Override
    public List<String> getPromotionIdByPromotionType(String id, PromotionTypeEnum promotionTypeEnum) {
        Map<String, Object> promotionMap = this.getPromotionMap(id);
        //如果没有促销信息，则返回新的
        if (promotionMap == null || promotionMap.isEmpty()) {
            return new ArrayList<>();
        }
        //对促销进行过滤
        List<String> keyCollect = promotionMap.keySet().stream().filter(i -> i.contains(promotionTypeEnum.name())).toList();
        List<String> promotionIds = new ArrayList<>();
        //写入促销id
        for (String key : keyCollect) {
            BaseStandardPromotions promotion = (BaseStandardPromotions) promotionMap.get(key);
            promotionIds.add(promotion.getId());
        }
        return promotionIds;
    }

    /**
     * 获取重置的商品索引
     *
     * @param goodsSku 商品sku信息
     * @return 商品索引
     */
    @Override
    public EsGoodsIndex getResetEsGoodsIndex(GoodsSku goodsSku) {
        if (Boolean.TRUE.equals(goodsSku.getDeleteFlag())) {
            return null;
        }
        Goods goods = goodsService.getById(goodsSku.getGoodsId());
        if (goods == null) {
            log.error("商品id为{}不存在！", goodsSku.getGoodsId());
            return null;
        }
        GoodsSkuDTO goodsSkuDTO = new GoodsSkuDTO(goodsSku, JSONUtil.toList(goods.getParams(), GoodsParamsDTO.class));


        List<String> brandIds = new ArrayList<>();

        List<String> categoryPaths = new ArrayList<>();

        List<String> storeCategoryPaths = new ArrayList<>();

        packageAttribute(goodsSkuDTO, brandIds, categoryPaths, storeCategoryPaths);

        List<Map<String, Object>> brandList = new ArrayList<>();
        if (CollUtil.isNotEmpty(brandIds)) {
            brandList = this.brandService.getBrandsMapsByCategory(CollUtil.distinct(brandIds), "id,name,logo");
        }
        List<Map<String, Object>> categoryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(categoryPaths)) {
            categoryList = this.categoryService.listMapsByIdsOrderByLevel(CollUtil.distinct(categoryPaths), "id,name");
        }
        List<Map<String, Object>> storeCategoryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(storeCategoryPaths)) {
            storeCategoryList = this.storeGoodsLabelService.listMapsByStoreIds(CollUtil.distinct(storeCategoryPaths), "id,label_name");
        }

        EsGoodsIndex index = wrapperEsGoodsIndex(goodsSkuDTO, brandList, categoryList, storeCategoryList);
        if (SceneEnums.SUPPLIER.name().equals(goodsSku.getScene())) {
            index = new EsSupplierGoodsIndex(index, goodsSku);
        }
        //设置促销信息
        try {
            List<PromotionGoods> skuValidPromotions = promotionsClient.findSkuValidPromotions(goodsSkuDTO.getStoreId() ,Collections.singletonList(goodsSkuDTO.getId()));

            List<PromotionGoods> promotionGoods = skuValidPromotions.stream()
                    .filter(j ->
                            (CharSequenceUtil.isNotEmpty(j.getSkuId()) && j.getSkuId().equals(goodsSku.getId())) ||
                            (j.getScopeType().equals(PromotionsScopeTypeEnum.ALL.name()) && j.getStoreId().equals("0")) ||
                            (j.getScopeType().equals(PromotionsScopeTypeEnum.ALL.name()) && j.getStoreId().equals(goodsSku.getStoreId())) ||
                            (j.getScopeType().equals(PromotionsScopeTypeEnum.PORTION_GOODS_CATEGORY.name()) && j.getScopeId().contains(goodsSku.getCategoryPath())))
                    .toList();
            setIndexPromotionsInfo(promotionGoods, index);
        } catch (Exception e) {
            log.error("获取商品促销信息失败！", e);
        }

        setGoodsTags(index);


        return this.addIndex(index);
    }

    @Override
    public List<String> checkIndexExists(List<String> skuIds, Class<?> clazz) {
        if (skuIds == null || skuIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> indexIds = new ArrayList<>();


        // 构建原生查询，检查哪些SKU在索引中存在
        NativeQueryBuilder queryBuilder = NativeQuery.builder();
        queryBuilder.withIds(skuIds);
        queryBuilder.withFields("id");
        queryBuilder.withPageable(PageRequest.of(0, skuIds.size()));

        this.findByQuery(queryBuilder.build(), clazz)
                .forEach(hit -> {
                    if (hit instanceof EsGoodsIndex goodsIndex) {
                        indexIds.add(goodsIndex.getId());
                    }
                    if (hit instanceof EsSupplierGoodsIndex supplierGoodsIndex) {
                        indexIds.add(supplierGoodsIndex.getId());
                    }
                });

        // 返回不存在的ID列表
        return skuIds.stream()
                .filter(id -> !indexIds.contains(id))
                .toList();
    }

    @Override
    public void batchCreateIndex(List<String> skuIds) {
        goodsSkuService.listByIds(skuIds).forEach(this::getResetEsGoodsIndex);
    }

    private void setGoodsTags(EsGoodsIndex index) {
        List<String> tags = Optional.ofNullable(index.getTags()).orElse(new ArrayList<>());
        List<String> ignoreTags = new ArrayList<>();
        QueryWrapper<GoodsTags> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("sort");
        goodsTagsService.list(queryWrapper).forEach(goodsTags -> {
            if (Boolean.FALSE.equals(goodsTags.getStatus())) {
                ignoreTags.add(goodsTags.getTagName());
            } else {
                String tagName = goodsTags.getTagName();
                Boolean addTag = switch (GoodsTagsTypeEnum.valueOf(goodsTags.getTagType())) {
                    case NEW -> {
                        // 判断是否是新品, 商品通过审核后 X 天内为新品
                        Date releaseTime = index.getReleaseTime();
                        DateTime dateTime = DateUtil.offsetDay(releaseTime, goodsTags.getDays());
                        yield dateTime.isAfterOrEquals(DateUtil.date());
                    }
                    case HOT -> {
                        // 判断是否是热销, 最近30天销量大于 X 件，展示此标签
                        GoodsStatisticsQueryParam goodsStatisticsQueryParam = new GoodsStatisticsQueryParam();
                        goodsStatisticsQueryParam.setSkuId(index.getId());
                        goodsStatisticsQueryParam.setStartDate(DateUtil.offsetDay(DateUtil.date(), -30));
                        goodsStatisticsQueryParam.setEndDate(DateUtil.date());

                        long goodsSalesVolume = orderFlowStatisticsClient.getGoodsSalesVolume(goodsStatisticsQueryParam);
                        yield goodsSalesVolume >= goodsTags.getDays();
                    }
                    case SELL -> {
                        // 判断是否是畅销, 最近30天用户评论数大于 X 条，展示此标签
                        EvaluationQueryParams evaluationQueryParams = new EvaluationQueryParams();
                        evaluationQueryParams.setGoodsId(index.getGoodsId());
                        evaluationQueryParams.setSkuId(index.getId());
                        evaluationQueryParams.setStartDate(DateUtil.offsetDay(DateUtil.date(), -30).toString(DatePattern.NORM_DATE_PATTERN));
                        evaluationQueryParams.setEndDate(DateUtil.date().toString(DatePattern.NORM_DATE_PATTERN));
                        long evaluationCount = memberEvaluationClient.getEvaluationCount(evaluationQueryParams);
                        yield evaluationCount >= goodsTags.getDays();
                    }
                    case CHOICE -> {
                        // 判断是否是精选, 最近30天用户好评率大于 X 时，展示此标签。
                        double praiseRate = getPraiseRate(index);
                        yield praiseRate >= goodsTags.getDays();
                    }
                    default -> null;
                };
                Map<String, Object> params = new HashMap<>();
                params.put("name", tagName);
                params.put("position", goodsTags.getTagPosition());
                params.put("sort", goodsTags.getSort());
                String tagJsonStr = JSONUtil.toJsonStr(params);
                boolean containsTag = tags.contains(tagJsonStr);
                if (addTag != null) {
                    if (addTag && !containsTag) {
                        tags.add(tagJsonStr);
                    } else if (Boolean.FALSE.equals(addTag) && containsTag) {
                        tags.removeIf(j -> j.contains(tagName));
                    }
                } else {
                    if (!containsTag) {
                        tags.add(tagJsonStr);
                    } else {
                        tags.removeIf(j -> j.contains(tagName));
                    }
                }
            }
        });
        index.setIgnoreTags(ignoreTags);
        index.setTags(tags);
    }

    private double getPraiseRate(EsGoodsIndex index) {
        EvaluationQueryParams evaluationQueryParams = new EvaluationQueryParams();
        evaluationQueryParams.setStatus("OPEN");
        evaluationQueryParams.setGoodsId(index.getGoodsId());
        evaluationQueryParams.setSkuId(index.getId());
        evaluationQueryParams.setStartDate(DateUtil.offsetDay(DateUtil.date(), -30).toString(DatePattern.NORM_DATE_PATTERN));
        evaluationQueryParams.setEndDate(DateUtil.date().toString(DatePattern.NORM_DATE_PATTERN));

        List<Map<String, Object>> list = memberEvaluationClient.getEvaluationNumberGroup(evaluationQueryParams);
        // 好评率
        int good = 0;
        int other = 0;
        for (Map<String, Object> map : list) {
            if (map.get("grade").equals(EvaluationGradeEnum.GOOD.name())) {
                good = Integer.parseInt(map.get("num").toString());
            } else {
                other += Integer.parseInt(map.get("num").toString());
            }
        }

        return CurrencyUtil.div(good, CurrencyUtil.add(good, other), 2);
    }

    private void setIndexPromotionsInfo(List<PromotionGoods> promotionGoods, EsGoodsIndex index) {
        if (CollUtil.isNotEmpty(promotionGoods)) {
            index.setPromotionMapJson(GsonUtils.toJson(promotionsClient.wrapperPromotionMapList(promotionGoods)));
        }
        // 如果存在秒杀和拼团活动,设置促销价格
        if (promotionGoods.stream().anyMatch(j -> j.getPromotionType().equals(PromotionTypeEnum.SECKILL.name()) || j.getPromotionType().equals(PromotionTypeEnum.MINUS.name()) || j.getPromotionType().equals(PromotionTypeEnum.PINTUAN.name()))) {
            Double promotionPrice = promotionGoods.stream().filter(j -> j.getPromotionType().equals(PromotionTypeEnum.SECKILL.name()) || j.getPromotionType().equals(PromotionTypeEnum.MINUS.name()) || j.getPromotionType().equals(PromotionTypeEnum.PINTUAN.name())).findFirst().map(PromotionGoods::getPrice).orElse(null);
            index.setPromotionPrice(promotionPrice);
        }
    }

    private void packageAttribute(GoodsSkuDTO goodsSkuDTO, List<String> brandIds, List<String> categoryPaths, List<String> storeCategoryPaths) {
        if (CharSequenceUtil.isNotEmpty(goodsSkuDTO.getBrandId())) {
            brandIds.add(goodsSkuDTO.getBrandId());
        }
        if (CharSequenceUtil.isNotEmpty(goodsSkuDTO.getStoreCategoryPath())) {
            storeCategoryPaths.addAll(Arrays.asList(goodsSkuDTO.getStoreCategoryPath().split(",")));
        }
        if (CharSequenceUtil.isNotEmpty((goodsSkuDTO.getCategoryPath()))) {
            categoryPaths.addAll(Arrays.asList(goodsSkuDTO.getCategoryPath().split(",")));
        }
    }

    /**
     * 修改商品活动索引
     *
     * @param goodsIndex 商品索引
     * @param key        关键字
     * @param promotion  活动
     */
    private UpdateQuery updateGoodsIndexPromotion(EsGoodsIndex goodsIndex, String key, BaseStandardPromotions promotion) {
        Map<String, Object> promotionMap;
        //数据非空处理，如果空给一个新的信息
        if (goodsIndex.getOriginPromotionMap() == null || goodsIndex.getOriginPromotionMap().isEmpty()) {
            promotionMap = HashMap.newHashMap(1);
        } else {
            promotionMap = goodsIndex.getOriginPromotionMap();
        }
        //如果活动已结束
        if (promotion.getPromotionStatus().equals(PromotionsStatusEnum.END.name()) || promotion.getPromotionStatus().equals(PromotionsStatusEnum.CLOSE.name())) {//如果存在活动
            //删除活动
            promotionMap.remove(key);
        } else {

            // 删除促销活动里的PromotionGoodsList
            JSONObject promotionJson = JSONUtil.parseObj(promotion);
            promotionJson.remove("promotionGoodsList");
            promotionMap.put(key, promotionJson);
        }
        return this.getGoodsIndexPromotionUpdateRequest(goodsIndex, promotionMap);
    }

    /**
     * 以更新部分字段的方式更新索引促销信息
     *
     * @param goodsIndex   索引
     * @param promotionMap 促销信息
     */
    private UpdateQuery getGoodsIndexPromotionUpdateRequest(EsGoodsIndex goodsIndex, Map<String, Object> promotionMap) {
        Map<String, Object> params = new HashMap<>();
        params.put("promotionMap", GsonUtils.toJson(promotionMap));

        return UpdateQuery.builder(goodsIndex.getId())
                .withParams(params)
                .withScriptType(ScriptType.INLINE)
                .withScript("ctx._source.promotionMapJson=params.promotionMap;")
                .withRetryOnConflict(3).build();
    }

    /**
     * 执行批量更新商品索引
     *
     * @param updateQueries 更新请求
     */
    private void executeBulkUpdateRequest(List<UpdateQuery> updateQueries) {
        if (updateQueries.isEmpty()) {
            return;
        }
        this.client.bulkUpdate(updateQueries, EsGoodsIndex.class);
    }

    private void generatorEsGoodsIndex(String scene) {
        try {
            // 初始化标识
            String flagCacheKey = CachePrefix.INIT_INDEX_FLAG.getPrefix();
            String processKey = CachePrefix.INIT_INDEX_PROCESS.getPrefix();
            if (SceneEnums.SUPPLIER.name().equals(scene)) {
                flagCacheKey = CachePrefix.INIT_SUPPLIER_INDEX_FLAG.getPrefix();
                processKey = CachePrefix.INIT_SUPPLIER_INDEX_PROCESS.getPrefix();
            }

            // 构建查询条件
            GoodsSearchParams searchParams = new GoodsSearchParams();
            searchParams.setScene(scene);

            // 初始化结果
            Map<String, Object> resultMap = (Map<String, Object>) cache.get(processKey);
            if (CollUtil.isEmpty(resultMap)) {
                resultMap = new HashMap<>();
                resultMap.put(KEY_SUCCESS, 0L);
                resultMap.put(KEY_FAIL, 0L);
                resultMap.put(KEY_PROCESSED, 0L);
                resultMap.put("total", this.goodsSkuService.count(searchParams.queryWrapper()));
                resultMap.put(CURRENT_INFO, "");
                cache.put(processKey, resultMap);
            }

            // 分批处理
            for (int i = 1; ; i++) {
                searchParams.setPageSize(2000);
                searchParams.setPageNumber(i);
                IPage<GoodsSkuDTO> skuIPage = goodsSkuService.getGoodsSkuDTOByPage(PageUtil.initPage(searchParams),
                        searchParams.queryWrapperGoodsSkuByCustomSql());

                log.debug("skuIPage = {}", com.alibaba.fastjson.JSONObject.toJSONString(skuIPage.getRecords()));
                if (skuIPage == null || CollUtil.isEmpty(skuIPage.getRecords())) {
                    break;
                }


                // 根据storeId分组, key 是 storeId, value 是该 storeId 下的商品Id集合
                Map<String, List<GoodsSkuDTO>> storeIdMap = skuIPage.getRecords().stream().collect(Collectors.groupingBy(GoodsSkuDTO::getStoreId));

                List<EsGoodsIndex> esGoodsIndices = new ArrayList<>();
                storeIdMap.forEach((String storeId, List<GoodsSkuDTO> goodsSkuDTOList) -> {

                    // 获取skuIds
                    List<String> skuIds = goodsSkuDTOList.stream().map(GoodsSku::getId).toList();
                    // 获取促销信息
                    List<PromotionGoods> skuValidPromotions = promotionsClient.findSkuValidPromotions(storeId, skuIds);

                    List<String> brandIds = new ArrayList<>();
                    List<String> categoryPaths = new ArrayList<>();
                    List<String> storeCategoryPaths = new ArrayList<>();

                    for (GoodsSkuDTO goodsSkuDTO : goodsSkuDTOList) {
                        packageAttribute(goodsSkuDTO, brandIds, categoryPaths, storeCategoryPaths);
                    }

                    List<Map<String, Object>> brandList = getBrandList(brandIds);
                    List<Map<String, Object>> categoryList = getCategoryList(categoryPaths);
                    List<Map<String, Object>> storeCategoryList = getStoreCategoryList(storeCategoryPaths);

//                    for (GoodsSkuDTO goodsSku : goodsSkuDTOList) {
//                        EsGoodsIndex esGoodsIndex = createEsGoodsIndex(esGoodsIndices, goodsSku, brandList, categoryList, storeCategoryList, skuValidPromotions);
//                        this.setGoodsTags(esGoodsIndex);
//                        esGoodsIndices.add(esGoodsIndex);
//                        cache.put(GoodsSkuClient.getStockCacheKey(goodsSku), goodsSku.getQuantity());
//                    }
                    Map<String, List<GoodsSkuDTO>> goodsMap = goodsSkuDTOList.stream().collect(Collectors.groupingBy(GoodsSkuDTO::getGoodsId));
                    for (Map.Entry<String, List<GoodsSkuDTO>> goodsEntry : goodsMap.entrySet()) {
                        GoodsSkuDTO goodsSku = goodsEntry.getValue().stream().sorted(Comparator.comparing(GoodsSkuDTO::getPrice)).collect(Collectors.toList()).getFirst();
                        EsGoodsIndex esGoodsIndex = createEsGoodsIndex(esGoodsIndices, goodsSku, brandList, categoryList, storeCategoryList, skuValidPromotions);
                        this.setGoodsTags(esGoodsIndex);
                        esGoodsIndices.add(esGoodsIndex);
                        cache.put(GoodsSkuClient.getStockCacheKey(goodsSku), goodsSku.getQuantity());
                    }
                });

                log.debug("esGoodsIndices.size = {}", esGoodsIndices.size());
                this.initIndex(esGoodsIndices, i == 1, scene);
            }

            cache.put(flagCacheKey, false);
            cache.vagueDel(CachePrefix.VIEW_GOODS_BUYER.getVaguePrefix(SceneEnums.MEMBER));
        } catch (Exception e) {
            log.error("商品索引生成异常：", e);
            cache.put(CachePrefix.INIT_INDEX_PROCESS.getPrefix(), null);
            cache.put(CachePrefix.INIT_INDEX_FLAG.getPrefix(), false);
        }
    }

    /**
     * 封装品牌信息
     *
     * @param brandIds 品牌id集合
     * @return 品牌信息
     */
    private List<Map<String, Object>> getBrandList(List<String> brandIds) {
        if (CollUtil.isNotEmpty(brandIds)) {
            return this.brandService.getBrandsMapsByCategory(CollUtil.distinct(brandIds), "id,name,logo");
        }
        return new ArrayList<>();
    }

    /**
     * 封装分类信息
     *
     * @param categoryPaths 分类路径集合
     * @return 分类信息
     */
    private List<Map<String, Object>> getCategoryList(List<String> categoryPaths) {
        if (CollUtil.isNotEmpty(categoryPaths)) {
            return this.categoryService.listMapsByIdsOrderByLevel(CollUtil.distinct(categoryPaths), "id,name");
        }
        return new ArrayList<>();
    }

    /**
     * 封装店铺分类信息
     *
     * @param storeCategoryPaths 店铺分类路径集合
     * @return 店铺分类信息
     */
    private List<Map<String, Object>> getStoreCategoryList(List<String> storeCategoryPaths) {
        if (CollUtil.isNotEmpty(storeCategoryPaths)) {
            return this.storeGoodsLabelService.listMapsByStoreIds(CollUtil.distinct(storeCategoryPaths), "id,label_name");
        }
        return new ArrayList<>();
    }

    /**
     * 创建商品索引
     *
     * @param esGoodsIndices     商品索引集合
     * @param goodsSku           商品sku信息
     * @param brandList          品牌信息
     * @param categoryList       分类信息
     * @param storeCategoryList  店铺分类信息
     * @param skuValidPromotions 促销信息
     * @return 商品索引
     */
    private EsGoodsIndex createEsGoodsIndex(List<EsGoodsIndex> esGoodsIndices, GoodsSkuDTO goodsSku, List<Map<String, Object>> brandList, List<Map<String, Object>> categoryList, List<Map<String, Object>> storeCategoryList, List<PromotionGoods> skuValidPromotions) {
        int skuSource = 100;
        EsGoodsIndex esGoodsIndex = wrapperEsGoodsIndex(goodsSku, brandList, categoryList, storeCategoryList);
        if (SceneEnums.SUPPLIER.name().equals(goodsSku.getScene())) {
            esGoodsIndex = new EsSupplierGoodsIndex(esGoodsIndex, goodsSku);
        }
        String goodsId = esGoodsIndex.getGoodsId();
        long count = esGoodsIndices.stream().filter(j -> j.getGoodsId().equals(goodsId)).count();
        if (count >= 1) {
            skuSource -= (int) count;
        }
        if (skuSource < 1) {
            skuSource = 1;
        }
        esGoodsIndex.setSkuSource(skuSource);
        List<PromotionGoods> promotionGoods = skuValidPromotions.stream()
                .filter(j -> (CharSequenceUtil.isNotEmpty(j.getSkuId()) && j.getSkuId().equals(goodsSku.getId())) ||
                             (j.getScopeType().equals(PromotionsScopeTypeEnum.ALL.name()) && j.getStoreId().equals("0")) ||
                             (j.getScopeType().equals(PromotionsScopeTypeEnum.ALL.name()) && j.getStoreId().equals(goodsSku.getStoreId())) ||
                             (j.getScopeType().equals(PromotionsScopeTypeEnum.PORTION_GOODS_CATEGORY.name()) && j.getStoreId().equals("0") && j.getScopeId().contains(goodsSku.getCategoryPath())) ||
                             (j.getScopeType().equals(PromotionsScopeTypeEnum.PORTION_GOODS_CATEGORY.name()) && j.getStoreId().equals(goodsSku.getStoreId()) && j.getScopeId().contains(goodsSku.getCategoryPath())))
                .toList();
        setIndexPromotionsInfo(promotionGoods, esGoodsIndex);
        return esGoodsIndex;
    }

    /**
     * 封装商品索引
     *
     * @param goodsSku          商品sku信息
     * @param brandList         品牌信息
     * @param categoryList      分类信息
     * @param storeCategoryList 店铺分类信息
     * @return 商品索引
     */
    private EsGoodsIndex wrapperEsGoodsIndex(GoodsSkuDTO goodsSku, List<Map<String, Object>> brandList,
                                             List<Map<String, Object>> categoryList,
                                             List<Map<String, Object>> storeCategoryList) {
        EsGoodsIndex index = new EsGoodsIndex(goodsSku);

        List<GoodsSkuVO> goodsListByGoodsId = goodsSkuService.getGoodsListByGoodsId(GoodsSearchParams.builder().goodsId(goodsSku.getGoodsId()).build());
        if (CollUtil.isNotEmpty(goodsListByGoodsId)) {
            index.setSkuList(JSONUtil.toJsonStr(goodsListByGoodsId));
        }

        FreightTemplateVO freightTemplate = freightTemplateClient.getFreightTemplate(goodsSku.getFreightTemplateId());
        if (freightTemplate != null) {
            index.setFreightTemplate(freightTemplate);
        }

        if (CharSequenceUtil.isNotEmpty(goodsSku.getParams())) {
            index.setAttrListByParams(JSONUtil.toList(goodsSku.getParams(), GoodsParamsDTO.class));
            index.setParams(goodsSku.getParams());
        }

        if (CollUtil.isNotEmpty(categoryList) && CharSequenceUtil.isNotEmpty(goodsSku.getCategoryPath())) {
            List<String> categoryNames = categoryList.stream()
                    .filter(o -> goodsSku.getCategoryPath().contains(o.get("id").toString()))
                    .map(o -> o.get("name").toString())
                    .toList();
            index.setCategoryNamePath(String.join(",", categoryNames));
            index.setCategoryName(categoryNames);
        }

        if (CollUtil.isNotEmpty(brandList) && CharSequenceUtil.isNotEmpty(goodsSku.getBrandId())) {
            brandList.stream()
                    .filter(p -> p.get("id").toString().equals(goodsSku.getBrandId()))
                    .findFirst()
                    .ifPresent(brandInfo -> {
                        index.setBrandName(brandInfo.get("name").toString());
                        index.setBrandUrl(brandInfo.get("logo").toString());
                    });
        }

        if (CollUtil.isNotEmpty(storeCategoryList) && CharSequenceUtil.isNotEmpty(goodsSku.getStoreCategoryPath())) {
            List<String> storeCategoryNames = storeCategoryList.stream()
                    .filter(o -> goodsSku.getStoreCategoryPath().contains(o.get("id").toString()))
                    .map(o -> o.get("label_name").toString())
                    .toList();
            index.setStoreCategoryNamePath(String.join(",", storeCategoryNames));
        }

        return index;
    }

    public void deleteProduct (Goods goods) throws Exception {
        log.debug("deleteProduct goods = {}", com.alibaba.fastjson.JSONObject.toJSONString(goods));
        if (StringUtils.isEmpty(goods.getProductId())) {
            return;
        }
        String deleteUrl = baseUrl + "/products/" + goods.getProductId();
        URI uri = new URI(deleteUrl);
        URL url = uri.toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("DELETE");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 获取响应
        int responseCode = connection.getResponseCode();

        if (responseCode == 200) {
            log.info("成功删除产品: {}, 响应代码: {}", goods.getProductId(), responseCode);
        } else {
            log.error("删除产品失败: {}, 响应代码: {}", goods.getProductId(), responseCode);
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                log.error("错误详情: {}", response.toString());
            }
        }
        connection.disconnect();
    }

    public void createProduct(Goods goods, List<GoodsSku> goodsSkus, List<GoodsSku> goodsSkuList) throws Exception {
        log.debug("createProduct goods = {}", com.alibaba.fastjson.JSONObject.toJSONString(goods));
        // 如果商品库存不为0
        GoodsSearchParams searchParams = new GoodsSearchParams();
        searchParams.setGoodsId(goods.getId());

        List<String> imageList = new ArrayList<>();
        if (CollUtil.isNotEmpty(goodsSkus)) {
            for (GoodsSku goodsSku : goodsSkus) {
                imageList.add(goodsSku.getBig());
            }
        }

        String createUrl = baseUrl + "/products";
        // 准备商品数据
        Map<String, Object> productData = new HashMap<>();
        productData.put("title", goods.getGoodsName());
        productData.put("images", imageList);

        String requestBody = com.alibaba.fastjson.JSONObject.toJSONString(productData);
        URI uri = new URI(createUrl);
        URL url = uri.toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 发送请求体
        try (java.io.OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // 获取响应
        int responseCode = connection.getResponseCode();

        if (responseCode == 200) {
            log.info("成功创建产品: {}, 响应代码: {}", goods.getId(), responseCode);
            // 读取响应内容
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                log.debug("创建产品响应: {}", response.toString());

                // 更新商品productId
                Map<String, Object> responseMap = com.alibaba.fastjson.JSONObject.parseObject(response.toString());
                if (responseMap.containsKey("productId")) {
                    String productId = responseMap.get("productId").toString();
                    goods.setProductId(productId);
                    goodsService.updateById(goods);
                    goodsSkuList.forEach(goodsSku -> {
                        goodsSku.setProductId(productId);
                    });
                    goodsSkuService.updateBatchById(goodsSkuList);
                    log.info("已更新商品productId: {}", productId);
                }
            }
        } else {
            log.error("创建产品失败:{}, 响应代码: {}", goods.getId(), responseCode);
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                log.error("错误详情: {}", response.toString());
            }
        }
        connection.disconnect();


    }
}
