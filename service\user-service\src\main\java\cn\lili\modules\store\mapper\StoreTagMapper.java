package cn.lili.modules.store.mapper;

import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.vos.StoreTagVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 商家标签数据处理层
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface StoreTagMapper extends BaseMapper<StoreTag> {

    /**
     * 获取标签分页列表（包含使用该标签的店铺数量）
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 标签VO分页列表
     */
    @Select("SELECT st.*, " +
            "(SELECT COUNT(*) FROM li_store s WHERE s.store_tag LIKE CONCAT('%', st.tag_name, '%') AND s.delete_flag = 0) as store_count " +
            "FROM li_store_tag st ${ew.customSqlSegment}")
    Page<StoreTagVO> getStoreTagPage(Page<StoreTagVO> page, @Param(Constants.WRAPPER) Wrapper<StoreTagVO> queryWrapper);

    /**
     * 获取启用的标签列表
     *
     * @return 启用的标签列表
     */
    @Select("SELECT * FROM li_store_tag WHERE enabled = 1 AND delete_flag = 0 ORDER BY sort_order ASC, create_time DESC")
    List<StoreTag> getEnabledTags();

    /**
     * 根据标签类型获取标签列表
     *
     * @param tagType 标签类型
     * @return 标签列表
     */
    @Select("SELECT * FROM li_store_tag WHERE tag_type = #{tagType} AND enabled = 1 AND delete_flag = 0 ORDER BY sort_order ASC")
    List<StoreTag> getTagsByType(@Param("tagType") String tagType);

    /**
     * 检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param excludeId 排除的ID（编辑时使用）
     * @return 存在的数量
     */
    @Select("SELECT COUNT(*) FROM li_store_tag WHERE tag_name = #{tagName} AND delete_flag = 0 " +
            "AND (#{excludeId} IS NULL OR id != #{excludeId})")
    int checkTagNameExists(@Param("tagName") String tagName, @Param("excludeId") String excludeId);

    /**
     * 批量更新标签状态
     *
     * @param ids 标签ID列表
     * @param enabled 启用状态
     * @return 更新数量
     */
    @Update("<script>" +
            "UPDATE li_store_tag SET enabled = #{enabled}, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("enabled") Boolean enabled);

    /**
     * 获取使用指定标签的店铺数量
     *
     * @param tagName 标签名称
     * @return 店铺数量
     */
    @Select("SELECT COUNT(*) FROM li_store WHERE store_tag LIKE CONCAT('%', #{tagName}, '%') AND delete_flag = 0")
    Long getStoreCountByTag(@Param("tagName") String tagName);
}
