package cn.lili.modules.store.entity.params;

import cn.lili.common.vo.PageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 店铺排名统计分页查询参数包装类
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@NoArgsConstructor
@Schema(title = "店铺排名统计分页查询参数")
public class StoreRankStatisticsListParams {

    @Schema(title = "分页参数")
    private PageVO pageVO;

    @Schema(title = "查询参数")
    private StoreRankStatisticsParams searchParams;

    public StoreRankStatisticsListParams(PageVO pageVO, StoreRankStatisticsParams searchParams) {
        this.pageVO = pageVO;
        this.searchParams = searchParams;
    }
}
