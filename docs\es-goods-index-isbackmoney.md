# ES商品索引添加 isBackMoney 字段

## 修改概述

在 Elasticsearch 商品索引中添加 `isBackMoney`（是否支持退现）字段，该字段从店铺（Store）信息中获取，用于支持商品搜索时的退现筛选功能。

## 修改内容

### 1. EsGoodsIndex 实体类修改

**文件**: `yht-sdk/src/main/java/cn/lili/modules/search/entity/dos/EsGoodsIndex.java`

**修改内容**:
```java
@Field(type = FieldType.Boolean)
@Schema(title = "是否支持退现")
private Boolean isBackMoney;
```

**修改说明**:
- 将原来的 `String isBackMoney` 改为 `Boolean isBackMoney`
- 添加了 `@Field(type = FieldType.Boolean)` ES 注解，确保在 Elasticsearch 中正确映射为布尔类型
- 添加了 `@Schema` 注解用于 API 文档生成

### 2. ES索引服务修改

**文件**: `service/goods-service/src/main/java/cn/lili/modules/search/serviceimpl/EsGoodsIndexServiceImpl.java`

**修改内容**:
```java
String storeId = goodsIndex.getStoreId();
if (StringUtils.isNotBlank(storeId)) {
    StoreVO store = storeClient.getStore(storeId);
    if (store != null) {
        goodsIndex.setProvince(store.getProvince());
        goodsIndex.setCity(store.getCity());
        goodsIndex.setDistrict(store.getDistrict());
        goodsIndex.setMarket(store.getMarket());
        goodsIndex.setIsBackMoney(store.getIsBackMoney()); // 新增
    }
}
```

**修改说明**:
- 在 `addIndex` 方法中，从店铺信息获取 `isBackMoney` 字段并设置到商品索引中
- 确保每次创建或更新商品索引时，都会同步店铺的退现支持状态

### 3. 测试用例

**文件**: `service/goods-service/src/test/java/cn/lili/modules/search/EsGoodsIndexBackMoneyTest.java`

**测试覆盖**:
- ✅ 支持退现的店铺商品索引测试
- ✅ 不支持退现的店铺商品索引测试
- ✅ 店铺不存在时的处理测试
- ✅ 字段类型验证测试
- ✅ 字段基本操作测试

## 数据流程

### 1. 商品索引创建流程
```
商品SKU → EsGoodsIndex构造 → 获取店铺信息 → 设置isBackMoney → 保存到ES
```

### 2. 字段数据来源
```
Store.isBackMoney → StoreVO.isBackMoney → EsGoodsIndex.isBackMoney → Elasticsearch
```

### 3. 更新触发时机
- 商品新增时
- 商品更新时
- 店铺信息更新时（通过 `updateGoodsIndexByStore` 方法）

## 使用场景

### 1. 商品搜索筛选
```java
// 搜索支持退现的商品
SearchRequest searchRequest = new SearchRequest();
BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
boolQuery.must(QueryBuilders.termQuery("isBackMoney", true));
```

### 2. 商品列表展示
```java
// 在商品列表中显示是否支持退现
if (Boolean.TRUE.equals(esGoodsIndex.getIsBackMoney())) {
    // 显示"支持退现"标签
}
```

### 3. 商品详情页面
```java
// 在商品详情页显示退现政策
Boolean isBackMoney = esGoodsIndex.getIsBackMoney();
String backMoneyPolicy = Boolean.TRUE.equals(isBackMoney) ? "支持退现" : "不支持退现";
```

## ES 映射配置

### 字段映射
```json
{
  "isBackMoney": {
    "type": "boolean"
  }
}
```

### 查询示例
```json
{
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "isBackMoney": true
          }
        }
      ]
    }
  }
}
```

## 兼容性说明

### 1. 向后兼容
- 新增字段不影响现有功能
- 现有商品索引在重建时会自动获取店铺的 `isBackMoney` 值
- 如果店铺信息不存在，字段值为 `null`

### 2. 数据迁移
- 现有 ES 索引需要重建以包含新字段
- 可以通过批量更新或重新索引来同步数据

### 3. 默认值处理
- 新字段默认值为 `null`
- 在业务逻辑中需要处理 `null` 值情况
- 建议在前端显示时将 `null` 视为"不支持退现"

## 性能影响

### 1. 索引大小
- 每个商品文档增加一个布尔字段，存储开销很小
- 对整体索引大小影响微乎其微

### 2. 查询性能
- 布尔字段查询性能优秀
- 支持高效的过滤和聚合操作

### 3. 更新性能
- 字段更新操作轻量级
- 不影响现有的索引更新性能

## 监控和维护

### 1. 数据一致性检查
```sql
-- 检查店铺表中 isBackMoney 字段的分布
SELECT isBackMoney, COUNT(*) as count 
FROM li_store 
GROUP BY isBackMoney;
```

### 2. ES 索引检查
```json
{
  "aggs": {
    "back_money_distribution": {
      "terms": {
        "field": "isBackMoney"
      }
    }
  }
}
```

### 3. 数据同步验证
- 定期检查店铺表和 ES 索引中 `isBackMoney` 字段的一致性
- 监控索引更新的成功率和错误日志

## 注意事项

### 1. 数据类型
- 确保字段类型为 `Boolean`，不要使用 `String`
- 处理 `null` 值的情况

### 2. 索引更新
- 店铺信息变更时需要同步更新相关商品的 ES 索引
- 建议通过消息队列异步处理批量更新

### 3. 查询优化
- 在频繁查询的场景中，考虑将 `isBackMoney` 作为过滤条件
- 可以与其他字段组合建立复合查询

## 扩展建议

### 1. 相关字段
- 可以考虑添加其他店铺级别的字段，如退货政策、售后服务等
- 统一管理店铺相关的商品属性

### 2. 缓存策略
- 对于频繁访问的店铺信息，可以考虑添加缓存
- 减少对店铺服务的调用频率

### 3. 业务扩展
- 支持更细粒度的退现规则（如按商品类别、金额等）
- 支持退现比例设置等高级功能

## 总结

通过在 ES 商品索引中添加 `isBackMoney` 字段，实现了以下目标：

1. **功能完整性**: 支持基于退现政策的商品搜索和筛选
2. **数据一致性**: 确保商品索引中的退现信息与店铺设置保持同步
3. **性能优化**: 通过 ES 索引提供高效的退现商品查询能力
4. **扩展性**: 为未来更多店铺级别的商品属性提供了参考模式

该修改为电商平台的商品搜索和展示功能提供了重要的业务支持，提升了用户体验和平台的服务能力。
