package cn.lili.modules.system.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.system.client.ServiceFeeClient;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.entity.dos.Setting;

import java.util.List;

/**
 * SettingServiceFallback
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-17 14:46
 */
public class ServiceFeeFallback implements ServiceFeeClient {
    @Override
    public List<ServiceFee> getServiceFeeListByIds(List<String> ids) {
        throw new ServiceException();
    }

    @Override
    public ServiceFee getById(String id) {
        throw new ServiceException();
    }

    @Override
    public List<ServiceFee> getDefaultServiceFeeList() {
        throw new ServiceException();
    }
}
