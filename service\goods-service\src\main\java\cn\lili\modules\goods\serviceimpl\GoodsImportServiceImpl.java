package cn.lili.modules.goods.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ExcelUtils;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.goods.entity.dos.Category;
import cn.lili.modules.goods.entity.dto.*;
import cn.lili.modules.goods.entity.enums.GoodsStockTypeEnum;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.goods.entity.enums.PurchaseRuleEnum;
import cn.lili.modules.goods.entity.vos.CategoryVO;
import cn.lili.modules.goods.integration.GoodsIntegrationService;
import cn.lili.modules.goods.service.BrandService;
import cn.lili.modules.goods.service.CategoryService;
import cn.lili.modules.goods.service.GoodsImportService;
import cn.lili.modules.goods.service.GoodsSkuService;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import cn.lili.modules.system.client.ExportLogClient;
import cn.lili.modules.system.entity.dos.ExportLog;
import cn.lili.modules.system.entity.dto.ExportDTO;
import cn.lili.modules.system.entity.enums.ExportStatusEnum;
import cn.lili.modules.system.entity.enums.ExportTypeEnum;
import cn.lili.routing.ExportRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsImportServiceImpl implements GoodsImportService {


    private final FreightTemplateClient freightTemplateClient;


    private final StoreClient storeClient;

    private final CategoryService categoryService;

    private final GoodsIntegrationService goodsIntegrationService;

    private final GoodsSkuService goodsSkuService;

    private final BrandService brandService;

    private final ExportLogClient exportLogClient;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final AmqpSender amqpSender;

    private final List<String> goodsImportCells = new ArrayList<>(List.of(
            "商品名称",
            "商品类型",
            "商品卖点",
            "商品分类",
            "运费模板",
            "发布状态",
            "商品图片",
            "成本价",
            "销售价",
            "商品库存",
            "商品重量",
            "商品详情",
            "规格图片",
            "规格编码",
            "商品规格"));

    private final List<String> goodsImportCellsComment = new ArrayList<>(List.of(
            "必填\n \n1、商品名称最多100个字符；\n",
            "必填\n \n1、虚拟商品不需要填写配送模版；\n",
            "非必填\n \n1、在商品详情页标题下面展示卖点信息，建议 60 字以内；\n",
            "必填\n \n1、选择内容为所有三级商品分类；\n",
            "1、仅商品类型为实物商品时必填；\n2、运费模板需要在商家后台先创建好；\n",
            "非必填\n \n1、不填表示为“商品创建时的默认状态”，默认为上架；\n2、填写表示设置为对应的商品状态；\n3、可填写的状态有：上架、下架；\n",
            "必填\n \n1、填写图片链接的格式为：链接1,链接2 ...（多张图片的链接用,隔开）；\n",
            "非必填\n \n1、商品成本价价格，单位元；\n2、0.01≤价格≤9999999\n3、没有添加默认为销售价；\n",
            "必填\n \n1、商品销售价价格，单位元；\n2、0.01≤价格≤9999999\n",
            "必填\n \n1、0≤库存≤99999999999；\n",
            "1、仅商品类型为实物商品时必填，重量单位默认为kg；\\n \\n2、0.001≤重量≤10000 ；\n",
            """
                     非必填
                                        \s
                     1、详情为图片链接；
                                        \s
                     2、填写链接的格式为：链接1,链接2 ...（多张图片的链接用,隔开）；
                    \s""",
            """
                     非必填
                                        \s
                     1、填写表示添加规格图片并开启添加规格图片；
                                        \s
                     2、仅第一个规格项的规格值可设置规格图片，单个规格值仅支持1张图片，所有规格值的图片填写在商品第一行，按顺序分别将图片匹配到规格值；
                                        \s
                    \s""",
            """
                     非必填
                                        \s
                     1、支持数字、字母、下划线等组合，不超过 50 个字；
                                        \s
                     2、规格编码是sku维度；
                    \s""",
            """
                     非必填
                                        \s
                     1、不填表示无规格商品；
                                        \s
                     2、填写表示多规格商品，规格填写格式为 规格项1：规格值1,规格项2：规格值2，多个多规格项用,隔开；
                                        \s
                     3、每个规格项不能超过5个字，如果为多规格，规格项的值与数量必须与第一个一致。每个规格值不能超过40个字；
                                        \s
                     4、相邻行中，商品名称和商品分类相同的规格默认关联同一个商品，以第一行商品设置作为商品信息；
                                        \s
                     5、规格可枚举的组合若未导入，系统会自动创建库存为0、价格为999999的规格，以防止误操作卖出；
                                        \s
                     6、同商品下不同规格信息如果填写错误，以第一个规格信息为准填入，例如：规格A、B同属一个商品，但状态一个为上架一个为下架，则默认取第一个规格状态填入；
                                        \s
                    \s"""));

    private final List<String> goodsImportExampleCells1 = new ArrayList<>(List.of(
            "示例商品",
            "实物商品",
            "春季新款,时尚潮流",
            "1731593948046315521-手机",
            "0-默认包邮",
            "上架",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/fb668b0e89d34083ba0e648a8ff98558.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/bb6aee3c304247e2a26e6ad8d9bf43ce.jpg",
            "1000",
            "1000",
            "100",
            "1",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/a3943bec17dd4634afed3e686ad4319e.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/fc538125cc0c43e797e9e72df2065c46.jpg",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/e08909e7c38c46509e9f70789a4e1759.avif",
            "691234",
            "外观:蓝色钛金属,内存:8G"));

    private final List<String> goodsImportExampleCells2 = new ArrayList<>(List.of(
            "示例商品",
            "实物商品",
            "春季新款,时尚潮流",
            "1731593948046315521-手机",
            "0-默认包邮",
            "上架",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/fb668b0e89d34083ba0e648a8ff98558.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/bb6aee3c304247e2a26e6ad8d9bf43ce.jpg",
            "1100",
            "1100",
            "100",
            "1",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/a3943bec17dd4634afed3e686ad4319e.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/fc538125cc0c43e797e9e72df2065c46.jpg",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/0b65a36a5c774924ac65e1e1f555e7e8.avif",
            "691235",
            "外观:原色钛金属,内存:8G"));

    private final int[] getGoodsImportColumnWidth = new int[]
            {7000, 7000, 7000, 7000, 7000, 7000, 3000, 7000, 3000, 3000, 3000, 7000, 7000, 7000, 7000};

    private final List<String> goodsImportCellsSupplier = new ArrayList<>(List.of(
            "商品名称",
            "商品类型",
            "商品卖点",
            "商品分类",
            "运费模板",
            "发布状态",
            "商品图片",
            "是否支持代发",
            "是否支持采购",
            "采购单位",
            "采购规则",
            "成本价(采购无需填写)",
            "销售价(采购无需填写)",
            "商品库存",
            "商品重量",
            "商品详情",
            "规格图片",
            "规格编码",
            "商品规格"));

    private final List<String> goodsImportCellsSupplierComment = new ArrayList<>(List.of(
            "必填\n \n1、商品名称最多100个字符；\n",
            "必填\n \n1、虚拟商品不需要填写配送模版；\n",
            "非必填\n \n1、在商品详情页标题下面展示卖点信息，建议 60 字以内；\n",
            "必填\n \n1、选择内容为所有三级商品分类；\n",
            "1、仅商品类型为实物商品时必填；\n2、运费模板需要在商家后台先创建好；\n",
            "非必填\n \n1、不填表示为“商品创建时的默认状态”，默认为上架；\n2、填写表示设置为对应的商品状态；\n3、可填写的状态有：上架、下架；\n",
            "必填\n \n1、填写图片链接的格式为：链接1,链接2 ...（多张图片的链接用,隔开）；\n",
            "1、代发和采购最少要支持一个，可同时支持代发和采购；\n2、代发需要填写成本价及销售价\n",
            "1、代发和采购最少要支持一个，可同时支持代发和采购；\n2、采购需要填写采购规则\n2、采购不需要填写成本价及销售价\n",
            "非必填\n \n1、支持采购时填写，不填默认为SKU；\n",
            "1、支持采购时填写；\n2、填写规则为 10-20, 表示当商品购买数量 ≥10 时，售价为 ￥20 /个。多个 `,` 隔开, 后一个价格必须小于前一个价格,最多3个\n",
            "非必填\n \n1、商品成本价价格，单位元；\n2、0.01≤价格≤9999999\n3、没有添加默认为销售价；\n",
            "必填\n \n1、商品销售价价格，单位元；\n2、0.01≤价格≤9999999\n",
            "必填\n \n1、0≤库存≤99999999999；\n",
            "1、仅商品类型为实物商品时必填，重量单位默认为kg；\\n \\n2、0.001≤重量≤10000 ；\n",
            """
                     非必填
                                        \s
                     1、详情为图片链接；
                                        \s
                     2、填写链接的格式为：链接1,链接2 ...（多张图片的链接用,隔开）；
                    \s""",
            """
                     非必填
                                        \s
                     1、填写表示添加规格图片并开启添加规格图片；
                                        \s
                     2、仅第一个规格项的规格值可设置规格图片，单个规格值仅支持1张图片，所有规格值的图片填写在商品第一行，按顺序分别将图片匹配到规格值；
                                        \s
                    \s""",
            """
                     非必填
                                        \s
                     1、支持数字、字母、下划线等组合，不超过 50 个字；
                                        \s
                     2、规格编码是sku维度；
                    \s""",
            """
                     非必填
                                        \s
                     1、不填表示无规格商品；
                                        \s
                     2、填写表示多规格商品，规格填写格式为 规格项1：规格值1,规格项2：规格值2，多个多规格项用,隔开；
                                        \s
                     3、每个规格项不能超过5个字，如果为多规格，规格项的值与数量必须与第一个一致。每个规格值不能超过40个字；
                                        \s
                     4、相邻行中，商品名称和商品分类相同的规格默认关联同一个商品，以第一行商品设置作为商品信息；
                                        \s
                     5、规格可枚举的组合若未导入，系统会自动创建库存为0、价格为999999的规格，以防止误操作卖出；
                                        \s
                     6、同商品下不同规格信息如果填写错误，以第一个规格信息为准填入，例如：规格A、B同属一个商品，但状态一个为上架一个为下架，则默认取第一个规格状态填入；
                                        \s
                    \s"""));

    private final List<String> goodsImportSupplierExampleCells1 = new ArrayList<>(List.of(
            "示例商品-代理",
            "实物商品",
            "春季新款,时尚潮流",
            "1731593948046315521-手机",
            "0-默认包邮",
            "上架",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/fb668b0e89d34083ba0e648a8ff98558.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/bb6aee3c304247e2a26e6ad8d9bf43ce.jpg",
            "是",
            "否",
            "",
            "",
            "1000",
            "1000",
            "100",
            "1",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/a3943bec17dd4634afed3e686ad4319e.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/fc538125cc0c43e797e9e72df2065c46.jpg",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/e08909e7c38c46509e9f70789a4e1759.avif",
            "691234",
            "外观:蓝色钛金属,内存:8G"));

    private final List<String> goodsImportSupplierExampleCells2 = new ArrayList<>(List.of(
            "示例商品-采购",
            "实物商品",
            "春季新款,时尚潮流",
            "1731593948046315521-手机",
            "0-默认包邮",
            "上架",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/fb668b0e89d34083ba0e648a8ff98558.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/STORE/1376417684140326912/default/bb6aee3c304247e2a26e6ad8d9bf43ce.jpg",
            "是",
            "是",
            "SKU",
            "10-20,20-15,30-10",
            "1100",
            "1100",
            "100",
            "1",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/a3943bec17dd4634afed3e686ad4319e.jpg,https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/fc538125cc0c43e797e9e72df2065c46.jpg",
            "https://yht-oss.oss-cn-beijing.aliyuncs.com/MANAGER/undefined/0b65a36a5c774924ac65e1e1f555e7e8.avif",
            "691235",
            "外观:原色钛金属,内存:8G"));

    private final int[] getGoodsImportColumnWidthSupplier = new int[]
            {7000, 7000, 3000, 3000, 3000, 7000, 7000, 7000, 7000, 7000, 14000, 3000, 3000, 3000, 3000, 7000, 7000, 7000, 7000};


    private static List<Map<String, Object>> findMissingSpecs(List<Map<String, Object>> existingSpecs, List<Map<String, Object>> allSpecs) {
        Set<Map<String, Object>> existingSet = new HashSet<>(existingSpecs);

        return allSpecs.stream()
                .filter(spec -> !existingSet.contains(spec))
                .toList();
    }

    private static void generateCombinations(List<String> keys, Map<String, Set<Object>> valueMap, Map<String, Object> current, List<Map<String, Object>> allSpecs) {
        if (keys.isEmpty()) {
            allSpecs.add(new HashMap<>(current));
            return;
        }

        String key = keys.removeFirst();
        for (Object value : valueMap.get(key)) {
            if (value == null) {
                continue;
            }
            current.put(key, value);
            generateCombinations(keys, valueMap, current, allSpecs);
            current.remove(key);
        }
        keys.addFirst(key);
    }

    private static List<Map<String, Object>> generateAllSpecs(List<Map<String, Object>> existingSpecs) {
        Set<String> keys = existingSpecs.stream()
                .flatMap(spec -> spec.keySet().stream())
                .collect(Collectors.toSet());

        Map<String, Set<Object>> valueMap = new HashMap<>();
        for (String key : keys) {
            valueMap.put(key, existingSpecs.stream()
                    .map(spec -> spec.get(key))
                    .collect(Collectors.toSet()));
        }

        List<Map<String, Object>> allSpecs = new ArrayList<>();
        generateCombinations(new ArrayList<>(valueMap.keySet()), valueMap, new HashMap<>(), allSpecs);
        return allSpecs;
    }

    @Override
    public void download(HttpServletResponse response, String storeId, String scene) {

        //创建Excel工作薄对象
        Workbook workbook;
        if (SceneEnums.SUPPLIER.name().equals(scene)) {
            workbook = createSupplierGoodsRow();
        } else {
            workbook = createGoodsRow();
        }

        String businessCategory = storeClient.getBusinessCategory(storeId);
        if (CharSequenceUtil.isEmpty(businessCategory)) {
            throw new ServiceException(ResultCode.STORE_BUSINESS_CATEGORY_NOT_EXIST);
        }
        List<CategoryVO> categoryVOList = this.categoryService.getStoreCategory();
        List<String> categoryNameList = new ArrayList<>();

        //先简单写，后期优化
        //循环三次添加值
        //循环列表，存放ID-分类名称
        for (CategoryVO categoryVO1 : categoryVOList) {
            for (CategoryVO categoryVO2 : categoryVO1.getChildren()) {
                for (CategoryVO categoryVO3 : categoryVO2.getChildren()) {
                    categoryNameList.add(categoryVO3.getId() + "-" + categoryVO3.getName());
                }
            }
        }

        List<String> freightTemplateNameList = new ArrayList<>();
        freightTemplateNameList.add("0-默认包邮");
        //循环列表，存放ID-运费模板名称
        for (FreightTemplateVO freightTemplateVO : freightTemplateClient.getFreightTemplateList(storeId)) {
            freightTemplateNameList.add(freightTemplateVO.getId() + "-" + freightTemplateVO.getName());
        }

        //添加商品类型
        int goodsTypeColNum = getCellIndex("商品类型", scene);
        if (SceneEnums.SUPPLIER.name().equals(scene)) {
            ExcelUtils.excelTo255(workbook, "hiddenGoodsType", 1, new String[]{"实物商品"}, goodsTypeColNum, goodsTypeColNum);
        } else {
            ExcelUtils.excelTo255(workbook, "hiddenGoodsType", 1, new String[]{"实物商品", "虚拟商品"}, goodsTypeColNum, goodsTypeColNum);
        }

        //添加分类
        int categoryColNum = getCellIndex("商品分类", scene);
        ExcelUtils.excelTo255(workbook, "hiddenCategoryVO", 2, categoryNameList.toArray(new String[]{}), categoryColNum, categoryColNum);

        //添加运费模板
        int freightTemplateColNum = getCellIndex("运费模板", scene);
        ExcelUtils.excelTo255(workbook, "hiddenFreightTemplateVO", 3, freightTemplateNameList.toArray(new String[]{}), freightTemplateColNum,
                freightTemplateColNum);

        //添加发布状态
        int releaseColNum = getCellIndex("发布状态", scene);
        ExcelUtils.excelTo255(workbook, "hiddenRelease", 4, new String[]{"上架", "下架"}, releaseColNum, releaseColNum);

        if (SceneEnums.SUPPLIER.name().equals(scene)) {

            int hiddenProxyColNum = goodsImportCellsSupplier.indexOf("是否支持代发");
            ExcelUtils.excelTo255(workbook, "hiddenProxy", 5, new String[]{"是", "否"}, hiddenProxyColNum, hiddenProxyColNum);
            int hiddenPurchaseColNum = goodsImportCellsSupplier.indexOf("是否支持采购");
            ExcelUtils.excelTo255(workbook, "hiddenPurchase", 6, new String[]{"是", "否"}, hiddenPurchaseColNum, hiddenPurchaseColNum);
            int hiddenPurchaseRuleColNum = goodsImportCellsSupplier.indexOf("采购单位");
            ExcelUtils.excelTo255(workbook, "hiddenPurchaseRule", 7, new String[]{"SKU", "SPU"}, hiddenPurchaseRuleColNum,
                    hiddenPurchaseRuleColNum);
        }

        //设置公共属性，列表名称
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("下载商品导入模板", StandardCharsets.UTF_8) + ".xls");
        try (ServletOutputStream out = response.getOutputStream()) {
            workbook.write(out);
        } catch (Exception e) {
            log.error("下载商品导入模板错误", e);
        }
    }

    private int getCellIndex(String cellName, String scene) {
        return SceneEnums.SUPPLIER.name().equals(scene) ? goodsImportCellsSupplier.indexOf(cellName) : goodsImportCells.indexOf(cellName);
    }

    private Workbook createGoodsRow() {
        return ExcelUtils.createGoodsRow(
                "商品导入模板",
                goodsImportCells,
                goodsImportCellsComment,
                getGoodsImportColumnWidth,
                goodsImportExampleCells1,
                goodsImportExampleCells2);
    }

    private Workbook createSupplierGoodsRow() {
        return ExcelUtils.createGoodsRow(
                "供应商商品导入模板",
                goodsImportCellsSupplier,
                goodsImportCellsSupplierComment,
                getGoodsImportColumnWidthSupplier,
                goodsImportSupplierExampleCells1,
                goodsImportSupplierExampleCells2);
    }

    @Override
    public void importExcel(MultipartFile files, String scene) throws Exception {
        InputStream inputStream;

        inputStream = files.getInputStream();
        ExcelReader excelReader = ExcelUtil.getReader(inputStream);

        // 读取列表
        // 检测数据-查看分类、模板、计量单位是否存在
        List<List<Object>> read = excelReader.read(1, excelReader.getRowCount());
        this.checkData(read, scene);

        List<GoodsImportDTO> goodsImportDTOList = this.readGoodsImportData(read, scene);
        //添加商品
        if (SceneEnums.SUPPLIER.name().equals(scene)) {
            addSupplierGoodsList(goodsImportDTOList);
        } else {
            addGoodsList(goodsImportDTOList);
        }

    }

    @Override
    public void queryExportStock(HttpServletResponse response, GoodsSearchParams searchParams) {
        List<GoodsSkuStockDTO> goodsSkuStockDTOList = this.goodsSkuService.goodsSkuStock(searchParams);
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }

        if (CollUtil.isEmpty(goodsSkuStockDTOList)) {
            throw new ServiceException(ResultCode.EXPORT_DATA_ERROR);
        }

        List<Object> exportList = goodsSkuStockDTOList.stream()
                .map(Object.class::cast) // 进行类型转换
                .toList();

        // 保存导出记录
        ExportLog exportLog = new ExportLog();
        String fileName = "商品库存列表-" + System.currentTimeMillis();
        exportLog.setFileName(fileName);
        if (currentUser.getScene().name().equals(SceneEnums.STORE.name()) || currentUser.getScene().name().equals(SceneEnums.SUPPLIER.name())) {
            exportLog.setStoreId(currentUser.getExtendId());
            exportLog.setStoreName(currentUser.getExtendName());
        } else {
            exportLog.setStoreId("-1");
            exportLog.setStoreName("平台");
        }
        exportLog.setOperator(currentUser.getNickName());
        exportLog.setExportNum(exportList.size());
        exportLog.setType(ExportTypeEnum.GOODS_STOCK.name());
        exportLog.setStatus(ExportStatusEnum.EXPORTING.name());
        ExportLog saveExport = exportLogClient.saveExport(exportLog);

        // 发送MQ消息，异步处理导出数据
        ExportDTO exportDTO = new ExportDTO();
        exportDTO.setExportList(exportList);
        exportDTO.setExportId(saveExport.getId());
        exportDTO.setFileName(fileName);
        exportDTO.setExportTypeEnum(ExportTypeEnum.GOODS_STOCK);
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getExport()).routingKey(ExportRoutingKey.EXPORT_DATA).message(JSONUtil.toJsonStr(exportDTO)).build());

    }

    @Override
    public void importStock(String storeId, MultipartFile files) {
        List<GoodsSkuStockDTO> goodsSkuStockDTOList = new ArrayList<>();
        try (InputStream inputStream = files.getInputStream()) {
            // 使用 WorkbookFactory.create 方法读取 Excel 文件
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 我们只读取第一个sheet

            // 检查第一个sheet的行数是否超过10002行
            if (sheet.getPhysicalNumberOfRows() > 10002) {
                throw new ServiceException(ResultCode.GOODS_STOCK_IMPORT_ERROR, "Excel行数超过10002行");
            }
            // 遍历行和单元格
            Iterator<Row> rowIterator = sheet.rowIterator();
            int rowIndex = 0;
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                rowIndex++;

                // 跳过表头
                if (rowIndex < 3) {
                    continue;
                }

                List<Object> objects = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    objects.add(getCellValue(row.getCell(i)));
                }

                // 判断商品是否存在
                GoodsSearchParams goodsSearchParams = new GoodsSearchParams();
                goodsSearchParams.setGoodsId(getCellValue(row.getCell(0)));
                goodsSearchParams.setId(getCellValue(row.getCell(1)));
                goodsSearchParams.setStoreId(storeId);
                boolean exist = this.goodsSkuService.checkGoodsSkuExist(goodsSearchParams);

                // 判断数据格式
                if (!"增".equals(getCellValue(row.getCell(2))) && !"减".equals(getCellValue(row.getCell(2)))) {
                    throw new ServiceException(ResultCode.GOODS_STOCK_IMPORT_ERROR, "库存修改方向列数据必须是“增”或“减”");
                } else if (!NumberUtil.isInteger(getCellValue(row.getCell(3))) || Integer.parseInt(getCellValue(row.getCell(3))) < 0) {
                    throw new ServiceException(ResultCode.GOODS_STOCK_IMPORT_ERROR, "库存必须是正整数");
                } else if (!exist) {
                    throw new ServiceException(ResultCode.GOODS_STOCK_IMPORT_ERROR, "第" + rowIndex + "行商品不存在");
                }
                GoodsSkuStockDTO goodsSkuStockDTO = new GoodsSkuStockDTO();
                goodsSkuStockDTO.setGoodsId(getCellValue(row.getCell(0)));
                goodsSkuStockDTO.setSkuId(getCellValue(row.getCell(1)));
                goodsSkuStockDTO.setType(GoodsStockTypeEnum.fromDescription(getCellValue(row.getCell(2))).name());
                goodsSkuStockDTO.setQuantity(Integer.parseInt(getCellValue(row.getCell(3))));
                goodsSkuStockDTOList.add(goodsSkuStockDTO);
            }
        } catch (IOException e) {
            log.error("IOException occurred while processing the Excel file.", e);
            throw new ServiceException(ResultCode.GOODS_STOCK_IMPORT_ERROR, e.getMessage());
        }

        // 批量修改商品库存
        this.goodsIntegrationService.updateStocksByType(goodsSkuStockDTOList);
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 将数值转换为整数以去掉小数点
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 检测表格必填项目
     *
     * @param read  读取的表格数据
     * @param scene 场景
     */
    private void checkData(List<List<Object>> read, String scene) {
        if (read.isEmpty()) {
            throw new ServiceException(ResultCode.GOODS_IMPORT_SIZE_ERROR);
        }
        for (int i = 0; i < read.size(); i++) {
            List<Object> objects = read.get(i);
            if (objects.isEmpty()) {
                throw new ServiceException(ResultCode.GOODS_IMPORT_REQUIRED_ERROR);
            }

            int dataLength = SceneEnums.SUPPLIER.name().equals(scene) ? goodsImportCellsSupplier.size() : goodsImportCells.size();
            // 如果数据长度小于表头长度，补全数据
            if (objects.size() < dataLength) {
                while (objects.size() < dataLength) {
                    objects.add("");
                }
            }

            List<String> cells = SceneEnums.SUPPLIER.name().equals(scene) ? goodsImportCellsSupplier : goodsImportCells;

            int finalI = i;
            List<String> ignoreList = new ArrayList<>(Arrays.asList("商品卖点", "发布状态", "商品详情", "规格图片", "规格编码", "商品规格"));

            if (this.getCellIndex("是否支持采购", scene) > 0 &&
                this.getCellIndex("是否支持代发", scene) > 0 &&
                objects.get(this.getCellIndex("是否支持采购", scene)) != null &&
                objects.get(this.getCellIndex("是否支持代发", scene)) != null) {

                if (objects.get(this.getCellIndex("是否支持采购", scene)).toString().equals("否") && objects.get(this.getCellIndex("是否支持代发", scene)).toString().equals("否")) {
                    throw new ServiceException(ResultCode.EMPTY_ERROR, "供应商商品，必须选择一个销售模式（支持采购 或 支持代发）");
                }

                if (objects.get(this.getCellIndex("是否支持采购", scene)).toString().equals("是")) {
                    int hiddenPurchaseRuleColNum = goodsImportCellsSupplier.indexOf("采购规则");
                    if (hiddenPurchaseRuleColNum >= objects.size() || objects.get(hiddenPurchaseRuleColNum) == null || CharSequenceUtil.isEmpty(objects.get(hiddenPurchaseRuleColNum).toString())) {
                        throw new ServiceException(ResultCode.EMPTY_ERROR, "支持采购的情况下，第" + (finalI + 2) + "行，采购规则 列，不能为空");
                    }
                    ignoreList.add("成本价(采购无需填写)");
                    ignoreList.add("销售价(采购无需填写)");
                } else {
                    int costColNum = goodsImportCellsSupplier.indexOf("成本价(采购无需填写)");
                    int priceColNum = goodsImportCellsSupplier.indexOf("销售价(采购无需填写)");
                    if (costColNum >= objects.size() || objects.get(costColNum) == null || CharSequenceUtil.isEmpty(objects.get(costColNum).toString())) {
                        throw new ServiceException(ResultCode.EMPTY_ERROR, "不支持采购的情况下，第" + (finalI + 2) + "行，成本价(采购无需填写) 列，不能为空");
                    }
                    if (priceColNum >= objects.size() || objects.get(priceColNum) == null || CharSequenceUtil.isEmpty(objects.get(priceColNum).toString())) {
                        throw new ServiceException(ResultCode.EMPTY_ERROR, "不支持采购的情况下，第" + (finalI + 2) + "行，销售价(采购无需填写) 列，不能为空");
                    }
                    ignoreList.add("采购单位");
                    ignoreList.add("采购规则");
                }
            }

            if (this.getCellIndex("商品类型", scene) > 0 && objects.get(this.getCellIndex("商品类型", scene)) != null && objects.get(this.getCellIndex("商品类型", scene)).toString().equals("虚拟商品")) {
                ignoreList.add("运费模板");
                ignoreList.add("商品重量");
            }


            cells.forEach(cell -> {
                try {

                    if (!ignoreList.contains(cell) &&
                        (this.getCellIndex(cell, scene) < 0 ||
                         objects.size() <= this.getCellIndex(cell, scene) ||
                         objects.get(this.getCellIndex(cell, scene)) == null ||
                         CharSequenceUtil.isEmpty(objects.get(this.getCellIndex(cell, scene)).toString()) ||
                         objects.get(this.getCellIndex(cell, scene)).toString().equals(ExcelUtils.getColumnLetter(this.getCellIndex(cell, scene))))) {
                        throw new ServiceException(ResultCode.EMPTY_ERROR, "第" + (finalI + 2) + "行，" + cell + " 列，不能为空");
                    }

                } catch (ServiceException se) {
                    throw se;
                } catch (Exception e) {
                    throw new ServiceException(ResultCode.EMPTY_ERROR, "解析第" + (finalI + 2) + "行，" + cell + " 列，出现异常,请检查数据是否符合规范");
                }
            });

        }
    }

    /**
     * 读取商品导入数据
     *
     * @param read  读取的表格数据
     * @param scene 场景
     * @return 商品导入数据
     */
    private List<GoodsImportDTO> readGoodsImportData(List<List<Object>> read, String scene) {
        List<GoodsImportDTO> goodsImportDTOList = new ArrayList<>();
        for (int i = 0; i < read.size(); i++) {
            List<Object> objects = read.get(i);

            if (objects.getFirst().toString().equals("以下为示例数据，填写表格时请删除，仅保留表头")) {
                continue;
            }
            GoodsImportDTO goodsImportDTO = new GoodsImportDTO();

            // 商品分类
            int categoryColNum = getCellIndex("商品分类", scene);
            String categoryId = ExcelUtils.getRequiredColValue(objects, categoryColNum, i, "商品分类").substring(0, objects.get(categoryColNum).toString().indexOf("-"));
            Category category = categoryService.getCategoryById(categoryId);
            if (category == null) {
                throw new ServiceException("商品分类不存在：" + categoryId.substring(categoryId.indexOf("-")));
            }
            goodsImportDTO.setCategory(category);

            // 商品类型
            int goodsTypeColNum = getCellIndex("商品类型", scene);
            goodsImportDTO.setGoodsType(ExcelUtils.getRequiredColValue(objects, goodsTypeColNum, i, "商品类型").equals("实物商品") ? "PHYSICAL_GOODS" : "VIRTUAL_GOODS");

            // 运费模板
            int templateColNum = getCellIndex("运费模板", scene);
            if (GoodsTypeEnum.PHYSICAL_GOODS.name().equals(goodsImportDTO.getGoodsType())) {
                String templateId = ExcelUtils.getRequiredColValue(objects, templateColNum, i, "运费模板").substring(0, objects.get(templateColNum).toString().indexOf("-"));
                if (templateId.equals("0")) {
                    goodsImportDTO.setTemplate("0");
                } else {
                    FreightTemplateVO freightTemplateVO = freightTemplateClient.getFreightTemplate(templateId);
                    if (freightTemplateVO == null) {
                        throw new ServiceException(ResultCode.EMPTY_ERROR,
                                "配送模板不存在：" + templateId.substring(templateId.indexOf("-")));
                    }
                }
                goodsImportDTO.setTemplate(templateId);
            }

            // 商品名称
            int goodsNameColNum = getCellIndex("商品名称", scene);
            goodsImportDTO.setGoodsName(ExcelUtils.getRequiredColValue(objects, goodsNameColNum, i, "商品名称"));

            int sellingPointColNum = getCellIndex("商品卖点", scene);
            goodsImportDTO.setSellingPoint(Objects.requireNonNullElse(ExcelUtils.getColValue(objects, sellingPointColNum), ""));

            goodsImportDTO.setGoodsUnit("个");

            int releaseColNum = getCellIndex("发布状态", scene);
            goodsImportDTO.setRelease(Objects.requireNonNullElse("上架".equals(ExcelUtils.getColValue(objects, releaseColNum)), true));

            int imagesColNum = getCellIndex("商品图片", scene);
            List<String> goodsGalleryList = new ArrayList<>(Arrays.asList(ExcelUtils.getRequiredColValue(objects, imagesColNum, i, "商品图片").split(",")));
            goodsImportDTO.setImages(goodsGalleryList);
            goodsImportDTO.setGoodsGalleryList(goodsGalleryList);


            int quantityColNum = getCellIndex("商品库存", scene);
            goodsImportDTO.setQuantity(Convert.toInt(ExcelUtils.getRequiredColValue(objects, quantityColNum, i, "商品库存")));

            int weightColNum = getCellIndex("商品重量", scene);
            goodsImportDTO.setWeight(Convert.toDouble(ExcelUtils.getRequiredColValue(objects, weightColNum, i, "商品重量")));

            int snColNum = getCellIndex("规格编码", scene);
            goodsImportDTO.setSn(Objects.requireNonNullElse(ExcelUtils.getColValue(objects, snColNum), RandomUtil.randomNumbers(5)));

            int introColNum = getCellIndex("商品详情", scene);
            goodsImportDTO.setIntro(Objects.requireNonNullElse(ExcelUtils.getColValue(objects, introColNum), ""));

            int specImageColNum = getCellIndex("规格图片", scene);
            goodsImportDTO.setSpecsImage(Objects.requireNonNullElse(ExcelUtils.getColValue(objects, specImageColNum), ""));

            int skuKeyColNum = getCellIndex("商品规格", scene);
            String skuKeyColValue = ExcelUtils.getColValue(objects, skuKeyColNum);
            if (skuKeyColValue == null) {
                goodsImportDTO.setSpecs("{\"默认\":\"默认\"}");
            } else {
                String skuJson = "{" + skuKeyColValue + "}";
                String skuValue = skuJson.replace(":", "\":\"")
                        .replace(",", "\",\"")
                        .replace("{", "{\"")
                        .replace("}", "\"}");
                goodsImportDTO.setSpecs(skuValue);
            }

            if (SceneEnums.SUPPLIER.name().equals(scene)) {
                int hiddenProxyColNum = goodsImportCellsSupplier.indexOf("是否支持代发");
                goodsImportDTO.setSupportProxy(ExcelUtils.getRequiredColValue(objects, hiddenProxyColNum, i, "是否支持代发").equals("是"));

                int hiddenPurchaseColNum = goodsImportCellsSupplier.indexOf("是否支持采购");
                goodsImportDTO.setSupportPurchase(ExcelUtils.getRequiredColValue(objects, hiddenPurchaseColNum, i, "是否支持采购").equals("是"));

                if (Boolean.TRUE.equals(goodsImportDTO.getSupportPurchase())) {
                    int hiddenPurchaseRuleColNum = goodsImportCellsSupplier.indexOf("采购单位");
                    goodsImportDTO.setPurchaseRule("SPU".equals(ExcelUtils.getRequiredColValue(objects, hiddenPurchaseRuleColNum, i, "采购单位")) ? PurchaseRuleEnum.SPU :
                            PurchaseRuleEnum.SKU);
                }

                int purchaseRulesColNum = goodsImportCellsSupplier.indexOf("采购规则");
                if (Boolean.TRUE.equals(goodsImportDTO.getSupportPurchase())) {
                    List<Wholesale> wholesaleList = new ArrayList<>();
                    for (String rule : objects.get(purchaseRulesColNum).toString().split(",")) {
                        Wholesale wholesale = new Wholesale();
                        wholesale.setNum(Convert.toInt(rule.substring(0, rule.indexOf("-"))));
                        wholesale.setPrice(Convert.toDouble(rule.substring(rule.indexOf("-") + 1)));
                        wholesaleList.add(wholesale);
                    }
                    goodsImportDTO.setWholesaleList(wholesaleList);
                }

                int costColNum = getCellIndex("成本价(采购无需填写)", scene);
                int priceColNum = getCellIndex("销售价(采购无需填写)", scene);
                if (Boolean.FALSE.equals(goodsImportDTO.getSupportPurchase())) {
                    goodsImportDTO.setCost(Convert.toDouble(ExcelUtils.getRequiredColValue(objects, costColNum, i, "成本价(采购无需填写)")));
                    goodsImportDTO.setPrice(Convert.toDouble(ExcelUtils.getRequiredColValue(objects, priceColNum, i, "销售价(采购无需填写)")));
                }

            } else {
                int costColNum = getCellIndex("成本价", scene);
                goodsImportDTO.setCost(Convert.toDouble(Objects.requireNonNullElse(ExcelUtils.getColValue(objects, costColNum), "")));

                int priceColNum = getCellIndex("销售价", scene);
                goodsImportDTO.setPrice(Convert.toDouble(ExcelUtils.getRequiredColValue(objects, priceColNum, i, "销售价")));
            }
            goodsImportDTOList.add(goodsImportDTO);
        }
        return goodsImportDTOList;
    }


    /**
     * 添加商品
     *
     * @param goodsImportDTOList 商品列表
     */
    private void addGoodsList(List<GoodsImportDTO> goodsImportDTOList) {
        Map<String, List<GoodsImportDTO>> collect = goodsImportDTOList.stream().collect(Collectors.groupingBy(GoodsImportDTO::getGoodsName));
        for (Map.Entry<String, List<GoodsImportDTO>> entry : collect.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                GoodsImportDTO goodsImportDTO = entry.getValue().getFirst();
                GoodsOperationDTO goodsOperationDTO = new GoodsOperationDTO(entry.getValue().getFirst());
                List<GoodsSkuOperationDTO> skuDTOList = new ArrayList<>();
                List<String> specList = new ArrayList<>();
                for (GoodsImportDTO goodsImportDTOSku : entry.getValue()) {
                    // 检查规格项是否与第一个规格项一致，如果不一致则提示错误
                    if (!skuDTOList.isEmpty()) {
                        boolean typeJSON = JSONUtil.isTypeJSON(goodsImportDTOSku.getSpecs());
                        if (!typeJSON) {
                            throw new ServiceException(ResultCode.EMPTY_ERROR, "商品名称：" + goodsOperationDTO.getGoodsName() + "，规格格式错误");
                        }
                        JSONObject specObj = JSONUtil.parseObj(goodsImportDTOSku.getSpecs());
                        JSONObject firstSpecObj = JSONUtil.parseObj(skuDTOList.getFirst().getSpecs());
                        if (!specObj.keySet().equals(firstSpecObj.keySet())) {
                            throw new ServiceException(ResultCode.GOODS_IMPORT_SPEC_ERROR);
                        }
                    }

                    specList.add(goodsImportDTOSku.getSpecs());

                    GoodsSkuOperationDTO skuOperationDTO = new GoodsSkuOperationDTO();
                    skuOperationDTO.setPrice(goodsImportDTOSku.getPrice());
                    skuOperationDTO.setQuantity(goodsImportDTOSku.getQuantity());
                    skuOperationDTO.setSn(goodsImportDTOSku.getSn());
                    skuOperationDTO.setWeight(goodsImportDTOSku.getWeight());
                    skuOperationDTO.setCost(goodsImportDTOSku.getCost());
                    skuOperationDTO.setSpecs(goodsImportDTOSku.getSpecs());
                    skuOperationDTO.setImages(goodsImportDTOSku.getSpecsImage());
                    skuDTOList.add(skuOperationDTO);
                }

                skuDTOList = checkMissingSpecs(specList, skuDTOList, goodsImportDTO.getSpecsImage());

                goodsOperationDTO.setSkuList(skuDTOList);

                Category parentCategory = categoryService.getCategoryById(goodsImportDTO.getCategory().getParentId());
                goodsOperationDTO.setCategoryPath(parentCategory.getParentId() + "," + parentCategory.getId() + "," + goodsImportDTO.getCategory().getId());

                // 商品参数校验
                this.checkGoodsData(goodsOperationDTO);

                //添加商品
                goodsIntegrationService.saveGoods(goodsOperationDTO);
            }
        }
    }

    private void checkGoodsData(GoodsOperationDTO goodsOperationDTO) {
        try {
            goodsOperationDTO.validateParams();
        } catch (ServiceException se) {
            throw new ServiceException(ResultCode.EMPTY_ERROR, "商品名称：" + goodsOperationDTO.getGoodsName() + "，" + se.getMessage());
        }
    }

    /**
     * 添加商品
     *
     * @param goodsImportDTOList 商品列表
     */
    private void addSupplierGoodsList(List<GoodsImportDTO> goodsImportDTOList) {
        Map<String, List<GoodsImportDTO>> collect = goodsImportDTOList.stream().collect(Collectors.groupingBy(GoodsImportDTO::getGoodsName));
        for (Map.Entry<String, List<GoodsImportDTO>> entry : collect.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                GoodsImportDTO goodsImportDTO = entry.getValue().getFirst();
                SupplierGoodsOperationDTO goodsOperationDTO = new SupplierGoodsOperationDTO(entry.getValue().getFirst());
                List<GoodsSkuOperationDTO> skuDTOList = new ArrayList<>();
                List<String> specList = new ArrayList<>();
                for (GoodsImportDTO goodsImportDTOSku : entry.getValue()) {
                    // 检查规格项是否与第一个规格项一致，如果不一致则提示错误
                    if (!skuDTOList.isEmpty()) {
                        boolean typeJSON = JSONUtil.isTypeJSON(goodsImportDTOSku.getSpecs());
                        if (!typeJSON) {
                            throw new ServiceException(ResultCode.EMPTY_ERROR, "商品名称：" + goodsOperationDTO.getGoodsName() + "，规格格式错误");
                        }
                        JSONObject specObj = JSONUtil.parseObj(goodsImportDTOSku.getSpecs());
                        JSONObject firstSpecObj = JSONUtil.parseObj(skuDTOList.getFirst().getSpecs());
                        if (!specObj.keySet().equals(firstSpecObj.keySet())) {
                            throw new ServiceException(ResultCode.GOODS_IMPORT_SPEC_ERROR);
                        }
                    }

                    specList.add(goodsImportDTOSku.getSpecs());

                    GoodsSkuOperationDTO skuOperationDTO = new GoodsSkuOperationDTO();
                    skuOperationDTO.setSpecs(goodsImportDTOSku.getSpecs());

                    skuOperationDTO.setQuantity(goodsImportDTOSku.getQuantity());
                    skuOperationDTO.setSn(goodsImportDTOSku.getSn());
                    skuOperationDTO.setWeight(goodsImportDTOSku.getWeight());
                    if (Boolean.FALSE.equals(goodsImportDTOSku.getSupportPurchase())) {
                        skuOperationDTO.setPrice(goodsImportDTOSku.getPrice());
                        skuOperationDTO.setCost(goodsImportDTOSku.getCost());
                    }
                    skuDTOList.add(skuOperationDTO);
                }

                skuDTOList = checkMissingSpecs(specList, skuDTOList, goodsImportDTO.getSpecsImage());

                goodsOperationDTO.setSkuList(skuDTOList);

                Category parentCategory = categoryService.getCategoryById(goodsImportDTO.getCategory().getParentId());
                goodsOperationDTO.setCategoryPath(parentCategory.getParentId() + "," + parentCategory.getId() + "," + goodsImportDTO.getCategory().getId());

                // 商品参数校验
                this.checkGoodsData(goodsOperationDTO);


                //添加商品
                goodsIntegrationService.saveSupplierGoods(goodsOperationDTO);
            }
        }
    }

    private List<GoodsSkuOperationDTO> checkMissingSpecs(List<String> specsList, List<GoodsSkuOperationDTO> skuList, String skuImages) {
        List<Map<String, Object>> skuSpecList = specsList.stream()
                .map(spec -> (Map<String, Object>) new HashMap<>(JSONUtil.parseObj(spec)))
                .toList();
        // 生成所有规格组合
        List<Map<String, Object>> allSpecs = generateAllSpecs(skuSpecList);
        // 查找缺失的规格
        List<Map<String, Object>> missingSpecs = findMissingSpecs(skuSpecList, allSpecs);

        // 如果有缺失的规格，则从新插入排序规格
        if (missingSpecs != null && !missingSpecs.isEmpty()) {
            List<GoodsSkuOperationDTO> skuListSorted = new ArrayList<>(allSpecs.size());

            // 获取纯sku信息

            // 重新排序sku
            for (Map<String, Object> allSpec : allSpecs) {
                Map<String, Object> spec = missingSpecs.stream()
                        .filter(missingSpec -> missingSpec.equals(allSpec))
                        .findFirst()
                        .orElse(null);
                if (spec != null) {
                    GoodsSkuOperationDTO skuOperationDTO = new GoodsSkuOperationDTO();
                    skuOperationDTO.setPrice(999999d);
                    skuOperationDTO.setQuantity(0);
                    skuOperationDTO.setCost(999999d);
                    skuOperationDTO.setSn(RandomUtil.randomNumbers(5));
                    skuOperationDTO.setWeight(999999d);
                    skuOperationDTO.setSpecs(JSONUtil.toJsonStr(spec));
                    skuOperationDTO.setImages(skuImages);
                    skuListSorted.add(skuOperationDTO);
                } else {
                    for (GoodsSkuOperationDTO skuOperationDTO : skuList) {
                        Map<String, Object> stringObjectMap = JSONUtil.parseObj(skuOperationDTO.getSpecs());
                        if (stringObjectMap.equals(JSONUtil.parseObj(allSpec))) {
                            skuListSorted.add(skuOperationDTO);
                            break;
                        }
                    }
                }
            }


            return skuListSorted;
        }
        return skuList;
    }


}
