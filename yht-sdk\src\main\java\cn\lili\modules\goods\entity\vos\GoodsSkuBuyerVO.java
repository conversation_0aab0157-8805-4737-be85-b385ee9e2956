package cn.lili.modules.goods.entity.vos;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品规格VO
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@NoArgsConstructor
public class GoodsSkuBuyerVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4820498013612909066L;

    @Schema(title = "唯一标识")
    private String id;

    @Schema(title = "商品id")
    private String goodsId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品价格")
    private Double price;

    @Schema(title = "品牌id")
    private String brandId;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "分类名称")
    private String categoryNamePath;

    @Schema(title = "计量单位")
    private String goodsUnit;

    @Schema(title = "卖点")
    private String sellingPoint;

    @Schema(title = "详情")
    private String intro;

    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "购买数量")
    private Integer buyCount;

    @Max(value = 99999999, message = "库存不能超过99999999")
    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "商品好评率")
    private Double grade;

    @Schema(title = "缩略图路径")
    private String thumbnail;

    @Schema(title = "小图路径")
    private String small;

    @Schema(title = "原图路径")
    private String original;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "评论数量")
    private Integer commentNum;

    @Schema(title = "卖家id")
    private String storeId;

    @Schema(title = "卖家名字")
    private String storeName;

    @Schema(title = "运费模板id")
    private String templateId;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "商品视频")
    private String goodsVideo;

    @Schema(title = "是否为推荐商品")
    private Boolean recommend;

    @Schema(title = "销售模式")
    private String salesModel;

    @Schema(title = "商品类型")
    private String goodsType;

    @Schema(title = "商品图片")
    private List<String> goodsGalleryList;

    @Schema(title = "促销价")
    private Double promotionPrice;

    @Schema(title = "活动库存")
    private Boolean promotionBuy;

    @Schema(title = "支持代发")
    private Boolean supportProxy;

    @Schema(title = "支持采购")
    private Boolean supportPurchase;

    @Schema(title = "供应商SKU ID")
    private String supplierSkuId;

    /**
     * @see cn.lili.modules.goods.entity.enums.PurchaseRuleEnum
     */
    @Schema(title = "采购规则")
    private String purchaseRule;

    @Schema(title = "起售数量")
    private Integer minimum;

    @Schema(title = "重量")
    private Double weight;
    /**
     * @see cn.lili.modules.goods.entity.dto.Wholesale
     */
    @Schema(title = "批发商品消费规则列表 List<Wholesale>")
    private String wholesale;


    @Schema(title = "资源包")
    private String imageZipFile;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney = false;

    @Schema(title = "是否是实拍商品")
    private Boolean isRealShoot;

    @Schema(title = "是否现货商品")
    private Boolean isInStock;

    @Schema(title = "预售截止时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preSaleDeadline;

    public GoodsSkuBuyerVO(GoodsSkuVO goodsSkuVO) {
        BeanUtils.copyProperties(goodsSkuVO, this);
    }
}
