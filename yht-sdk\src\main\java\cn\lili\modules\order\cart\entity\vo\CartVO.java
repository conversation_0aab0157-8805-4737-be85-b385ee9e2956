package cn.lili.modules.order.cart.entity.vo;

import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.distribution.entity.vos.DistributionVO;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.promotion.entity.dos.MemberCoupon;
import cn.lili.modules.promotion.entity.vos.CouponVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 购物车展示VO
 *
 * <AUTHOR>
 * @since 2020-03-24 10:33 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "购物车")
@NoArgsConstructor
public class CartVO extends CartBase implements Serializable {

    @Serial
    private static final long serialVersionUID = -5651775413457562422L;

    @Schema(title = "购物车中的产品列表")
    private List<CartSkuVO> skuList;

    @Schema(title = "sn")
    private String sn;

    @Schema(title = "购物车锁定状态，表面不能更新选中与否状态")
    private Boolean locked;

    @Schema(title = "购物车锁定原因")
    private Boolean lockedReason;

    @Schema(title = "购物车页展示时，店铺内的商品是否全选状态.1为店铺商品全选状态,0位非全选")
    private Boolean checked;

    @Schema(title = "满优惠活动")
    private FullDiscountVO fullDiscount;

    @Schema(title = "是否满优惠")
    private Boolean isFull;

    @Schema(title = "使用的优惠券列表")
    private List<MemberCoupon> couponList;

    @Schema(title = "使用的优惠券列表")
    private List<CouponVO> canReceiveCoupon;

    @Schema(title = "赠品列表")
    private List<String> giftList;

    @Schema(title = "赠送优惠券列表")
    private List<String> giftCouponList;

    @Schema(title = "赠送积分")
    private Long giftPoint;

    @Schema(title = "重量")
    private Double weight;

    @Schema(title = "购物车商品数量")
    private Integer goodsNum;

    @Schema(title = "购物车商品数量")
    private String remark;

    /**
     * @see DeliveryMethodEnum
     */
    @Schema(title = "配送方式")
    private DeliveryMethodEnum deliveryMethodEnum;

    @Schema(title = "已参与的的促销活动提示，直接展示给客户")
    private String promotionNotice;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "一级分销员")
    private DistributionVO distribution1;

    @Schema(title = "二级分销员")
    private DistributionVO distribution2;

    private List<ServiceFeeDTO> serviceFeeDTOList;

    public CartVO(CartSkuVO cartSkuVO) {
        this.setStoreId(cartSkuVO.getStoreId());
        this.setStoreName(cartSkuVO.getStoreName());
        this.setSelfOperated(cartSkuVO.getSelfOperated());
        this.setSelfPickFlag(cartSkuVO.getSelfPickFlag());
        this.setSkuList(new ArrayList<>());
        this.setCouponList(new ArrayList<>());
        this.setGiftList(new ArrayList<>());
        this.setGiftCouponList(new ArrayList<>());
        this.setCanReceiveCoupon(new ArrayList<>());
        this.setChecked(false);
        this.isFull = false;
        this.weight = 0d;
        this.giftPoint = 0L;
        this.remark = "";
        this.supplierId = cartSkuVO.getSupplierId();
        this.supplierName = cartSkuVO.getSupplierName();
    }

    public CartVO(List<CartSkuVO> cartSkuVOList, CartVO cartVO) {
        BeanUtil.copyProperties(cartVO, this);
        this.setSkuList(cartSkuVOList);
    }

    public void addGoodsNum(Integer goodsNum) {
        if (this.goodsNum == null) {
            this.goodsNum = goodsNum;
        } else {
            this.goodsNum += goodsNum;
        }
    }


    /**
     * 过滤购物车中已选择的sku
     *
     * @return
     */
    public List<CartSkuVO> getCheckedSkuList() {
        if (skuList != null && !skuList.isEmpty()) {
            return skuList.stream().filter(CartSkuVO::getChecked).toList();
        }
        return skuList;
    }

}
