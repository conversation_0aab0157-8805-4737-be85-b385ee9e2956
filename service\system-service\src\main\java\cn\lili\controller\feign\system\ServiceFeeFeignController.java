package cn.lili.controller.feign.system;

import cn.lili.modules.system.client.ServiceFeeClient;
import cn.lili.modules.system.entity.dos.ServiceFee;
import cn.lili.modules.system.service.ServiceFeeService;
import cn.lili.modules.system.service.SettingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class ServiceFeeFeignController implements ServiceFeeClient {

    private final ServiceFeeService serviceFeeService;

    @Override
    public List<ServiceFee> getServiceFeeListByIds(List<String> ids) {
        return serviceFeeService.getServiceFeeListByIds(ids);
    }

    @Override
    public ServiceFee getById (String id) {
        return serviceFeeService.getById(id);
    }

    @Override
    public List<ServiceFee> getDefaultServiceFeeList() {
        return serviceFeeService.getDefaultServiceFeeList();
    }
}
