# 商品删除时同步删除图片功能

## 功能概述

在删除商品（li_goods、li_goods_sku）时，自动删除商品相关的图片文件，包括：
- li_file 数据库记录
- OSS 存储的实际文件

但需要检查这些图片是否在其他商品中被使用，如果被使用则跳过删除。

## 实现架构

### 服务调用关系
```
goods-service (商品服务)
    ↓ 调用
resource-service (资源服务)
    ↓ 操作
li_file (文件表) + OSS (对象存储)
```

### 核心流程
1. **收集图片URL**: 从要删除的商品中收集所有图片URL
2. **检查使用情况**: 查询这些图片是否在其他商品中被使用
3. **过滤可删除文件**: 排除仍在使用的图片
4. **获取文件信息**: 根据URL获取文件的key信息
5. **执行删除**: 删除OSS文件和数据库记录

## 代码实现

### 1. UploadClient 接口扩展

**文件**: `yht-sdk/src/main/java/cn/lili/modules/file/client/UploadClient.java`

新增方法：
```java
// 根据URL获取文件信息
List<File> getFilesByUrls(List<String> urls);

// 检查文件在商品中的使用情况
List<String> checkFileUsageInGoods(List<String> fileUrls, List<String> excludeGoodsIds);

// 删除文件（数据库+OSS）
void deleteFiles(List<String> fileKeys);
```

### 2. Resource Service 实现

**文件**: `service/resource-service/src/main/java/cn/lili/controller/client/FileFeignController.java`

#### 关键实现：checkFileUsageInGoods
```java
public List<String> checkFileUsageInGoods(List<String> fileUrls, List<String> excludeGoodsIds) {
    // 构建SQL查询，检查图片在以下表和字段中的使用情况：
    // li_goods: thumbnail, small, original, image_zip_file
    // li_goods_gallery: thumbnail, small, original
    
    // 使用UNION查询所有可能的字段
    // 排除当前要删除的商品ID
    // 返回仍在使用的图片URL列表
}
```

### 3. Goods Service 调用

**文件**: `service/goods-service/src/main/java/cn/lili/modules/goods/integration/impl/GoodsIntegrationServiceImpl.java`

#### 修改 deleteGoods 方法
```java
public Boolean deleteGoods(List<String> goodsIds) {
    // ... 原有删除逻辑 ...
    
    // 新增：删除商品相关的图片文件
    this.deleteGoodsImages(goodsIds);
    
    // ... 其他逻辑 ...
}
```

#### 新增 deleteGoodsImages 方法
```java
private void deleteGoodsImages(List<String> goodsIds) {
    // 1. 收集商品的所有图片URL
    List<String> imageUrls = collectGoodsImageUrls(goodsIds);
    
    // 2. 检查图片使用情况
    List<String> usedImageUrls = uploadClient.checkFileUsageInGoods(imageUrls, goodsIds);
    
    // 3. 过滤可删除的图片
    List<String> canDeleteUrls = filterDeletableUrls(imageUrls, usedImageUrls);
    
    // 4. 获取文件信息并删除
    List<File> files = uploadClient.getFilesByUrls(canDeleteUrls);
    List<String> fileKeys = extractFileKeys(files);
    uploadClient.deleteFiles(fileKeys);
}
```

## 涉及的数据表和字段

### 商品相关表
- **li_goods**: 
  - `thumbnail` (缩略图)
  - `small` (小图)
  - `original` (原图)
  - `image_zip_file` (图片资源包)

- **li_goods_gallery**: 
  - `thumbnail` (缩略图)
  - `small` (小图)
  - `original` (原图)

### 文件相关表
- **li_file**:
  - `id` (主键)
  - `file_key` (文件key，用于OSS删除)
  - `url` (文件URL，用于匹配)

## 安全机制

### 1. 降级保护
- 当 resource-service 不可用时，UploadClientFallback 会返回所有文件都在使用
- 避免误删除仍在使用的文件

### 2. 异常处理
- 图片删除失败不影响商品删除流程
- 详细的日志记录便于问题排查

### 3. 事务处理
- OSS删除失败不影响数据库记录删除
- 确保数据一致性

## 使用示例

```java
// 删除商品时会自动触发图片删除
List<String> goodsIds = Arrays.asList("goods1", "goods2");
Boolean result = goodsIntegrationService.deleteGoods(goodsIds);
```

## 日志输出

```
INFO - 开始删除商品图片，商品ID：[goods1, goods2]
INFO - 收集到商品图片URL：[url1, url2, url3]
INFO - 检查文件在商品中的使用情况：fileUrls=[url1, url2, url3], excludeGoodsIds=[goods1, goods2]
INFO - 检查结果：被使用的文件URL：[url2]
INFO - 可以删除的图片URL：[url1, url3]
INFO - OSS文件删除成功：[key1, key3]
INFO - 文件删除完成：数据库记录=[file1, file3], OSS文件=[key1, key3]
INFO - 成功删除商品图片文件：[key1, key3]
```

## 注意事项

1. **性能考虑**: 大量商品删除时可能产生复杂的SQL查询，建议分批处理
2. **数据一致性**: 确保商品删除和图片删除的事务一致性
3. **监控告警**: 建议对文件删除失败进行监控和告警
4. **备份策略**: 重要文件建议先备份再删除

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加新的图片字段检查
- 可以扩展到其他业务场景（如店铺删除、分类删除等）
- 支持不同的存储后端（OSS、MinIO等）
