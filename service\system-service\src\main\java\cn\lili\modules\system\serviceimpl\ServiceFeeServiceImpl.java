package cn.lili.modules.system.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.SwitchEnum;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.logistics.LogisticsPluginFactory;
import cn.lili.modules.logistics.entity.dto.LabelOrderDTO;
import cn.lili.modules.logistics.entity.enums.LogisticsEnum;
import cn.lili.modules.order.order.client.AfterSaleClient;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.client.OrderItemClient;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.enums.DeliverStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.store.entity.dos.StoreLogistics;
import cn.lili.modules.system.entity.dos.*;
import cn.lili.modules.system.entity.dto.LogisticsSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.entity.vo.RegionVO;
import cn.lili.modules.system.entity.vo.ServiceFeeVO;
import cn.lili.modules.system.entity.vo.Traces;
import cn.lili.modules.system.mapper.LogisticsMapper;
import cn.lili.modules.system.mapper.ServiceFeeMapper;
import cn.lili.modules.system.service.LogisticsService;
import cn.lili.modules.system.service.ServiceFeeService;
import cn.lili.modules.system.service.SettingService;
import cn.lili.modules.system.service.StoreLogisticsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 服务费业务层实现
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServiceFeeServiceImpl extends ServiceImpl<ServiceFeeMapper, ServiceFee> implements ServiceFeeService {


    @Override
    public List<ServiceFeeVO> getOpenList() {

        LambdaQueryWrapper<ServiceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceFee::getDeleteFlag, false);
        List<ServiceFee> list = this.list(queryWrapper);

        return dataTree(list);
    }

    private List<ServiceFeeVO> dataTree(List<ServiceFee> dataList) {
        List<ServiceFeeVO> feeVOS = new ArrayList<>();
        dataList.stream().filter(item -> item.getParentId().equals("0")).forEach(item -> {
            feeVOS.add(new ServiceFeeVO(item,1));
        });
        dataList.stream().filter(item -> !(item.getParentId().equals("0"))).forEach(item -> {
            for (ServiceFeeVO fee : feeVOS) {
                if (fee.getId().equals(item.getParentId())) {
                    fee.getChildren().add(new ServiceFeeVO(item,2));
                }
            }
        });
        return feeVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdateFee(ServiceFee serviceFee) {
        LambdaQueryWrapper<ServiceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceFee::getName, serviceFee.getName());
        queryWrapper.ne(CharSequenceUtil.isNotEmpty(serviceFee.getId()), ServiceFee::getId, serviceFee.getId());
        if (this.getOne(queryWrapper) != null) {
            throw new ServiceException(ResultCode.SERVICE_FEE_EXIST_ERROR);
        }
        return this.saveOrUpdate(serviceFee);
    }

    public List<ServiceFee> getServiceFeeListByIds(List<String> ids) {
        LambdaQueryWrapper<ServiceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceFee::getDeleteFlag, false);
        queryWrapper.in(ServiceFee::getId, ids);
        return this.list(queryWrapper);
    }

    public List<ServiceFee> getDefaultServiceFeeList() {
        List<ServiceFee> resultList = new ArrayList<>();
        // 获取子级价格默认为0的和没有子级的服务费
        LambdaQueryWrapper<ServiceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceFee::getDeleteFlag, false);
        // 获取所有的服务费
        List<ServiceFee> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            for (ServiceFee serviceFee : list) {
                if (serviceFee.getParentId().equals("0")) {
                    List<ServiceFee> sfList = list.stream().filter(sf -> serviceFee.getId().equals(sf.getParentId())).toList();
                    if (CollectionUtils.isEmpty(sfList)) {
                        resultList.add(serviceFee);
                    }
                }else {
                    if (serviceFee.getPrice() == 0) {
                        resultList.add(serviceFee);
                    }
                }
            }
        }
        return resultList;
    }
}
