package cn.lili.modules.store.entity.dos;

import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 商家标签
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@TableName("li_store_tag")
@Schema(title = "商家标签")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StoreTag extends BaseStandardEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "标签名称不能为空")
    @Schema(title = "标签名称")
    private String tagName;

    @Schema(title = "标签描述")
    private String tagDescription;

    @Schema(title = "标签颜色")
    private String tagColor;

    @Schema(title = "标签图标")
    private String tagIcon;

    @NotNull(message = "排序不能为空")
    @Schema(title = "排序")
    private BigDecimal sortOrder;

    @Schema(title = "是否启用")
    private Boolean enabled = true;

    @Schema(title = "标签类型", description = "QUALITY-优质商家, RECOMMEND-推荐商家, HOT-热门商家, NEW-新商家")
    private String tagType;

    @Schema(title = "备注")
    private String remark;

    public StoreTag(String tagName, String tagDescription) {
        this.tagName = tagName;
        this.tagDescription = tagDescription;
        this.enabled = true;
        this.sortOrder = BigDecimal.ZERO;
    }
}
