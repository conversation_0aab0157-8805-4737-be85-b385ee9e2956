package cn.lili.modules.statistics.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.statistics.client.PlatformViewClient;
import cn.lili.modules.statistics.entity.dos.PlatformViewData;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 **/
public class PlatformViewFallback implements PlatformViewClient {


    /**
     * 批量保存
     *
     * @param platformViewDataList 平台pv统计集合
     * @return 是否操作成功
     */
    @Override
    public boolean saveBatch(List<PlatformViewData> platformViewDataList) {
        throw new ServiceException();
    }

    @Override
    public Page<StoreRankStatisticsVO> getStoreStatistics(StoreRankStatisticsParams params) {
        throw new ServiceException();
    }
}
