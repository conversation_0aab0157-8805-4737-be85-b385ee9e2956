package cn.lili.modules.store.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商家标签DTO
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreTagDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "标签ID", description = "编辑时必填")
    private String id;

    @NotBlank(message = "标签名称不能为空")
    @Schema(title = "标签名称")
    private String tagName;

    @Schema(title = "标签描述")
    private String tagDescription;

    @Schema(title = "标签颜色", description = "十六进制颜色值，如：#ff0000")
    private String tagColor;

    @Schema(title = "标签图标")
    private String tagIcon;

    @NotNull(message = "排序不能为空")
    @Schema(title = "排序", description = "数值越小排序越靠前")
    private BigDecimal sortOrder;

    @Schema(title = "是否启用")
    private Boolean enabled = true;

    @Schema(title = "标签类型", description = "QUALITY-优质商家, RECOMMEND-推荐商家, HOT-热门商家, NEW-新商家, REAL_PHOTO-实拍商家, FAST_DELIVERY-发货快商家")
    private String tagType;

    @Schema(title = "备注")
    private String remark;

    /**
     * 验证参数
     */
    public void validateParams() {
        if (tagName != null) {
            tagName = tagName.trim();
        }
        if (tagDescription != null) {
            tagDescription = tagDescription.trim();
        }
        if (remark != null) {
            remark = remark.trim();
        }
        
        // 设置默认值
        if (enabled == null) {
            enabled = true;
        }
        if (sortOrder == null) {
            sortOrder = BigDecimal.ZERO;
        }
    }
}
