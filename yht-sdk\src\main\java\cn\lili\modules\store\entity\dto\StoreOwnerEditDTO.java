package cn.lili.modules.store.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import cn.lili.common.utils.ValidateParamsUtil;
import lombok.Data;

/**
 * 店铺修改DTO
 *
 * <AUTHOR>
 * @since 2020-08-22 15:10:51
 */
@Data
public class StoreOwnerEditDTO {


    @Size(min = 2, max = 20, message = "店铺名称需要在2-20个字符以内")
    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "店铺logo")
    private String storeLogo = "https://huiyifang-test.oss-cn-shenzhen.aliyuncs.com/yht.png";

    @Size(min = 6, max = 200, message = "店铺简介需在6-200字符之间")
    @Schema(title = "店铺简介")
    private String storeDesc;

    @Schema(title = "经纬度")
    private String storeCenter;

    @Schema(title = "库存预警数量")
    private Integer stockWarning;

    @Schema(title = "开启楼层装修")
    private Boolean enableDecoration = false;

    @Schema(title = "开启自提")
    private Boolean enablePickup = false;

    @Schema(title = "icp")
    private String icp;

    @Schema(title = "公司名称")
    private String companyName;

    @Schema(title = "代理利率")
    private Integer agencyRate;

    @Schema(title = "qq")
    private String storeQq;

    @Schema(title = "微信")
    private String storeWx;

    @Schema(title = "旺旺号")
    private String storeWw;

    @Schema(title = "联系方式")
    private String storePhone;

    @Schema(title = "直营店铺-是否允许购买商品")
    private Boolean isBuyGoods;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney;

    @Schema(title = "是否直营")
    private Boolean selfOperated;

    @Schema(title = "省")
    private String province;

    @Schema(title = "市")
    private String city;

    @Schema(title = "区")
    private String district;

    @Schema(title = "市场")
    private String market;

    @Schema(title = "市场详细地址")
    private String marketDetail;

    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(storeName, 2, 20)) {
            ValidateParamsUtil.throwInvalidParamError("店铺名称长度为2-20个字符");
        }
        if (!ValidateParamsUtil.isValidString(storeDesc, 6, 200)) {
            ValidateParamsUtil.throwInvalidParamError("店铺简介需在6-200字符之间");
        }
        if (!CharSequenceUtil.isEmpty(companyName)) {
            ValidateParamsUtil.throwInvalidParamError("请填写公司名称");
        }
        if (!CharSequenceUtil.isEmpty(icp)) {
            ValidateParamsUtil.throwInvalidParamError("请填写icp备案号");
        }
        return true;
    }

}
