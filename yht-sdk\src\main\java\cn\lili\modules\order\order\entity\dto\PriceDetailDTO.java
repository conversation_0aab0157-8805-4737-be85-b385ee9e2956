package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.annotation.PropIgnore;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.modules.distribution.entity.enums.DistributionModelEnum;
import cn.lili.modules.promotion.entity.vos.PromotionSkuVO;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 商城流水，细节到orderItem
 *
 * <AUTHOR>
 * @since 2020/11/17 7:25 下午
 */
@Data
@NoArgsConstructor
@Slf4j
public class PriceDetailDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8808470688518188146L;


    //===============================================payment=================================================
    //流水金额（实际支付金额）
    @Schema(title = "流水金额(入账 出帐金额) = goodsPrice + freight - discountPrice - couponPrice + updatePrice")
    private Double flowPrice = 0D;


    //支付积分
    @Schema(title = "支付积分")
    private Long payPoint = 0L;


    //============================================end payment================================================


    //============================================update price================================================

    //如果修改订单金额，则需要对订单信息进行重新计算
    @Schema(title = "订单修改金额")
    private Double updatePrice = 0D;

    //============================================end update price================================================

    //============================================base================================================
    // 订单原始总价格=商品总金额（商品原价）+配送费

    @Schema(title = "订单原始总价格 默认等于流水金额 ")
    private Double originalPrice = 0D;

    @Schema(title = "商品总金额（商品原价）")
    private Double goodsPrice = 0D;
    @Schema(title = "优惠活动金额")
    private Double discountPrice = 0D;

    @Schema(title = "优惠券金额")
    private Double couponPrice = 0D;

    @Schema(title = "店铺配送费")
    private Double freightPrice = 0D;

    //============================================end base================================================


    //===========================================supplier========================================================
    @Schema(title = "供应商商品总价")
    private Double supplierGoodsPrice = 0D;

    @Schema(title = "供应商订单金额=供应商商品总价+供应商物流费用")
    private Double supplierOrderPrice = 0D;

    @Schema(title = "平台收取交易佣金比例")
    private Double supplierCommissionPoint = 0D;

    @Schema(title = "平台收取供应商交易佣金金额=(supplierGoodsPrice(供应商商品总价) * platFormCommissionPoint(平台佣金比例))/100")
    private Double supplierCommissionPrice = 0D;

    @Schema(title = "供应商配送费")
    private Double supplierFreightPrice = 0D;


    @Schema(title = "供应商结算金额 = 供应商商品总价(supplierGoodsPrice)-供应商佣金计算(supplierCommissionPrice)+供应商配送费" +
        "(supplierFreightPrice)")
    private Double supplierSettlementPrice = 0D;
    //=======================================end supplier====================================================

    //=============================================seller=========================================================

    @Schema(title = "商家盈余金额 = 实际支付金额(flowPrice)-供应订单金额")
    private Double surplusAmountPrice = 0D;

    @Schema(title = "店铺交易佣金比例")
    private Double storeCommissionPoint = 0D;


    @Schema(title = "营销列表 或涉及店铺承担部分运营成本")
    private List<SiteMarketingCost> siteMarketingCosts = new ArrayList<>();


    //SiteMarketingCost.countStoreMarketingCost(营销列表) 平台补贴营销费用
    @Schema(title = "平台级别活动，店铺营销费用统计，用作补贴")
    private Double storeMarketingCost = 0D;

    @Schema(title = "平台收取店铺交易佣金金额=surplusAmountPrice(店铺盈余金额) * storeCommissionPoint(平台佣金比例)/100")
    private Double storeCommissionPrice = 0D;

    //=========distribution2.0 新增==========
    /**
     * @see cn.lili.modules.distribution.entity.enums.DistributionModelEnum
     */
    @Schema(title = "分销佣金模式(平台承担/商家承担)")
    private String commissionModel = DistributionModelEnum.SELLER.name();

    @Schema(title = "一级分销返佣比例")
    private Double firstCommissionRate = 0D;

    @Schema(title = "一级分销返现支出")
    private Double firstCommission = 0D;

    @Schema(title = "二级分销返佣比例")
    private Double secondaryCommissionRate = 0D;

    @Schema(title = "二级分销返佣支出")
    private Double secondaryCommission = 0D;

    @Schema(title = "分销商返现支出 = firstCommission(一级分销返现支出) + secondaryCommission(二级分销返佣支出)")
    private Double distributorSettlementPrice = 0D;


    //=========end distribution==========

    @Schema(title = "商家结算金额=" +
        "店铺盈余费用(surplusAmountPrice)" +
        "+店铺营销费用(storeMarketingCost)" +
        "-平台收取店铺交易佣金金额(storeCommissionPrice)" +
        "-分销商返现支出(distributorSettlementPrice)" +
        "+店铺配送费(freightPrice)")
    private Double sellerSettlementPrice = 0D;
    //============================================end seller=====================================================


    //============================================profitsharing================================================

    //flowPrice(实际支付金额)
    //          =distributorSettlementPrice(分销商返现支出)
    //          +sellerSettlementPrice(商家结算金额)
    //          +supplierSettlementPrice(供应商结算金额)
    //          +platformSettlementPrice(平台结算金额)


    @Schema(title = "平台盈利金额=实际支付金额(flowPrice)-商家结算金额(sellerSettlementPrice)-供应商结算金额(supplierSettlementPrice)")
    private Double platformSettlementPrice = 0D;


    private Boolean specialOrder = false;

    //============================================end profitsharing================================================


    /**
     * 参与的促销活动
     */
    @PropIgnore
    @Schema(title = "参与的促销活动")
    private List<PromotionSkuVO> joinPromotion = new ArrayList<>();

    @Schema(title = "会员权益优惠折扣")
    private Double memberBenefitsDiscount = 0D;


    @Schema(title = "会员权益优惠折扣价格")
    private Double memberBenefitsDiscountPrice = 0D;

    @Schema(title = "服务费集合")
    private List<ServiceFeeDTO> serviceFeeDTOList = new ArrayList<>();

    @Schema(title = "服务费合计")
    private Double serviceFee = 0D;

    /**
     * 通过json获取模型
     *
     * @param json json
     * @return PriceDetailDTO
     */
    public static PriceDetailDTO getModel(String json) {
        if (CharSequenceUtil.isEmpty(json)) {
            return null;
        }
        try {
            return GsonUtils.fromJson(json, PriceDetailDTO.class);
        } catch (Exception e) {

            return null;
        }
    }

    /**
     * 写入修改金额，自动计算订单各个金额
     *
     * @param updatePrice 修改后的订单金额
     */
    public void resetUpdatePrice(Double updatePrice) {
        this.updatePrice = updatePrice;

        this.recount();
    }

    /**
     * 全部重新计算
     */
    public void recount() {
        //流水金额(入账 出帐金额) = "流水金额(入账 出帐金额) = goodsPrice + freight - discountPrice - couponPrice + updatePrice"
        this.flowPrice =
            CurrencyUtil.sub(CurrencyUtil.add(goodsPrice, freightPrice), CurrencyUtil.add(discountPrice, couponPrice));
        //如果商家修改订单金额，则订单进行价格重新计算
        if (updatePrice != 0) {
            flowPrice = CurrencyUtil.add(flowPrice, updatePrice);
        } else {
            //如果订单没有修改价格，则流水金额赋值给原始订单金额
            originalPrice = flowPrice;
        }

        // 会员权益优惠折扣
        if (memberBenefitsDiscount != null && memberBenefitsDiscount > 0) {
            Double currentPrice = CurrencyUtil.mul(flowPrice, memberBenefitsDiscount);
            memberBenefitsDiscountPrice = CurrencyUtil.sub(flowPrice, currentPrice);
            flowPrice = currentPrice;
        }

        //计算服务费
        if (CollectionUtils.isNotEmpty(serviceFeeDTOList)) {
            Double reduce = serviceFeeDTOList.stream().map(ServiceFeeDTO::getTotalPrice).reduce(Double::sum).orElse(0D);
            flowPrice = CurrencyUtil.add(flowPrice, reduce);
        }

        //计算供应商结算金额
        recountSupplier();
        //计算商家结算金额
        recountSeller();
        //计算平台结算金额
        recountPlatform();
        // 重新计算分销佣金
        recountDistribution();
    }


    public Double realGoodsPrice() {
        Double realGoodsPrice = CurrencyUtil.sub(goodsPrice, discountPrice, couponPrice);
        if (realGoodsPrice < 0) {
            return 0D;
        }
        return realGoodsPrice;
    }
    /**
     * 重新计算分销佣金
     */
    private void recountDistribution() {
        this.firstCommission = CurrencyUtil.div(
            CurrencyUtil.mul(realGoodsPrice(), firstCommissionRate), 100);
        this.secondaryCommission = CurrencyUtil.div(
            CurrencyUtil.mul(realGoodsPrice(), secondaryCommissionRate), 100);
        this.distributorSettlementPrice = CurrencyUtil.add(firstCommission, secondaryCommission);
        // 如果店铺承担分销成本，店铺结算金额需要减去分销金额
        if (DistributionModelEnum.SELLER.name().equals(commissionModel)) {
            sellerSettlementPrice = CurrencyUtil.sub(sellerSettlementPrice, distributorSettlementPrice);
        }// -分销商结算金额(distributorSettlementPrice)     //需要考虑分销佣金承担模式，如果是商家承担则不需要计算
        else if (DistributionModelEnum.PLATFORM.name().equals(commissionModel)) {
            platformSettlementPrice = CurrencyUtil.sub(platformSettlementPrice, distributorSettlementPrice);
        }
    }
    private void recountSeller() {
        // 特殊订单，店铺不进行再次结算
        if (Boolean.TRUE.equals(specialOrder)) {
            if (supplierSettlementPrice != null && supplierSettlementPrice > 0) {
                sellerSettlementPrice = CurrencyUtil.sub(sellerSettlementPrice, supplierSettlementPrice);
            }
            return;
        }
        //商家盈余金额 = 实际支付金额(flowPrice)-供应订单金额
        surplusAmountPrice = CurrencyUtil.sub(flowPrice, supplierOrderPrice);

        //店铺营销费用统计
        storeMarketingCost = SiteMarketingCost.countStoreMarketingCost(siteMarketingCosts);

        //平台收取店铺交易佣金金额=surplusAmountPrice(店铺盈余金额) * storeCommissionPoint(平台佣金比例)/100

        //需额外考虑极端情况，盈余金额小于0 的情况
        if (surplusAmountPrice <= 0) {
            storeCommissionPrice = 0D;
        } else {
            storeCommissionPrice =
                CurrencyUtil.mul(surplusAmountPrice, storeCommissionPoint);
        }
        //商家结算金额="店铺盈余费用(surplusAmountPrice)" +
        //          "-平台收取店铺交易佣金金额(storeCommissionPrice)" +
        //          "+店铺营销费用(storeMarketingCost)"
        sellerSettlementPrice =
            CurrencyUtil.add(CurrencyUtil.sub(surplusAmountPrice, storeCommissionPrice), storeMarketingCost);

    }

    private void recountSupplier() {
        //供应商商品总价 = 供应商商品总价(supplierGoodsPrice)+供应商配送费(supplierFreightPrice)
        supplierOrderPrice = CurrencyUtil.add(supplierGoodsPrice, supplierFreightPrice);
        //供应商佣金计算
        supplierCommissionPrice = CurrencyUtil.mul(supplierOrderPrice, supplierCommissionPoint);
        //供应商结算金额 = 供应商订单金额(supplierOrderPrice)-供应商佣金计算(supplierCommissionPrice)
        supplierSettlementPrice = CurrencyUtil.sub(supplierOrderPrice, supplierCommissionPrice);
    }

    private void recountPlatform() {
        //平台盈利金额=实际支付金额(flowPrice)-商家结算金额(sellerSettlementPrice)-供应商结算金额(supplierSettlementPrice)-供应商实际分得金额
        platformSettlementPrice = CurrencyUtil.sub(flowPrice, sellerSettlementPrice, supplierSettlementPrice);
    }

    /**
     * 累加金额
     */
    public void increase(PriceDetailDTO priceDetailDTO) {
        //自我累加
        this.goodsPrice = CurrencyUtil.add(this.goodsPrice, priceDetailDTO.getGoodsPrice());
        this.freightPrice = CurrencyUtil.add(this.freightPrice, priceDetailDTO.getFreightPrice());
        this.discountPrice = CurrencyUtil.add(this.discountPrice, priceDetailDTO.getDiscountPrice());
        this.couponPrice = CurrencyUtil.add(this.couponPrice, priceDetailDTO.getCouponPrice());
        this.updatePrice = CurrencyUtil.add(this.updatePrice, priceDetailDTO.getUpdatePrice());
        this.originalPrice = CurrencyUtil.add(this.originalPrice, priceDetailDTO.getOriginalPrice());
        this.flowPrice = CurrencyUtil.add(this.flowPrice, priceDetailDTO.getFlowPrice());
        this.memberBenefitsDiscountPrice = CurrencyUtil.add(this.memberBenefitsDiscountPrice, priceDetailDTO.getMemberBenefitsDiscountPrice());
        this.memberBenefitsDiscount = priceDetailDTO.getMemberBenefitsDiscount();

        this.surplusAmountPrice = CurrencyUtil.add(this.surplusAmountPrice, priceDetailDTO.getSurplusAmountPrice());
        this.storeMarketingCost = CurrencyUtil.add(this.storeMarketingCost, priceDetailDTO.getStoreMarketingCost());
        this.storeCommissionPrice = CurrencyUtil.add(this.storeCommissionPrice,
            priceDetailDTO.getStoreCommissionPrice());
        this.sellerSettlementPrice = CurrencyUtil.add(this.sellerSettlementPrice,
            priceDetailDTO.getSellerSettlementPrice());
        this.supplierOrderPrice = CurrencyUtil.add(this.supplierOrderPrice, priceDetailDTO.getSupplierOrderPrice());
        this.supplierGoodsPrice = CurrencyUtil.add(this.supplierGoodsPrice, priceDetailDTO.getSupplierGoodsPrice());
        this.supplierCommissionPrice = CurrencyUtil.add(this.supplierCommissionPrice,
            priceDetailDTO.getSupplierCommissionPrice());
        this.supplierFreightPrice = CurrencyUtil.add(this.supplierFreightPrice,
            priceDetailDTO.getSupplierFreightPrice());
        this.supplierSettlementPrice = CurrencyUtil.add(this.supplierSettlementPrice,
            priceDetailDTO.getSupplierSettlementPrice());
        this.platformSettlementPrice = CurrencyUtil.add(this.platformSettlementPrice,
            priceDetailDTO.getPlatformSettlementPrice());
        this.distributorSettlementPrice = CurrencyUtil.add(this.distributorSettlementPrice,
            priceDetailDTO.getDistributorSettlementPrice());
        this.firstCommission = CurrencyUtil.add(this.firstCommission, priceDetailDTO.getFirstCommission());
        this.commissionModel = priceDetailDTO.getCommissionModel();
        this.secondaryCommission = CurrencyUtil.add(this.secondaryCommission, priceDetailDTO.getSecondaryCommission());

        this.firstCommissionRate = priceDetailDTO.getFirstCommissionRate();
        this.secondaryCommissionRate = priceDetailDTO.getSecondaryCommissionRate();

        this.payPoint = this.payPoint + priceDetailDTO.getPayPoint();
    }

    /**
     * 批量累加
     *
     * @param priceDetailDTOS 累加对象
     */
    public void accumulationPriceDTO(List<PriceDetailDTO> priceDetailDTOS) {
        for (PriceDetailDTO price : priceDetailDTOS) {
            this.increase(price);
        }
    }

    public void addSiteMarketingCost(SiteMarketingCost siteMarketingCost) {
        if (siteMarketingCosts == null) {
            siteMarketingCosts = new ArrayList<>();
        }
        siteMarketingCosts.add(siteMarketingCost);
    }

    public void resetDiscountPrice(Double discountPrice) {
        this.discountPrice = discountPrice;
        promotionPriceHandler();
    }

    public void resetCouponPrice(Double couponPrice) {
        this.couponPrice = couponPrice;
        promotionPriceHandler();
    }

    /**
     * 如果促销金额+优惠券金额大于商品金额问题处理
     */
    private void promotionPriceHandler() {
        if (couponPrice == null || discountPrice == null) {
            return;
        }
        //如果订单优惠总额>商品金额，则处理一下数据，使得两数相加<=商品金额
        if (Math.abs(CurrencyUtil.add(couponPrice, discountPrice)) > goodsPrice) {
            couponPrice = CurrencyUtil.sub(goodsPrice, discountPrice);
            this.resetCouponPrice(couponPrice);
        }
    }

    /**
     * 通过json获取模型
     */
    @PropIgnore
    public String toJson() {
        return GsonUtils.toJson(this);
    }


    public Double getSupplierSettlementPrice() {
        if (supplierSettlementPrice == null) {
            return 0.0;
        }
        return supplierSettlementPrice;
    }

    public Double getDistributorSettlementPrice() {
        if (distributorSettlementPrice == null) {
            return 0.0;
        }
        return distributorSettlementPrice;
    }

    public Double getSellerSettlementPrice() {
        if (sellerSettlementPrice == null) {
            return 0.0;
        }
        return sellerSettlementPrice;
    }

    public Double getPlatformSettlementPrice() {
        if (platformSettlementPrice == null) {
            return 0.0;
        }
        return platformSettlementPrice;
    }

    public Double getFirstCommission() {
        if (firstCommission == null || firstCommission <= 0) {
            return 0D;
        }
        return firstCommission;
    }

    public Double getSecondaryCommission() {
        if (secondaryCommission == null || secondaryCommission <= 0) {
            return 0D;
        }
        return secondaryCommission;
    }
    public String getCommissionModel() {
        if (commissionModel == null) {
            return DistributionModelEnum.SELLER.name();
        }
        return DistributionModelEnum.PLATFORM.name();
    }

    public void setSpecialOrderSettlementPrice(Double sellerSettlementPrice) {
        this.setSpecialOrder(true);
        this.setSellerSettlementPrice(sellerSettlementPrice);
    }

    public void minPriceHandler() {
        this.flowPrice = 0.01;
        //计算商家结算金额
        recountSeller();
        //计算平台结算金额
        recountPlatform();
    }

}
