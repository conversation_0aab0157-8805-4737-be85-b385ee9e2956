package cn.lili.modules.system.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.entity.params.StoreMarketSearchParams;
import cn.lili.modules.system.entity.vo.StoreMarketVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商家市场业务层
 *
 * <AUTHOR>
 * @since 2025-7-27
 */
public interface StoreMarketService extends IService<StoreMarket> {


    /**
     * 获取已开启的服务费列表
     *
     * @return 服务费列表
     */
    Page<StoreMarketVO> getOpenList(StoreMarketSearchParams storeMarketSearchParams);


    boolean addOrUpdate(StoreMarket storeMarket);


    List<StoreMarket> listAll(String city);
}