package cn.lili.modules.goods.service;


import cn.lili.modules.goods.entity.dos.Brand;
import cn.lili.modules.goods.entity.dos.GoodsParams;
import cn.lili.modules.goods.entity.dto.BrandPageDTO;
import cn.lili.modules.goods.entity.dto.GoodsParamsSearchParams;
import cn.lili.modules.goods.entity.vos.BrandVO;
import cn.lili.modules.goods.entity.vos.GoodsPageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 商品参数业务层
 *
 * <AUTHOR>
 */
public interface GoodsParamsService extends IService<GoodsParams> {

    /**
     * 删除参数
     *
     * @param ids 品牌id
     */
    void deleteParams(List<String> ids);



    /**
     * 添加参数
     *
     * @param goodsParams 商品参数信息
     * @return 添加结果
     */
    boolean addParams(GoodsParams goodsParams);

    /**
     * 更新品牌
     *
     * @param goodsParams 商品参数信息
     * @return 更新结果
     */
    boolean updateParams(GoodsParams goodsParams);


    List<GoodsParams> getList(GoodsParamsSearchParams searchParams);
}