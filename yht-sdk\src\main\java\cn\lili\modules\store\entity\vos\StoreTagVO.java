package cn.lili.modules.store.entity.vos;

import cn.lili.modules.store.entity.dos.StoreTag;
import cn.lili.modules.store.entity.enums.StoreTagTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家标签VO
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@NoArgsConstructor
public class StoreTagVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "标签ID")
    private String id;

    @Schema(title = "标签名称")
    private String tagName;

    @Schema(title = "标签描述")
    private String tagDescription;

    @Schema(title = "标签颜色")
    private String tagColor;

    @Schema(title = "标签图标")
    private String tagIcon;

    @Schema(title = "排序")
    private BigDecimal sortOrder;

    @Schema(title = "是否启用")
    private Boolean enabled;

    @Schema(title = "标签类型")
    private String tagType;

    @Schema(title = "标签类型名称")
    private String tagTypeName;

    @Schema(title = "备注")
    private String remark;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "创建时间")
    private Date createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "使用该标签的店铺数量")
    private Long storeCount;

    public StoreTagVO(StoreTag storeTag) {
        if (storeTag != null) {
            BeanUtils.copyProperties(storeTag, this);
            
            // 设置标签类型名称
            if (storeTag.getTagType() != null) {
                StoreTagTypeEnum tagTypeEnum = StoreTagTypeEnum.getByCode(storeTag.getTagType());
                if (tagTypeEnum != null) {
                    this.tagTypeName = tagTypeEnum.getName();
                }
            }
        }
    }
}
