package cn.lili.modules.order.order.mapper;

import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dto.OrderExportDTO;
import cn.lili.modules.order.order.entity.dto.OrderItemExportDTO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.payment.entity.dos.PaymentLog;
import cn.lili.modules.statistics.entity.dos.PlatformViewData;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 订单数据处理层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:35 下午
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 修改订单状态
     *
     * @param status  状态
     * @param orderSn 订单编号
     */
    @Update({"update li_order set order_status = #{status} where sn = #{orderSn}"})
    void updateStatus(String status, String orderSn);

    /**
     * 查询订单简短信息分页
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 简短订单分页
     */
    @Select("select o.sn,o.trade_sn,o.flow_price,o.create_time,o.order_status,o.pay_status,o.payment_method,o.payment_time,o.buyer_id,o.logistics_no, " +
        "o.nickname,o.store_name,o.store_id,o.client_type,o.order_type,o.deliver_status," +
        "o.order_promotion_type,o.seller_remark,o.is_proxy,o.is_paid_to_supplier,o.full_refund,o.after_sale_applying " +
        ",GROUP_CONCAT(oi.goods_id) as group_goods_id" +
        ",GROUP_CONCAT(oi.sku_id) as group_sku_id" +
        ",GROUP_CONCAT(oi.num) as group_num" +
        ",GROUP_CONCAT(oi.image) as group_images" +
        ",GROUP_CONCAT(oi.goods_name) as group_name " +
        ",GROUP_CONCAT(oi.specs) as group_specs " +
        ",GROUP_CONCAT(oi.after_sale_status) as group_after_sale_status" +
        ",GROUP_CONCAT(oi.complain_status) as group_complain_status" +
        ",GROUP_CONCAT(oi.comment_status) as group_comment_status" +
        ",GROUP_CONCAT(oi.sn) as group_order_items_sn " +
        ",GROUP_CONCAT(oi.goods_price) as group_goods_price " +
        " FROM li_order o INNER JOIN li_order_item AS oi on o.sn = oi.order_sn ${ew.customSqlSegment} ")
    Page<OrderSimpleVO> queryByParams(Page<OrderSimpleVO> page, @Param(Constants.WRAPPER) Wrapper<OrderSimpleVO> queryWrapper);

    /**
     * 查询订单支付记录
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 订单支付记录分页
     */
    @Select("select o.* from li_order o ${ew.customSqlSegment} ")
    Page<PaymentLog> queryPaymentLogs(Page<PaymentLog> page, @Param(Constants.WRAPPER) Wrapper<PaymentLog> queryWrapper);

    /**
     * 查询订单信息
     *
     * @param queryWrapper 查询条件
     * @return 简短订单分页
     */
    @Select("select o.* " +
            " FROM li_order o INNER JOIN li_order_item AS oi on o.sn = oi.order_sn ${ew.customSqlSegment} ")
    List<Order> queryListByParams(@Param(Constants.WRAPPER) Wrapper<Order> queryWrapper);

    /**
     * 查询导出订单货物DTO
     * @param queryWrapper
     * @return
     */
    @Select("SELECT o.sn,o.full_refund, oi.sn as order_item_sn, oi.goods_name, oi.goods_price, oi.num, oi.goods_id, oi.unit_price, oi.flow_price, oi.price_detail, o.payment_method, o.consignee_name, o.consignee_mobile, o.consignee_address_path, " +
            "o.consignee_detail, o.remark, o.create_time, o.payment_time, o.client_type, o.order_status, o.order_type, af.service_status as after_sale_status, o.cancel_reason, o.logistics_time, o.complete_time, " +
            "o.store_name, o.supplier_name, o.is_proxy " +
            "FROM li_order o LEFT JOIN li_order_item oi ON oi.order_sn=o.sn " +
            "LEFT JOIN li_after_sale af ON af.order_item_sn = oi.sn ${ew.customSqlSegment}")
    List<OrderItemExportDTO> queryExportOrderItem(@Param(Constants.WRAPPER) Wrapper<OrderItem> queryWrapper);

    /**
     * 分组统计每个店铺的PV和UV数量
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 店铺流量统计分页数据
     */
    @Select("SELECT " +
            "lo.store_id as storeId, " +
            "SUM(lo.flow_price) as saleAmount " +
            "FROM li_order lo " +
            "${ew.customSqlSegment}")
    Page<StoreRankStatisticsVO> getSaleAmountByStore(Page<Order> page,
                                                   @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    /**
     * 获取店铺24小时内快速发货订单数量
     *
     * @param storeId 店铺ID
     * @return 24小时内快速发货订单数量
     */
    @Select("SELECT COUNT(*) FROM li_order " +
            "WHERE store_id = #{storeId} " +
            "AND deliver_status = 'DELIVERED' " +
            "AND payment_time IS NOT NULL " +
            "AND logistics_time IS NOT NULL " +
            "AND TIMESTAMPDIFF(HOUR, payment_time, logistics_time) <= 24 " +
            "AND TIMESTAMPDIFF(HOUR, payment_time, logistics_time) >= 0")
    Long getQuickDeliveryOrderCount(@Param("storeId") String storeId);

}